<div class="employee-management-dashboard">
  <!-- Header -->
  <div class="dashboard-header">
    <h2 class="dashboard-title">
      {{ selectedEmployeeId ? 'Employee Management' : 'Employee Management Dashboard' }}
    </h2>
    <div class="dashboard-actions">
      @if (activeSection !== 'overview') {
        <app-button
          [variant]="'primary'"
          [size]="'md'"
          [icon]="'add'"
          (click)="onAddNew()">
          Add {{ getSectionLabel(activeSection) }}
        </app-button>
      }
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="section-tabs">
    @for (section of sections; track section.key) {
      <button
        class="section-tab"
        [class.active]="activeSection === section.key"
        (click)="setActiveSection(section.key)">
        <span class="material-icons">{{ section.icon }}</span>
        <span class="tab-label">{{ section.label }}</span>
      </button>
    }
  </div>

  <!-- Content Area -->
  <div class="dashboard-content">
    <!-- Overview Section -->
    @if (activeSection === 'overview') {
      <div class="overview-section">
        @if (!selectedEmployeeId) {
          <!-- Global Statistics -->
          <div class="statistics-grid">
            @if (statisticsLoading()) {
              <div class="loading-state">
                <span class="material-icons">hourglass_empty</span>
                <p>Loading statistics...</p>
              </div>
            } @else if (statistics()) {
              <div class="stat-card">
                <div class="stat-icon">
                  <span class="material-icons">people</span>
                </div>
                <div class="stat-content">
                  <h3>{{ statistics().total_employees || 0 }}</h3>
                  <p>Total Employees</p>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">
                  <span class="material-icons">trending_up</span>
                </div>
                <div class="stat-content">
                  <h3>{{ pendingHikes().length }}</h3>
                  <p>Pending Hikes</p>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">
                  <span class="material-icons">event_busy</span>
                </div>
                <div class="stat-content">
                  <h3>{{ pendingLeaves().length }}</h3>
                  <p>Pending Leaves</p>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">
                  <span class="material-icons">flag</span>
                </div>
                <div class="stat-content">
                  <h3>{{ activeOKRs().length }}</h3>
                  <p>Active OKRs</p>
                </div>
              </div>
            }
          </div>
        } @else {
          <!-- Employee-specific overview -->
          <div class="employee-overview">
            <div class="overview-grid">
              <!-- Recent Salary -->
              <div class="overview-card">
                <h3>Current Salary</h3>
                @if (salariesLoading()) {
                  <div class="loading">Loading...</div>
                } @else if (salaries().length > 0) {
                  <div class="salary-info">
                    <p class="amount">${{ salaries()[0].net_salary | number:'1.2-2' }}</p>
                    <p class="date">Effective: {{ salaries()[0].effective_date | date }}</p>
                  </div>
                } @else {
                  <p class="no-data">No salary data</p>
                }
              </div>

              <!-- Recent Hikes -->
              <div class="overview-card">
                <h3>Recent Hikes</h3>
                @if (hikesLoading()) {
                  <div class="loading">Loading...</div>
                } @else if (hikes().length > 0) {
                  <div class="hike-list">
                    @for (hike of hikes().slice(0, 3); track hike.id) {
                      <div class="hike-item">
                        <span class="hike-amount">+{{ hike.hike_percentage }}%</span>
                        <span class="hike-date">{{ hike.effective_date | date:'shortDate' }}</span>
                        <span class="hike-status" [class]="'status-' + hike.status">{{ hike.status }}</span>
                      </div>
                    }
                  </div>
                } @else {
                  <p class="no-data">No hike data</p>
                }
              </div>

              <!-- Active OKRs -->
              <div class="overview-card">
                <h3>Active OKRs</h3>
                @if (okrsLoading()) {
                  <div class="loading">Loading...</div>
                } @else if (activeOKRs().length > 0) {
                  <div class="okr-list">
                    @for (okr of activeOKRs().slice(0, 3); track okr.id) {
                      <div class="okr-item">
                        <span class="okr-title">{{ okr.title }}</span>
                        <div class="okr-progress">
                          <div class="progress-bar">
                            <div class="progress-fill" [style.width.%]="okr.progress"></div>
                          </div>
                          <span class="progress-text">{{ okr.progress }}%</span>
                        </div>
                      </div>
                    }
                  </div>
                } @else {
                  <p class="no-data">No active OKRs</p>
                }
              </div>

              <!-- Pending Leaves -->
              <div class="overview-card">
                <h3>Leave Status</h3>
                @if (leavesLoading()) {
                  <div class="loading">Loading...</div>
                } @else if (pendingLeaves().length > 0) {
                  <div class="leave-list">
                    @for (leave of pendingLeaves().slice(0, 3); track leave.id) {
                      <div class="leave-item">
                        <span class="leave-type">{{ leave.leave_type }}</span>
                        <span class="leave-dates">{{ leave.start_date | date:'shortDate' }} - {{ leave.end_date | date:'shortDate' }}</span>
                        <span class="leave-status" [class]="'status-' + leave.status">{{ leave.status }}</span>
                      </div>
                    }
                  </div>
                } @else {
                  <p class="no-data">No pending leaves</p>
                }
              </div>
            </div>
          </div>
        }
      </div>
    }

    <!-- Salary Section -->
    @if (activeSection === 'salary') {
      <div class="section-content">
        @if (salariesError()) {
          <div class="error-state">
            <span class="material-icons">error</span>
            <p>{{ salariesError() }}</p>
          </div>
        } @else {
          <app-data-table-container
            [data]="salaries()"
            [columns]="getSalaryTableConfig().columns"
            [showHeader]="false"
            [showPagination]="false"
            (rowClick)="onTableAction('view', $event)">
          </app-data-table-container>
        }
      </div>
    }

    <!-- Hike Section -->
    @if (activeSection === 'hike') {
      <div class="section-content">
        @if (hikesError()) {
          <div class="error-state">
            <span class="material-icons">error</span>
            <p>{{ hikesError() }}</p>
          </div>
        } @else {
          <app-data-table-container
            [data]="hikes()"
            [columns]="getHikeTableConfig().columns"
            [showHeader]="false"
            [showPagination]="false"
            (rowClick)="onTableAction('view', $event)">
          </app-data-table-container>
        }
      </div>
    }

    <!-- OKR Section -->
    @if (activeSection === 'okr') {
      <div class="section-content">
        @if (okrsError()) {
          <div class="error-state">
            <span class="material-icons">error</span>
            <p>{{ okrsError() }}</p>
          </div>
        } @else {
          <app-data-table-container
            [data]="okrs()"
            [columns]="getOKRTableConfig().columns"
            [showHeader]="false"
            [showPagination]="false"
            (rowClick)="onTableAction('view', $event)">
          </app-data-table-container>
        }
      </div>
    }

    <!-- Leave Section -->
    @if (activeSection === 'leave') {
      <div class="section-content">
        @if (leavesError()) {
          <div class="error-state">
            <span class="material-icons">error</span>
            <p>{{ leavesError() }}</p>
          </div>
        } @else {
          <app-data-table-container
            [data]="leaves()"
            [columns]="getLeaveTableConfig().columns"
            [showHeader]="false"
            [showPagination]="false"
            (rowClick)="onTableAction('view', $event)">
          </app-data-table-container>
        }
      </div>
    }

    <!-- Attendance Section -->
    @if (activeSection === 'attendance') {
      <div class="section-content">
        @if (attendanceError()) {
          <div class="error-state">
            <span class="material-icons">error</span>
            <p>{{ attendanceError() }}</p>
          </div>
        } @else {
          <app-data-table-container
            [data]="attendance()"
            [columns]="getAttendanceTableConfig().columns"
            [showHeader]="false"
            [showPagination]="false"
            (rowClick)="onTableAction('view', $event)">
          </app-data-table-container>
        }
      </div>
    }

    <!-- Training Section -->
    @if (activeSection === 'training') {
      <div class="section-content">
        @if (trainingsError()) {
          <div class="error-state">
            <span class="material-icons">error</span>
            <p>{{ trainingsError() }}</p>
          </div>
        } @else {
          <app-data-table-container
            [data]="trainings()"
            [columns]="getTrainingTableConfig().columns"
            [showHeader]="false"
            [showPagination]="false"
            (rowClick)="onTableAction('view', $event)">
          </app-data-table-container>
        }
      </div>
    }

    <!-- Asset Section -->
    @if (activeSection === 'asset') {
      <div class="section-content">
        @if (assetsError()) {
          <div class="error-state">
            <span class="material-icons">error</span>
            <p>{{ assetsError() }}</p>
          </div>
        } @else {
          <app-data-table-container
            [data]="assets()"
            [columns]="getAssetTableConfig().columns"
            [showHeader]="false"
            [showPagination]="false"
            (rowClick)="onTableAction('view', $event)">
          </app-data-table-container>
        }
      </div>
    }
  </div>
</div>
