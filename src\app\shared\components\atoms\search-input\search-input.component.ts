import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'app-search-input',
  standalone: true,
  imports: [CommonModule, FormsModule, IconComponent],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SearchInputComponent),
      multi: true
    }
  ],
  template: `
    <div class="search-input-container">
      <app-icon name="fa fa-search" size="sm" class="search-icon"></app-icon>
      <input
        type="text"
        class="search-input"
        [placeholder]="placeholder"
        [value]="searchValue"
        [disabled]="disabled"
        (input)="onInput($event)"
        (blur)="onBlur()"
      />
      <app-icon
        *ngIf="searchValue"
        name="fa fa-times"
        size="sm"
        class="clear-icon"
        (click)="clearSearch()"
      ></app-icon>
    </div>
  `,
  styles: [`
    .search-input-container {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
    }

    .search-icon {
      position: absolute;
      left: 12px;
      color: #6c757d;
    }

    .search-input {
      width: 100%;
      padding: 8px 12px 8px 36px;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      font-size: 14px;
      color: #495057;
    }

    .search-input:focus {
      outline: none;
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .clear-icon {
      position: absolute;
      right: 12px;
      color: #6c757d;
      cursor: pointer;
    }

    .clear-icon:hover {
      color: #495057;
    }
  `]
})
export class SearchInputComponent implements ControlValueAccessor {
  @Input() placeholder: string = 'Search...';
  @Input() debounceTime: number = 300;
  @Input() disabled: boolean = false;
  @Input() set value(val: string) {
    this.searchValue = val;
  }

  @Output() search = new EventEmitter<string>();
  @Output() clear = new EventEmitter<void>();

  searchValue: string = '';
  private debounceTimer: any;

  // ControlValueAccessor implementation
  private onChange = (value: string) => {};
  private onTouched = () => {};

  // ControlValueAccessor methods
  writeValue(value: string): void {
    this.searchValue = value || '';
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchValue = target.value;

    // Emit to parent component
    this.search.emit(this.searchValue);

    // Update form control value with debouncing
    clearTimeout(this.debounceTimer);
    this.debounceTimer = setTimeout(() => {
      this.onChange(this.searchValue);
    }, this.debounceTime);
  }

  onBlur(): void {
    this.onTouched();
  }

  clearSearch(): void {
    this.searchValue = '';
    this.onChange('');
    this.search.emit('');
    this.clear.emit();
  }
}
