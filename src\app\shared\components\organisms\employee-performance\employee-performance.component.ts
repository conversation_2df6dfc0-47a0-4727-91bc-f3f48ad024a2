import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-performance',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="performance-container">
      <div class="performance-header">
        <h3 class="section-title">Performance</h3>
        <div class="header-actions">
          <button class="action-btn">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      
      <div class="performance-content">
        <div class="performance-score">
          <div class="score-value">{{ employee?.performance }}%</div>
          <div class="score-label">vs. 65% avg</div>
        </div>
        
        <div class="performance-chart">
          <svg viewBox="0 0 300 150" class="chart">
            <!-- Grid lines -->
            <line x1="0" y1="0" x2="0" y2="120" stroke="#e9ecef" stroke-width="1" />
            <line x1="0" y1="120" x2="300" y2="120" stroke="#e9ecef" stroke-width="1" />
            <line x1="0" y1="90" x2="300" y2="90" stroke="#e9ecef" stroke-width="1" stroke-dasharray="4" />
            <line x1="0" y1="60" x2="300" y2="60" stroke="#e9ecef" stroke-width="1" stroke-dasharray="4" />
            <line x1="0" y1="30" x2="300" y2="30" stroke="#e9ecef" stroke-width="1" stroke-dasharray="4" />
            <line x1="0" y1="0" x2="300" y2="0" stroke="#e9ecef" stroke-width="1" />
            
            <!-- Y-axis labels -->
            <text x="-5" y="120" text-anchor="end" alignment-baseline="middle" font-size="10" fill="#adb5bd">0%</text>
            <text x="-5" y="90" text-anchor="end" alignment-baseline="middle" font-size="10" fill="#adb5bd">25%</text>
            <text x="-5" y="60" text-anchor="end" alignment-baseline="middle" font-size="10" fill="#adb5bd">50%</text>
            <text x="-5" y="30" text-anchor="end" alignment-baseline="middle" font-size="10" fill="#adb5bd">75%</text>
            <text x="-5" y="0" text-anchor="end" alignment-baseline="middle" font-size="10" fill="#adb5bd">100%</text>
            
            <!-- X-axis labels -->
            <text x="0" y="135" text-anchor="middle" font-size="10" fill="#adb5bd">Jan</text>
            <text x="50" y="135" text-anchor="middle" font-size="10" fill="#adb5bd">Feb</text>
            <text x="100" y="135" text-anchor="middle" font-size="10" fill="#adb5bd">Mar</text>
            <text x="150" y="135" text-anchor="middle" font-size="10" fill="#adb5bd">Apr</text>
            <text x="200" y="135" text-anchor="middle" font-size="10" fill="#adb5bd">May</text>
            <text x="250" y="135" text-anchor="middle" font-size="10" fill="#adb5bd">Jun</text>
            
            <!-- Performance line -->
            <path [attr.d]="performancePath" fill="none" stroke="#28a745" stroke-width="2" />
            
            <!-- Area under the line -->
            <path [attr.d]="performanceAreaPath" fill="url(#performance-gradient)" opacity="0.3" />
            
            <!-- Gradient definition -->
            <defs>
              <linearGradient id="performance-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stop-color="#28a745" />
                <stop offset="100%" stop-color="#ffffff" />
              </linearGradient>
            </defs>
          </svg>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .performance-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .performance-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e9ecef;
    }
    
    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }
    
    .action-btn {
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      font-size: 16px;
    }
    
    .performance-content {
      padding: 16px;
      flex: 1;
    }
    
    .performance-score {
      text-align: center;
      margin-bottom: 16px;
    }
    
    .score-value {
      font-size: 24px;
      font-weight: 600;
      color: #28a745;
    }
    
    .score-label {
      font-size: 14px;
      color: #6c757d;
    }
    
    .performance-chart {
      height: 150px;
    }
    
    .chart {
      width: 100%;
      height: 100%;
    }
  `]
})
export class EmployeePerformanceComponent implements OnInit {
  @Input() employee: any;
  
  performancePath: string = '';
  performanceAreaPath: string = '';
  
  ngOnInit(): void {
    this.generatePerformanceChart();
  }
  
  generatePerformanceChart(): void {
    // Sample performance data points (0-100%)
    const performanceData = [70, 65, 75, 85, 90, 95];
    
    // Convert data to SVG path
    let path = '';
    let areaPath = '';
    
    performanceData.forEach((value, index) => {
      const x = index * 50; // 50px spacing between points
      const y = 120 - (value / 100 * 120); // Scale to fit 120px height (0-100%)
      
      if (index === 0) {
        path = `M${x},${y}`;
        areaPath = `M${x},${y}`;
      } else {
        path += ` L${x},${y}`;
        areaPath += ` L${x},${y}`;
      }
    });
    
    // Complete the area path by adding bottom corners
    areaPath += ` L${(performanceData.length - 1) * 50},120 L0,120 Z`;
    
    this.performancePath = path;
    this.performanceAreaPath = areaPath;
  }
}
