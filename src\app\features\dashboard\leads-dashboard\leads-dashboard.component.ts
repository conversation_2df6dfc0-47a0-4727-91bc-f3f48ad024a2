import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

interface MetricCard {
  icon: string;
  iconColor: string;
  title: string;
  value: string | number;
  change: number;
  changeType: 'positive' | 'negative';
}

interface PipelineStage {
  name: string;
  value: number;
  color: string;
}

interface LeadsByCompany {
  name: string;
  value: number;
  logo?: string;
}

interface LeadsBySource {
  source: string;
  percentage: number;
}

@Component({
  selector: 'app-leads-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './leads-dashboard.component.html',
  styleUrl: './leads-dashboard.component.scss'
})
export class LeadsDashboardComponent {
  // Metric cards data
  metricCards: MetricCard[] = [
    {
      icon: 'chart-line',
      iconColor: '#FF7043',
      title: 'Total No of Leads',
      value: 6000,
      change: -4.01,
      changeType: 'negative'
    },
    {
      icon: 'user-plus',
      iconColor: '#26A69A',
      title: 'No of New Leads',
      value: 120,
      change: 20.01,
      changeType: 'positive'
    },
    {
      icon: 'user-minus',
      iconColor: '#EF5350',
      title: 'No of Lost Leads',
      value: 30,
      change: 5,
      changeType: 'positive'
    },
    {
      icon: 'users',
      iconColor: '#AB47BC',
      title: 'No of Total Customers',
      value: 9895,
      change: 5,
      changeType: 'positive'
    }
  ];

  // Pipeline stages data
  pipelineStages: PipelineStage[] = [
    { name: 'Contacted', value: 50000, color: '#FFC107' },
    { name: 'Opportunity', value: 25985, color: '#2196F3' },
    { name: 'Not Contacted', value: 12566, color: '#FF9800' },
    { name: 'Closed', value: 8965, color: '#4CAF50' },
    { name: 'Lost', value: 2452, color: '#F44336' }
  ];

  // Leads by company data
  leadsByCompany: LeadsByCompany[] = [
    { name: 'Pitch', value: 545985 },
    { name: 'Acme Inc', value: 245789 },
    { name: 'Global Tech', value: 198562 },
    { name: 'Innovate Co', value: 156842 }
  ];

  // Leads by source data
  leadsBySource: LeadsBySource[] = [
    { source: 'Social', percentage: 45 },
    { source: 'Organic', percentage: 30 },
    { source: 'Referral', percentage: 15 },
    { source: 'Direct', percentage: 10 }
  ];

  // Lost leads reasons
  lostLeadsReasons = [
    { reason: 'Sales Pipeline', percentage: 35 },
    { reason: 'Price', percentage: 25 },
    { reason: 'Competition', percentage: 20 },
    { reason: 'No Response', percentage: 15 },
    { reason: 'Other', percentage: 5 }
  ];
}
