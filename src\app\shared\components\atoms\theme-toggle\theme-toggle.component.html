<!-- Simple Toggle Button -->
<div class="theme-toggle">
  <button 
    type="button"
    class="theme-toggle__button"
    (click)="toggleTheme()"
    [title]="getToggleTooltip()"
    [attr.aria-label]="getToggleTooltip()">
    <i [class]="getToggleIcon()" class="theme-toggle__icon"></i>
  </button>
</div>

<!-- Dropdown with All Options (Hidden by default, can be shown with CSS class) -->
<div class="theme-toggle theme-toggle--dropdown">
  <div class="theme-toggle__dropdown">
    <button 
      type="button"
      class="theme-toggle__trigger"
      [title]="'Current theme: ' + getThemeModeDisplayName(themeConfig().mode)"
      [attr.aria-label]="'Current theme: ' + getThemeModeDisplayName(themeConfig().mode)">
      <i [class]="getCurrentThemeIcon()" class="theme-toggle__icon"></i>
      <span class="theme-toggle__label">{{ getThemeModeDisplayName(themeConfig().mode) }}</span>
      <i class="fas fa-chevron-down theme-toggle__chevron"></i>
    </button>
    
    <div class="theme-toggle__menu">
      <button
        *ngFor="let mode of getAvailableThemeModes()"
        type="button"
        class="theme-toggle__option"
        [class.theme-toggle__option--active]="themeConfig().mode === mode.value"
        (click)="setThemeMode(mode.value)"
        [attr.aria-label]="'Switch to ' + mode.label + ' theme'">
        <i [class]="mode.icon" class="theme-toggle__option-icon"></i>
        <span class="theme-toggle__option-label">{{ mode.label }}</span>
        <i 
          *ngIf="themeConfig().mode === mode.value"
          class="fas fa-check theme-toggle__check"></i>
      </button>
    </div>
  </div>
</div>
