import { Component, Input, Output, EventEmitter, forwardRef, OnInit, OnDestroy, OnChanges, SimpleChanges, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NG_VALUE_ACCESSOR, ControlValueAccessor, ReactiveFormsModule, FormControl } from '@angular/forms';
import { IconComponent } from '../../atoms/icon/icon.component';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { DropdownService } from '../../../services/dropdown.service';

export interface DropdownOption {
  value: string | number | null;
  label: string;
  icon?: string;
  subtitle?: string; // For additional info like position, department
  avatar?: string; // For employee profile pictures
  disabled?: boolean; // To disable certain options
}

@Component({
  selector: 'app-searchable-dropdown',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, IconComponent],
  templateUrl: './searchable-dropdown.component.html',
  styleUrl: './searchable-dropdown.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SearchableDropdownComponent),
      multi: true
    }
  ]
})
export class SearchableDropdownComponent implements ControlValueAccessor, OnInit, OnDestroy, OnChanges {
  private dropdownService = inject(DropdownService);

  @Input() options: DropdownOption[] = [];
  @Input() placeholder: string = 'Select an option';
  @Input() searchPlaceholder: string = 'Search...';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() invalid: boolean = false;
  @Input() showSearch: boolean = true;
  @Input() id: string = `dropdown-${Math.random().toString(36).substr(2, 9)}`; // Unique ID for dropdown management

  @Output() selectionChange = new EventEmitter<string | number | null>();
  @Output() searchChange = new EventEmitter<string>();

  isOpen: boolean = false;
  searchControl = new FormControl('');
  filteredOptions: DropdownOption[] = [];
  selectedValue: string | number | null = '';

  private destroy$ = new Subject<void>();

  // ControlValueAccessor implementation
  private onChange: (value: string | number | null) => void = () => {};
  private onTouched: () => void = () => {};

  ngOnInit(): void {
    this.updateFilteredOptions();

    // Set up search filtering
    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroy$),
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.filterOptions(searchTerm || '');
      this.searchChange.emit(searchTerm || '');
    });

    // Listen to dropdown service for global dropdown management
    this.dropdownService.isOpen(this.id).pipe(
      takeUntil(this.destroy$)
    ).subscribe(isOpen => {
      this.isOpen = isOpen;
      if (!isOpen) {
        // Clean up when closed
        this.searchControl.setValue('');
        this.updateFilteredOptions();
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update filtered options when options input changes
    if (changes['options'] && changes['options'].currentValue) {
      this.updateFilteredOptions();

      // If dropdown is open and search is active, reapply filter
      if (this.isOpen && this.searchControl.value) {
        this.filterOptions(this.searchControl.value);
      }
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Update filtered options (helper method)
  private updateFilteredOptions(): void {
    this.filteredOptions = [...this.options];
  }

  // Filter options based on search term
  filterOptions(searchTerm: string): void {
    if (!searchTerm.trim()) {
      this.updateFilteredOptions();
      return;
    }

    const term = searchTerm.toLowerCase().trim();
    this.filteredOptions = this.options.filter(option => {
      const labelMatch = option.label.toLowerCase().includes(term);
      const subtitleMatch = option.subtitle?.toLowerCase().includes(term) || false;
      return labelMatch || subtitleMatch;
    });
  }

  // Get the label for the selected value
  get selectedLabel(): string {
    const selected = this.options.find(option => option.value === this.selectedValue);
    return selected ? selected.label : this.placeholder;
  }

  // Toggle dropdown
  toggleDropdown(event: Event): void {
    if (this.disabled) return;

    event.stopPropagation();

    if (this.isOpen) {
      // Close this dropdown
      this.dropdownService.close(this.id);
    } else {
      // Open this dropdown (closes all others)
      this.dropdownService.open(this.id);

      // Reset search when opening
      this.searchControl.setValue('');
      this.updateFilteredOptions();

      // Add click outside listener
      setTimeout(() => {
        document.addEventListener('click', this.closeDropdown);
      });
    }

    this.onTouched();
  }

  // Close dropdown
  closeDropdown = (): void => {
    this.dropdownService.close(this.id);
    document.removeEventListener('click', this.closeDropdown);
  }

  // Select an option
  selectOption(option: DropdownOption): void {
    if (option.disabled) return;

    this.selectedValue = option.value;
    this.onChange(option.value);
    this.selectionChange.emit(option.value);
    this.dropdownService.close(this.id);
  }

  // ControlValueAccessor methods
  writeValue(value: string | number | null): void {
    this.selectedValue = value;
  }

  registerOnChange(fn: (value: string | number | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
