@use 'sass:color';
@import '../../../../styles/variables/_colors';

.company-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

// Company Header
.company-header {
  background-color: $white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba($black, 0.05);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.header-content {
  display: flex;
  align-items: center;
  padding: 1.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
  }
}

.back-button {
  background: none;
  border: none;
  color: $gray-600;
  font-size: 1.25rem;
  cursor: pointer;
  margin-right: 1rem;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background-color: $gray-100;
    color: $gray-800;
  }

  @media (max-width: 768px) {
    margin-bottom: 1rem;
  }
}

.company-info {
  display: flex;
  align-items: center;
  flex: 1;

  @media (max-width: 768px) {
    width: 100%;
    margin-bottom: 1rem;
  }
}

.company-logo {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 1rem;
  background-color: $gray-100;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.company-title {
  flex: 1;

  h1 {
    font-size: 1.5rem;
    font-weight: $font-weight-bold;
    color: $gray-900;
    margin: 0 0 0.25rem;
  }
}

.company-meta {
  display: flex;
  align-items: center;
  color: $gray-600;
  font-size: 0.875rem;

  .company-industry {
    margin-right: 1rem;

    &::before {
      content: '\f275';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      margin-right: 0.25rem;
    }
  }

  .company-location {
    &::before {
      content: '\f3c5';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      margin-right: 0.25rem;
    }
  }
}

.company-actions {
  display: flex;
  gap: 0.75rem;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: flex-end;
  }
}

// Loading Skeleton
.loading-skeleton {
  flex: 1;

  .skeleton-title {
    height: 24px;
    width: 200px;
    background-color: $gray-200;
    border-radius: 4px;
    margin-bottom: 8px;
  }

  .skeleton-subtitle {
    height: 16px;
    width: 150px;
    background-color: $gray-200;
    border-radius: 4px;
  }
}

// Error State
.error-state {
  display: flex;
  align-items: center;
  color: $danger;

  i {
    font-size: 1.5rem;
    margin-right: 0.5rem;
  }

  p {
    margin: 0;
    font-weight: $font-weight-medium;
  }
}

.error-message {
  background-color: rgba($danger, 0.1);
  color: $danger;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;

  i {
    margin-right: 0.5rem;
  }
}

// Buttons
.btn-edit, .btn-save, .btn-cancel, .btn-delete, .btn-add, .btn-add-first {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: $font-weight-medium;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  i {
    margin-right: 0.5rem;
  }
}

.btn-edit {
  background-color: $primary;
  color: $white;

  &:hover {
    background-color: color.adjust($primary, $lightness: -10%);
  }
}

.btn-save {
  background-color: $success;
  color: $white;

  &:hover:not(:disabled) {
    background-color: color.adjust($success, $lightness: -10%);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.btn-cancel {
  background-color: $gray-200;
  color: $gray-700;

  &:hover {
    background-color: $gray-300;
  }
}

.btn-delete {
  background-color: $white;
  color: $danger;
  border: 1px solid $danger;

  &:hover {
    background-color: rgba($danger, 0.1);
  }
}

.btn-add, .btn-add-first {
  background-color: $primary;
  color: $white;

  &:hover {
    background-color: color.adjust($primary, $lightness: -10%);
  }
}

.btn-add-first {
  margin-top: 1rem;
}

// Tabs
.company-tabs {
  margin-bottom: 1.5rem;
}

.tabs-list {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  border-bottom: 1px solid $gray-200;

  @media (max-width: 576px) {
    flex-wrap: wrap;
  }
}

.tab-item {
  margin-right: 1rem;

  &.active {
    .tab-link {
      color: $primary;
      border-bottom-color: $primary;
    }
  }

  @media (max-width: 576px) {
    margin-bottom: 0.5rem;
  }
}

.tab-link {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 0;
  color: $gray-600;
  border-bottom: 2px solid transparent;
  background: none;
  border-top: none;
  border-left: none;
  border-right: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: $font-weight-medium;

  i {
    margin-right: 0.5rem;
  }

  &:hover {
    color: $primary;
  }
}

.tab-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba($primary, 0.1);
  color: $primary;
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  margin-left: 0.5rem;
}

// Company Content
.company-content {
  background-color: $white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba($black, 0.05);
  padding: 2rem;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
}

// Tab Content
.tab-content {
  max-width: 800px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  @media (max-width: 576px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

.tab-title {
  font-size: 1.25rem;
  font-weight: $font-weight-semibold;
  color: $gray-800;
  margin: 0;
}

// Form
.company-form {
  max-width: 800px;
}

.form-section {
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 1.125rem;
  font-weight: $font-weight-semibold;
  color: $gray-800;
  margin: 0 0 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid $gray-200;
}

.form-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
}

.form-group {
  flex: 1;
  margin-bottom: 1.5rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }

  input, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid $gray-300;
    border-radius: 4px;
    font-size: 1rem;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba($primary, 0.1);
    }

    &.readonly {
      background-color: $gray-100;
      cursor: default;
    }
  }

  textarea {
    resize: vertical;
  }
}

.form-error {
  color: $danger;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;

  i {
    font-size: 3rem;
    color: $gray-300;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1rem;
    color: $gray-600;
    margin: 0 0 0.5rem;
  }
}


