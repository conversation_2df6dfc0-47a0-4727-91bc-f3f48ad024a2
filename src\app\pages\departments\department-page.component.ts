import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DepartmentPageTemplateComponent } from '../../shared/components/templates/department-page-template/department-page-template.component';

@Component({
  selector: 'app-department-page',
  standalone: true,
  imports: [CommonModule, DepartmentPageTemplateComponent],
  template: `<app-department-page-template></app-department-page-template>`,
  styles: []
})
export class DepartmentPageComponent {
  // This is a page component that uses the template
  // It's kept simple as the template handles the layout
  // and the components handle the business logic
}
