/**
 * Theme CSS Custom Properties
 * These variables are dynamically updated by the ThemeService
 */

:root {
  // Primary colors
  --theme-primary: #3b82f6;
  --theme-primary-light: #60a5fa;
  --theme-primary-dark: #2563eb;
  --theme-primary-rgb: 59, 130, 246;
  
  // Secondary colors
  --theme-secondary: #64748b;
  --theme-secondary-light: #94a3b8;
  --theme-secondary-dark: #475569;
  --theme-secondary-rgb: 100, 116, 139;
  
  // Neutral colors
  --theme-background: #ffffff;
  --theme-surface: #f8fafc;
  --theme-surface-variant: #f1f5f9;
  --theme-background-rgb: 255, 255, 255;
  --theme-surface-rgb: 248, 250, 252;
  
  // Text colors
  --theme-on-background: #1e293b;
  --theme-on-surface: #334155;
  --theme-on-primary: #ffffff;
  --theme-on-secondary: #ffffff;
  --theme-on-background-rgb: 30, 41, 59;
  --theme-on-surface-rgb: 51, 65, 85;
  
  // Status colors
  --theme-success: #10b981;
  --theme-warning: #f59e0b;
  --theme-error: #ef4444;
  --theme-info: #06b6d4;
  --theme-success-rgb: 16, 185, 129;
  --theme-warning-rgb: 245, 158, 11;
  --theme-error-rgb: 239, 68, 68;
  --theme-info-rgb: 6, 182, 212;
  
  // Border and divider colors
  --theme-border: #e2e8f0;
  --theme-divider: #f1f5f9;
  --theme-border-rgb: 226, 232, 240;
  --theme-divider-rgb: 241, 245, 249;
  
  // Input colors
  --theme-input-background: #ffffff;
  --theme-input-border: #d1d5db;
  --theme-input-focus: #3b82f6;
  --theme-input-background-rgb: 255, 255, 255;
  --theme-input-border-rgb: 209, 213, 219;
  
  // Card colors
  --theme-card-background: #ffffff;
  --theme-card-border: #e5e7eb;
  --theme-card-shadow: rgba(0, 0, 0, 0.1);
  --theme-card-background-rgb: 255, 255, 255;
  --theme-card-border-rgb: 229, 231, 235;
  
  // Elevation shadows
  --theme-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --theme-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --theme-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --theme-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  // Transition durations
  --theme-transition-fast: 150ms;
  --theme-transition-normal: 250ms;
  --theme-transition-slow: 350ms;
  
  // Border radius
  --theme-radius-sm: 4px;
  --theme-radius-md: 8px;
  --theme-radius-lg: 12px;
  --theme-radius-xl: 16px;
  --theme-radius-full: 9999px;
  
  // Spacing scale
  --theme-spacing-xs: 0.25rem;
  --theme-spacing-sm: 0.5rem;
  --theme-spacing-md: 1rem;
  --theme-spacing-lg: 1.5rem;
  --theme-spacing-xl: 2rem;
  --theme-spacing-2xl: 3rem;
  
  // Typography
  --theme-font-size-xs: 0.75rem;
  --theme-font-size-sm: 0.875rem;
  --theme-font-size-base: 1rem;
  --theme-font-size-lg: 1.125rem;
  --theme-font-size-xl: 1.25rem;
  --theme-font-size-2xl: 1.5rem;
  --theme-font-size-3xl: 1.875rem;
  
  --theme-font-weight-normal: 400;
  --theme-font-weight-medium: 500;
  --theme-font-weight-semibold: 600;
  --theme-font-weight-bold: 700;
  
  --theme-line-height-tight: 1.25;
  --theme-line-height-normal: 1.5;
  --theme-line-height-relaxed: 1.75;
}

// Dark theme overrides
.theme-dark {
  // Primary colors (adjusted for dark theme)
  --theme-primary: #60a5fa;
  --theme-primary-light: #93c5fd;
  --theme-primary-dark: #3b82f6;
  --theme-primary-rgb: 96, 165, 250;
  
  // Secondary colors
  --theme-secondary: #94a3b8;
  --theme-secondary-light: #cbd5e1;
  --theme-secondary-dark: #64748b;
  --theme-secondary-rgb: 148, 163, 184;
  
  // Neutral colors
  --theme-background: #0f172a;
  --theme-surface: #1e293b;
  --theme-surface-variant: #334155;
  --theme-background-rgb: 15, 23, 42;
  --theme-surface-rgb: 30, 41, 59;
  
  // Text colors
  --theme-on-background: #f8fafc;
  --theme-on-surface: #e2e8f0;
  --theme-on-primary: #1e293b;
  --theme-on-secondary: #1e293b;
  --theme-on-background-rgb: 248, 250, 252;
  --theme-on-surface-rgb: 226, 232, 240;
  
  // Status colors (brighter for dark theme)
  --theme-success: #34d399;
  --theme-warning: #fbbf24;
  --theme-error: #f87171;
  --theme-info: #22d3ee;
  --theme-success-rgb: 52, 211, 153;
  --theme-warning-rgb: 251, 191, 36;
  --theme-error-rgb: 248, 113, 113;
  --theme-info-rgb: 34, 211, 238;
  
  // Border and divider colors
  --theme-border: #475569;
  --theme-divider: #334155;
  --theme-border-rgb: 71, 85, 105;
  --theme-divider-rgb: 51, 65, 85;
  
  // Input colors
  --theme-input-background: #1e293b;
  --theme-input-border: #475569;
  --theme-input-focus: #60a5fa;
  --theme-input-background-rgb: 30, 41, 59;
  --theme-input-border-rgb: 71, 85, 105;
  
  // Card colors
  --theme-card-background: #1e293b;
  --theme-card-border: #475569;
  --theme-card-shadow: rgba(0, 0, 0, 0.3);
  --theme-card-background-rgb: 30, 41, 59;
  --theme-card-border-rgb: 71, 85, 105;
  
  // Elevation shadows (darker for dark theme)
  --theme-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --theme-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --theme-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --theme-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

// Header position styles
.header-fixed {
  .main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }
  
  .main-content {
    padding-top: var(--header-height, 64px);
  }
}

.header-static {
  .main-header {
    position: static;
  }
  
  .main-content {
    padding-top: 0;
  }
}

.header-sticky {
  .main-header {
    position: sticky;
    top: 0;
    z-index: 1000;
  }
  
  .main-content {
    padding-top: 0;
  }
}

// Sidebar layout styles
.sidebar-collapsed {
  .main-sidebar {
    transform: translateX(-100%);
    
    &.sidebar-right {
      transform: translateX(100%);
    }
  }
  
  .main-content {
    margin-left: 0;
    
    &.sidebar-right {
      margin-right: 0;
    }
  }
}

// Layout type styles
.layout-horizontal {
  .main-layout {
    flex-direction: column;
  }
  
  .main-sidebar {
    width: 100%;
    height: auto;
  }
  
  .main-content {
    margin-left: 0;
    margin-top: var(--sidebar-height, auto);
  }
}

.layout-vertical {
  .main-layout {
    flex-direction: row;
  }
  
  .main-sidebar {
    width: var(--sidebar-width, 280px);
    height: 100vh;
  }
  
  .main-content {
    margin-left: var(--sidebar-width, 280px);
    margin-top: 0;
  }
}
