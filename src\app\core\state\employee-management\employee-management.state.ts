import { computed, inject } from '@angular/core';
import { patchState, signalStore, withComputed, withMethods, withState } from '@ngrx/signals';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, switchMap, tap, catchError, EMPTY, debounceTime, distinctUntilChanged } from 'rxjs';

// Import services and interfaces
import {
  EmployeeManagementService,
  EmployeeSalary,
  EmployeeHike,
  EmployeeOKR,
  EmployeePerformance,
  EmployeeLeave,
  EmployeeAttendance,
  EmployeeTraining,
  EmployeeAsset,
  PaginatedResponse
} from '../../services/employee-management.service';

// State interface
interface EmployeeManagementState {
  // Salary Management
  salaries: EmployeeSalary[];
  salariesLoading: boolean;
  salariesError: string | null;
  salariesPage: number;
  salariesPageSize: number;
  salariesTotalCount: number;

  // Hike Management
  hikes: EmployeeHike[];
  hikesLoading: boolean;
  hikesError: string | null;
  hikesPage: number;
  hikesPageSize: number;
  hikesTotalCount: number;

  // OKR Management
  okrs: EmployeeOKR[];
  okrsLoading: boolean;
  okrsError: string | null;
  okrsPage: number;
  okrsPageSize: number;
  okrsTotalCount: number;
  selectedQuarter: string | null;
  selectedYear: number | null;

  // Performance Management
  performances: EmployeePerformance[];
  performancesLoading: boolean;
  performancesError: string | null;
  performancesPage: number;
  performancesPageSize: number;
  performancesTotalCount: number;

  // Leave Management
  leaves: EmployeeLeave[];
  leavesLoading: boolean;
  leavesError: string | null;
  leavesPage: number;
  leavesPageSize: number;
  leavesTotalCount: number;
  selectedLeaveStatus: string | null;

  // Attendance Management
  attendance: EmployeeAttendance[];
  attendanceLoading: boolean;
  attendanceError: string | null;
  attendancePage: number;
  attendancePageSize: number;
  attendanceTotalCount: number;
  selectedDate: string | null;

  // Training Management
  trainings: EmployeeTraining[];
  trainingsLoading: boolean;
  trainingsError: string | null;
  trainingsPage: number;
  trainingsPageSize: number;
  trainingsTotalCount: number;
  selectedTrainingStatus: string | null;

  // Asset Management
  assets: EmployeeAsset[];
  assetsLoading: boolean;
  assetsError: string | null;
  assetsPage: number;
  assetsPageSize: number;
  assetsTotalCount: number;
  selectedAssetStatus: string | null;

  // General
  selectedEmployeeId: number | null;
  statistics: any;
  statisticsLoading: boolean;
  statisticsError: string | null;
}

// Initial state
const initialState: EmployeeManagementState = {
  // Salary Management
  salaries: [],
  salariesLoading: false,
  salariesError: null,
  salariesPage: 1,
  salariesPageSize: 10,
  salariesTotalCount: 0,

  // Hike Management
  hikes: [],
  hikesLoading: false,
  hikesError: null,
  hikesPage: 1,
  hikesPageSize: 10,
  hikesTotalCount: 0,

  // OKR Management
  okrs: [],
  okrsLoading: false,
  okrsError: null,
  okrsPage: 1,
  okrsPageSize: 10,
  okrsTotalCount: 0,
  selectedQuarter: null,
  selectedYear: new Date().getFullYear(),

  // Performance Management
  performances: [],
  performancesLoading: false,
  performancesError: null,
  performancesPage: 1,
  performancesPageSize: 10,
  performancesTotalCount: 0,

  // Leave Management
  leaves: [],
  leavesLoading: false,
  leavesError: null,
  leavesPage: 1,
  leavesPageSize: 10,
  leavesTotalCount: 0,
  selectedLeaveStatus: null,

  // Attendance Management
  attendance: [],
  attendanceLoading: false,
  attendanceError: null,
  attendancePage: 1,
  attendancePageSize: 10,
  attendanceTotalCount: 0,
  selectedDate: null,

  // Training Management
  trainings: [],
  trainingsLoading: false,
  trainingsError: null,
  trainingsPage: 1,
  trainingsPageSize: 10,
  trainingsTotalCount: 0,
  selectedTrainingStatus: null,

  // Asset Management
  assets: [],
  assetsLoading: false,
  assetsError: null,
  assetsPage: 1,
  assetsPageSize: 10,
  assetsTotalCount: 0,
  selectedAssetStatus: null,

  // General
  selectedEmployeeId: null,
  statistics: null,
  statisticsLoading: false,
  statisticsError: null,
};

export const EmployeeManagementStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withComputed((store) => ({
    // Computed properties for better data access
    hasSalaries: computed(() => store.salaries().length > 0),
    hasHikes: computed(() => store.hikes().length > 0),
    hasOKRs: computed(() => store.okrs().length > 0),
    hasPerformances: computed(() => store.performances().length > 0),
    hasLeaves: computed(() => store.leaves().length > 0),
    hasAttendance: computed(() => store.attendance().length > 0),
    hasTrainings: computed(() => store.trainings().length > 0),
    hasAssets: computed(() => store.assets().length > 0),

    // Pagination computed properties
    salariesHasNextPage: computed(() => {
      const totalPages = Math.ceil(store.salariesTotalCount() / store.salariesPageSize());
      return store.salariesPage() < totalPages;
    }),
    hikesHasNextPage: computed(() => {
      const totalPages = Math.ceil(store.hikesTotalCount() / store.hikesPageSize());
      return store.hikesPage() < totalPages;
    }),
    okrsHasNextPage: computed(() => {
      const totalPages = Math.ceil(store.okrsTotalCount() / store.okrsPageSize());
      return store.okrsPage() < totalPages;
    }),

    // Status filters
    pendingHikes: computed(() => store.hikes().filter(hike => hike.status === 'pending')),
    approvedHikes: computed(() => store.hikes().filter(hike => hike.status === 'approved')),
    activeOKRs: computed(() => store.okrs().filter(okr => okr.status === 'active')),
    pendingLeaves: computed(() => store.leaves().filter(leave => leave.status === 'pending')),
    assignedAssets: computed(() => store.assets().filter(asset => asset.status === 'assigned')),
  })),
  withMethods((store, employeeManagementService = inject(EmployeeManagementService)) => ({
    // Salary Management Methods
    loadSalaries: rxMethod<{ employeeId?: number; page?: number; pageSize?: number }>(
      pipe(
        tap(({ page, pageSize }) => {
          if (page !== undefined) patchState(store, { salariesPage: page });
          if (pageSize !== undefined) patchState(store, { salariesPageSize: pageSize });
          patchState(store, { salariesLoading: true, salariesError: null });
        }),
        switchMap(({ employeeId, page, pageSize }) =>
          employeeManagementService.getEmployeeSalaries(
            employeeId || store.selectedEmployeeId() || undefined,
            page || store.salariesPage(),
            pageSize || store.salariesPageSize()
          ).pipe(
            tap((response: PaginatedResponse<EmployeeSalary>) => {
              patchState(store, {
                salaries: response.results,
                salariesTotalCount: response.count,
                salariesLoading: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                salariesError: error.message,
                salariesLoading: false
              });
              return EMPTY;
            })
          )
        )
      )
    ),

    createSalary: rxMethod<Partial<EmployeeSalary>>(
      pipe(
        switchMap((salaryData) =>
          employeeManagementService.createEmployeeSalary(salaryData).pipe(
            tap((newSalary: EmployeeSalary) => {
              patchState(store, {
                salaries: [newSalary, ...store.salaries()]
              });
            }),
            catchError((error) => {
              patchState(store, { salariesError: error.message });
              return EMPTY;
            })
          )
        )
      )
    ),

    // Hike Management Methods
    loadHikes: rxMethod<{ employeeId?: number; page?: number; pageSize?: number }>(
      pipe(
        tap(({ page, pageSize }) => {
          if (page !== undefined) patchState(store, { hikesPage: page });
          if (pageSize !== undefined) patchState(store, { hikesPageSize: pageSize });
          patchState(store, { hikesLoading: true, hikesError: null });
        }),
        switchMap(({ employeeId, page, pageSize }) =>
          employeeManagementService.getEmployeeHikes(
            employeeId || store.selectedEmployeeId() || undefined,
            page || store.hikesPage(),
            pageSize || store.hikesPageSize()
          ).pipe(
            tap((response: PaginatedResponse<EmployeeHike>) => {
              patchState(store, {
                hikes: response.results,
                hikesTotalCount: response.count,
                hikesLoading: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                hikesError: error.message,
                hikesLoading: false
              });
              return EMPTY;
            })
          )
        )
      )
    ),

    createHike: rxMethod<Partial<EmployeeHike>>(
      pipe(
        switchMap((hikeData) =>
          employeeManagementService.createEmployeeHike(hikeData).pipe(
            tap((newHike: EmployeeHike) => {
              patchState(store, {
                hikes: [newHike, ...store.hikes()]
              });
            }),
            catchError((error) => {
              patchState(store, { hikesError: error.message });
              return EMPTY;
            })
          )
        )
      )
    ),

    approveHike: rxMethod<number>(
      pipe(
        switchMap((hikeId) =>
          employeeManagementService.approveEmployeeHike(hikeId).pipe(
            tap((updatedHike: EmployeeHike) => {
              const updatedHikes = store.hikes().map(hike =>
                hike.id === hikeId ? updatedHike : hike
              );
              patchState(store, { hikes: updatedHikes });
            }),
            catchError((error) => {
              patchState(store, { hikesError: error.message });
              return EMPTY;
            })
          )
        )
      )
    ),

    // OKR Management Methods
    loadOKRs: rxMethod<{ employeeId?: number; quarter?: string; year?: number; page?: number; pageSize?: number }>(
      pipe(
        tap(({ page, pageSize, quarter, year }) => {
          if (page !== undefined) patchState(store, { okrsPage: page });
          if (pageSize !== undefined) patchState(store, { okrsPageSize: pageSize });
          if (quarter !== undefined) patchState(store, { selectedQuarter: quarter });
          if (year !== undefined) patchState(store, { selectedYear: year });
          patchState(store, { okrsLoading: true, okrsError: null });
        }),
        switchMap(({ employeeId, quarter, year, page, pageSize }) =>
          employeeManagementService.getEmployeeOKRs(
            employeeId || store.selectedEmployeeId() || undefined,
            quarter || store.selectedQuarter() || undefined,
            year || store.selectedYear() || undefined,
            page || store.okrsPage(),
            pageSize || store.okrsPageSize()
          ).pipe(
            tap((response: PaginatedResponse<EmployeeOKR>) => {
              patchState(store, {
                okrs: response.results,
                okrsTotalCount: response.count,
                okrsLoading: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                okrsError: error.message,
                okrsLoading: false
              });
              return EMPTY;
            })
          )
        )
      )
    ),

    createOKR: rxMethod<Partial<EmployeeOKR>>(
      pipe(
        switchMap((okrData) =>
          employeeManagementService.createEmployeeOKR(okrData).pipe(
            tap((newOKR: EmployeeOKR) => {
              patchState(store, {
                okrs: [newOKR, ...store.okrs()]
              });
            }),
            catchError((error) => {
              patchState(store, { okrsError: error.message });
              return EMPTY;
            })
          )
        )
      )
    ),

    updateOKRProgress: rxMethod<{ id: number; progress: number }>(
      pipe(
        switchMap(({ id, progress }) =>
          employeeManagementService.updateOKRProgress(id, progress).pipe(
            tap((updatedOKR: EmployeeOKR) => {
              const updatedOKRs = store.okrs().map(okr =>
                okr.id === id ? updatedOKR : okr
              );
              patchState(store, { okrs: updatedOKRs });
            }),
            catchError((error) => {
              patchState(store, { okrsError: error.message });
              return EMPTY;
            })
          )
        )
      )
    ),

    // Leave Management Methods
    loadLeaves: rxMethod<{ employeeId?: number; status?: string; page?: number; pageSize?: number }>(
      pipe(
        tap(({ page, pageSize, status }) => {
          if (page !== undefined) patchState(store, { leavesPage: page });
          if (pageSize !== undefined) patchState(store, { leavesPageSize: pageSize });
          if (status !== undefined) patchState(store, { selectedLeaveStatus: status });
          patchState(store, { leavesLoading: true, leavesError: null });
        }),
        switchMap(({ employeeId, status, page, pageSize }) =>
          employeeManagementService.getEmployeeLeaves(
            employeeId || store.selectedEmployeeId() || undefined,
            status || store.selectedLeaveStatus() || undefined,
            page || store.leavesPage(),
            pageSize || store.leavesPageSize()
          ).pipe(
            tap((response: PaginatedResponse<EmployeeLeave>) => {
              patchState(store, {
                leaves: response.results,
                leavesTotalCount: response.count,
                leavesLoading: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                leavesError: error.message,
                leavesLoading: false
              });
              return EMPTY;
            })
          )
        )
      )
    ),

    createLeave: rxMethod<Partial<EmployeeLeave>>(
      pipe(
        switchMap((leaveData) =>
          employeeManagementService.createEmployeeLeave(leaveData).pipe(
            tap((newLeave: EmployeeLeave) => {
              patchState(store, {
                leaves: [newLeave, ...store.leaves()]
              });
            }),
            catchError((error) => {
              patchState(store, { leavesError: error.message });
              return EMPTY;
            })
          )
        )
      )
    ),

    approveLeave: rxMethod<number>(
      pipe(
        switchMap((leaveId) =>
          employeeManagementService.approveEmployeeLeave(leaveId).pipe(
            tap((updatedLeave: EmployeeLeave) => {
              const updatedLeaves = store.leaves().map(leave =>
                leave.id === leaveId ? updatedLeave : leave
              );
              patchState(store, { leaves: updatedLeaves });
            }),
            catchError((error) => {
              patchState(store, { leavesError: error.message });
              return EMPTY;
            })
          )
        )
      )
    ),

    // Attendance Management Methods
    loadAttendance: rxMethod<{ employeeId?: number; date?: string; page?: number; pageSize?: number }>(
      pipe(
        tap(({ page, pageSize, date }) => {
          if (page !== undefined) patchState(store, { attendancePage: page });
          if (pageSize !== undefined) patchState(store, { attendancePageSize: pageSize });
          if (date !== undefined) patchState(store, { selectedDate: date });
          patchState(store, { attendanceLoading: true, attendanceError: null });
        }),
        switchMap(({ employeeId, date, page, pageSize }) =>
          employeeManagementService.getEmployeeAttendance(
            employeeId || store.selectedEmployeeId() || undefined,
            date || store.selectedDate() || undefined,
            page || store.attendancePage(),
            pageSize || store.attendancePageSize()
          ).pipe(
            tap((response: PaginatedResponse<EmployeeAttendance>) => {
              patchState(store, {
                attendance: response.results,
                attendanceTotalCount: response.count,
                attendanceLoading: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                attendanceError: error.message,
                attendanceLoading: false
              });
              return EMPTY;
            })
          )
        )
      )
    ),

    // Training Management Methods
    loadTrainings: rxMethod<{ employeeId?: number; status?: string; page?: number; pageSize?: number }>(
      pipe(
        tap(({ page, pageSize, status }) => {
          if (page !== undefined) patchState(store, { trainingsPage: page });
          if (pageSize !== undefined) patchState(store, { trainingsPageSize: pageSize });
          if (status !== undefined) patchState(store, { selectedTrainingStatus: status });
          patchState(store, { trainingsLoading: true, trainingsError: null });
        }),
        switchMap(({ employeeId, status, page, pageSize }) =>
          employeeManagementService.getEmployeeTrainings(
            employeeId || store.selectedEmployeeId() || undefined,
            status || store.selectedTrainingStatus() || undefined,
            page || store.trainingsPage(),
            pageSize || store.trainingsPageSize()
          ).pipe(
            tap((response: PaginatedResponse<EmployeeTraining>) => {
              patchState(store, {
                trainings: response.results,
                trainingsTotalCount: response.count,
                trainingsLoading: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                trainingsError: error.message,
                trainingsLoading: false
              });
              return EMPTY;
            })
          )
        )
      )
    ),

    // Asset Management Methods
    loadAssets: rxMethod<{ employeeId?: number; status?: string; page?: number; pageSize?: number }>(
      pipe(
        tap(({ page, pageSize, status }) => {
          if (page !== undefined) patchState(store, { assetsPage: page });
          if (pageSize !== undefined) patchState(store, { assetsPageSize: pageSize });
          if (status !== undefined) patchState(store, { selectedAssetStatus: status });
          patchState(store, { assetsLoading: true, assetsError: null });
        }),
        switchMap(({ employeeId, status, page, pageSize }) =>
          employeeManagementService.getEmployeeAssets(
            employeeId || store.selectedEmployeeId() || undefined,
            status || store.selectedAssetStatus() || undefined,
            page || store.assetsPage(),
            pageSize || store.assetsPageSize()
          ).pipe(
            tap((response: PaginatedResponse<EmployeeAsset>) => {
              patchState(store, {
                assets: response.results,
                assetsTotalCount: response.count,
                assetsLoading: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                assetsError: error.message,
                assetsLoading: false
              });
              return EMPTY;
            })
          )
        )
      )
    ),

    // General Methods
    setSelectedEmployeeId: (employeeId: number | null) => {
      patchState(store, { selectedEmployeeId: employeeId });
    },

    loadStatistics: rxMethod<void>(
      pipe(
        tap(() => patchState(store, { statisticsLoading: true, statisticsError: null })),
        switchMap(() =>
          employeeManagementService.getEmployeeStatistics().pipe(
            tap((statistics) => {
              patchState(store, {
                statistics,
                statisticsLoading: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                statisticsError: error.message,
                statisticsLoading: false
              });
              return EMPTY;
            })
          )
        )
      )
    ),

    clearErrors: () => {
      patchState(store, {
        salariesError: null,
        hikesError: null,
        okrsError: null,
        performancesError: null,
        leavesError: null,
        attendanceError: null,
        trainingsError: null,
        assetsError: null,
        statisticsError: null
      });
    },

    reset: () => {
      patchState(store, initialState);
    }
  }))
);
