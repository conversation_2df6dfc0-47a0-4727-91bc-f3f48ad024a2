@import '../../../../../styles/variables/_colors';

.theme-toggle {
  position: relative;
  display: inline-block;

  &__button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: var(--theme-surface, $gray-100);
    color: var(--theme-on-surface, $gray-700);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      background: var(--theme-surface-variant, $gray-200);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px var(--theme-primary, $primary);
    }
  }

  &__icon {
    font-size: 1.125rem;
    transition: all 0.3s ease;
  }

  // Dropdown variant
  &--dropdown {
    .theme-toggle__dropdown {
      position: relative;
    }

    .theme-toggle__trigger {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 0.75rem;
      border: 1px solid var(--theme-border, $gray-300);
      border-radius: 8px;
      background: var(--theme-surface, $white);
      color: var(--theme-on-surface, $gray-700);
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 120px;

      &:hover {
        background: var(--theme-surface-variant, $gray-50);
        border-color: var(--theme-primary, $primary);
      }

      &:focus {
        outline: none;
        border-color: var(--theme-primary, $primary);
        box-shadow: 0 0 0 2px rgba(var(--theme-primary-rgb, 59, 130, 246), 0.1);
      }
    }

    .theme-toggle__label {
      font-size: 0.875rem;
      font-weight: 500;
      flex: 1;
      text-align: left;
    }

    .theme-toggle__chevron {
      font-size: 0.75rem;
      transition: transform 0.2s ease;
    }

    .theme-toggle__menu {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      margin-top: 0.25rem;
      background: var(--theme-surface, $white);
      border: 1px solid var(--theme-border, $gray-300);
      border-radius: 8px;
      box-shadow: var(--theme-card-shadow, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-8px);
      transition: all 0.2s ease;
      overflow: hidden;
    }

    &:hover .theme-toggle__menu,
    .theme-toggle__trigger:focus + .theme-toggle__menu,
    .theme-toggle__menu:hover {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    &:hover .theme-toggle__chevron {
      transform: rotate(180deg);
    }
  }

  &__option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem;
    border: none;
    background: transparent;
    color: var(--theme-on-surface, $gray-700);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;

    &:hover {
      background: var(--theme-surface-variant, $gray-50);
    }

    &:focus {
      outline: none;
      background: var(--theme-surface-variant, $gray-50);
    }

    &--active {
      background: rgba(var(--theme-primary-rgb, 59, 130, 246), 0.1);
      color: var(--theme-primary, $primary);

      .theme-toggle__option-icon {
        color: var(--theme-primary, $primary);
      }
    }

    &:not(:last-child) {
      border-bottom: 1px solid var(--theme-divider, $gray-100);
    }
  }

  &__option-icon {
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
  }

  &__option-label {
    font-size: 0.875rem;
    font-weight: 500;
    flex: 1;
  }

  &__check {
    font-size: 0.875rem;
    color: var(--theme-primary, $primary);
  }
}

// Dark theme specific styles
:host-context(.theme-dark) {
  .theme-toggle {
    &__button {
      background: var(--theme-surface, #{$gray-800});
      color: var(--theme-on-surface, #{$gray-200});

      &:hover {
        background: var(--theme-surface-variant, #{$gray-700});
      }
    }

    &--dropdown {
      .theme-toggle__trigger {
        background: var(--theme-surface, #{$gray-800});
        border-color: var(--theme-border, #{$gray-600});
        color: var(--theme-on-surface, #{$gray-200});

        &:hover {
          background: var(--theme-surface-variant, #{$gray-700});
        }
      }

      .theme-toggle__menu {
        background: var(--theme-surface, #{$gray-800});
        border-color: var(--theme-border, #{$gray-600});
      }
    }

    &__option {
      color: var(--theme-on-surface, #{$gray-200});

      &:hover {
        background: var(--theme-surface-variant, #{$gray-700});
      }

      &:focus {
        background: var(--theme-surface-variant, #{$gray-700});
      }

      &:not(:last-child) {
        border-bottom-color: var(--theme-divider, #{$gray-700});
      }
    }
  }
}

// Animation for theme transition
.theme-toggle__icon {
  animation: themeIconRotate 0.3s ease;
}

@keyframes themeIconRotate {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(0.8);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .theme-toggle {
    &--dropdown {
      .theme-toggle__trigger {
        min-width: 100px;
        padding: 0.5rem;
      }

      .theme-toggle__label {
        display: none;
      }
    }
  }
}
