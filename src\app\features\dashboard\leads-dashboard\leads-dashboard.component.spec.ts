import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LeadsDashboardComponent } from './leads-dashboard.component';
import { RouterTestingModule } from '@angular/router/testing';

describe('LeadsDashboardComponent', () => {
  let component: LeadsDashboardComponent;
  let fixture: ComponentFixture<LeadsDashboardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LeadsDashboardComponent, RouterTestingModule]
    }).compileComponents();

    fixture = TestBed.createComponent(LeadsDashboardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have metric cards', () => {
    expect(component.metricCards.length).toBeGreaterThan(0);
  });

  it('should have pipeline stages', () => {
    expect(component.pipelineStages.length).toBeGreaterThan(0);
  });
});
