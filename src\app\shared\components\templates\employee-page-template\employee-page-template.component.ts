import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

// Import organisms and molecules
import { EmployeesListComponent } from '../../organisms/employees-list/employees-list.component';

// Import atoms
import { ButtonComponent } from '../../atoms/button/button.component';
import { SearchInputComponent } from '../../atoms/search-input/search-input.component';

// Import interfaces
import { Employee } from '../../../../core/state/employees/employees.state';
import { EmployeeFormData } from '../../../../core/models/employee-extended.interface';
import { Department } from '../../../../core/models/department.interface';
import { Designation } from '../../../../core/models/employee-extended.interface';

export type EmployeePageView = 'list' | 'create' | 'edit' | 'detail' | 'details-manager';

@Component({
  selector: 'app-employee-page-template',
  standalone: true,
  imports: [
    CommonModule,
    EmployeesListComponent,
    ButtonComponent,
    SearchInputComponent
  ],
  templateUrl: './employee-page-template.component.html',
  styleUrls: ['./employee-page-template.component.scss']
})
export class EmployeePageTemplateComponent {
  @Input() currentView: EmployeePageView = 'list';
  @Input() employees: Employee[] = [];
  @Input() selectedEmployee: Employee | null = null;
  @Input() departments: Department[] = [];
  @Input() designations: Designation[] = [];
  @Input() isLoading: boolean = false;
  @Input() error: string | null = null;
  @Input() pageTitle: string = 'Employee Management';
  @Input() showStats: boolean = true;
  @Input() showSearch: boolean = true;
  @Input() showActions: boolean = true;

  // Statistics
  @Input() totalEmployees: number = 0;
  @Input() activeEmployees: number = 0;
  @Input() newHires: number = 0;
  @Input() onLeave: number = 0;

  // Events
  @Output() viewChange = new EventEmitter<EmployeePageView>();
  @Output() employeeCreate = new EventEmitter<EmployeeFormData>();
  @Output() employeeUpdate = new EventEmitter<{ id: number; data: EmployeeFormData }>();
  @Output() employeeDelete = new EventEmitter<number>();
  @Output() employeeSelect = new EventEmitter<Employee>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() refreshData = new EventEmitter<void>();

  onViewChange(view: EmployeePageView): void {
    this.viewChange.emit(view);
  }

  onCreateEmployee(): void {
    this.onViewChange('create');
  }

  onEditEmployee(employee: Employee): void {
    this.employeeSelect.emit(employee);
    this.onViewChange('edit');
  }

  onViewEmployee(employee: Employee): void {
    this.employeeSelect.emit(employee);
    this.onViewChange('detail');
  }

  onManageEmployeeDetails(employee: Employee): void {
    this.employeeSelect.emit(employee);
    this.onViewChange('details-manager');
  }

  onDeleteEmployee(employeeId: number): void {
    this.employeeDelete.emit(employeeId);
  }

  onFormSubmit(formData: EmployeeFormData): void {
    if (this.currentView === 'create') {
      this.employeeCreate.emit(formData);
    } else if (this.currentView === 'edit' && this.selectedEmployee) {
      this.employeeUpdate.emit({
        id: this.selectedEmployee.id,
        data: formData
      });
    }
  }

  onFormCancel(): void {
    this.onViewChange('list');
  }

  onSearch(query: string): void {
    this.searchChange.emit(query);
  }

  onRefresh(): void {
    this.refreshData.emit();
  }

  get pageSubtitle(): string {
    switch (this.currentView) {
      case 'create':
        return 'Add New Employee';
      case 'edit':
        return `Edit ${this.selectedEmployee?.first_name} ${this.selectedEmployee?.last_name}`;
      case 'detail':
        return `${this.selectedEmployee?.first_name} ${this.selectedEmployee?.last_name} Details`;
      case 'details-manager':
        return `Manage ${this.selectedEmployee?.first_name} ${this.selectedEmployee?.last_name} Details`;
      default:
        return 'Manage your organization\'s employees';
    }
  }

  get showBackButton(): boolean {
    return this.currentView !== 'list';
  }

  get isFormView(): boolean {
    return this.currentView === 'create' || this.currentView === 'edit';
  }

  get isEditMode(): boolean {
    return this.currentView === 'edit';
  }

  get isDetailsManagerView(): boolean {
    return this.currentView === 'details-manager';
  }

  get statsData() {
    return [
      {
        title: 'Total Employees',
        value: this.totalEmployees,
        icon: 'users',
        color: 'primary',
        trend: null
      },
      {
        title: 'Active',
        value: this.activeEmployees,
        icon: 'user-check',
        color: 'success',
        trend: null
      },
      {
        title: 'New Hires',
        value: this.newHires,
        icon: 'user-plus',
        color: 'info',
        trend: null
      },
      {
        title: 'On Leave',
        value: this.onLeave,
        icon: 'user-minus',
        color: 'warning',
        trend: null
      }
    ];
  }
}
