import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-projects',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="projects-container">
      <div class="projects-header">
        <h3 class="section-title">Projects</h3>
        <div class="header-actions">
          <button class="action-btn">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>

      <div class="projects-content">
        <div class="project-card">
          <div class="project-info">
            <div class="project-avatar">
              <img src="assets/images/avatars/avatar-2.svg" alt="<PERSON>">
            </div>
            <div class="project-details">
              <h4 class="project-title">Office Redesign</h4>
              <p class="project-subtitle"><PERSON></p>
              <p class="project-date">14 Jan 2023</p>
            </div>
          </div>

          <div class="project-progress">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="75"></div>
            </div>
            <div class="progress-stats">
              <div class="progress-percentage">75%</div>
              <div class="team-members">
                <div class="member-avatar">AL</div>
                <div class="member-avatar">SR</div>
                <div class="member-avatar">JD</div>
              </div>
            </div>
          </div>
        </div>

        <div class="project-card">
          <div class="project-info">
            <div class="project-avatar">
              <img src="assets/images/avatars/avatar-3.svg" alt="Anthony Lewis">
            </div>
            <div class="project-details">
              <h4 class="project-title">Mobile App</h4>
              <p class="project-subtitle">Anthony Lewis</p>
              <p class="project-date">14 Jan 2023</p>
            </div>
          </div>

          <div class="project-progress">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="45" style="background-color: #20c997;"></div>
            </div>
            <div class="progress-stats">
              <div class="progress-percentage">45%</div>
              <div class="team-members">
                <div class="member-avatar">AL</div>
                <div class="member-avatar">SR</div>
                <div class="member-avatar">JD</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .projects-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .projects-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e9ecef;
    }

    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }

    .action-btn {
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      font-size: 16px;
    }

    .projects-content {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .project-card {
      padding: 16px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
    }

    .project-info {
      display: flex;
      margin-bottom: 16px;
    }

    .project-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 16px;
      background-color: #e9ecef;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .project-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .project-details {
      flex: 1;
    }

    .project-title {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #343a40;
    }

    .project-subtitle {
      margin: 0 0 4px 0;
      font-size: 14px;
      color: #6c757d;
    }

    .project-date {
      margin: 0;
      font-size: 12px;
      color: #adb5bd;
    }

    .project-progress {
      margin-top: 8px;
    }

    .progress-bar {
      height: 6px;
      background-color: #e9ecef;
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 8px;
    }

    .progress-fill {
      height: 100%;
      background-color: #ff6b35;
    }

    .progress-stats {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .progress-percentage {
      font-size: 14px;
      font-weight: 600;
      color: #343a40;
    }

    .team-members {
      display: flex;
    }

    .member-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #ff6b35;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: 600;
      margin-left: -8px;
      border: 2px solid white;
    }

    .member-avatar:first-child {
      margin-left: 0;
    }
  `]
})
export class EmployeeProjectsComponent {
  @Input() employee: any;
}
