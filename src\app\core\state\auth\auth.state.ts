/**
 * NgRx Signals Auth State
 *
 * This file implements authentication state management using NgRx Signals.
 *
 * IMPORTANT: Before using this implementation, make sure to:
 * 1. Install the required packages:
 *    npm install @ngrx/signals @ngrx/effects @ngrx/store --force
 *
 * 2. Update your Angular version if needed:
 *    npm install @angular/core@latest @angular/common@latest --force
 *
 * 3. If you encounter build issues with @angular-architects/native-federation,
 *    you may need to update that package or use a compatible version.
 */

import { patchState, signalStore, withComputed, withMethods, withState } from '@ngrx/signals';
import { computed, inject } from '@angular/core';
import { User } from '../../models/user.interface';
import { AuthService } from '../../services/auth.service';
import { JwtService } from '../../services/jwt.service';
import { Router } from '@angular/router';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { catchError, finalize, map, of, pipe, switchMap, tap } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';

// Define the auth state interface
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Define the initial state
export const initialAuthState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null
};

// Create the auth store
export const AuthStore = signalStore(
  { providedIn: 'root' },
  withState(initialAuthState),
  withComputed((state) => ({
    // Computed properties
    fullName: computed(() => {
      const user = state.user();
      if (!user) return '';
      return `${user.first_name} ${user.last_name}`;
    }),
    isAdmin: computed(() => {
      const user = state.user();
      return user?.is_staff || false;
    })
  })),
  withMethods((store, authService = inject(AuthService), router = inject(Router)) => ({
    // Initialize the auth state
    init() {
      const token = authService.getAccessToken();
      if (token) {
        // Check if token is valid without making API calls
        const jwtService = inject(JwtService);
        if (!jwtService.isTokenExpired(token)) {
          // Token is valid, check if we have user data in storage
          const userJson = localStorage.getItem('user');
          if (userJson) {
            try {
              const user = JSON.parse(userJson);
              patchState(store, {
                user,
                isAuthenticated: true,
                error: null
              });
            } catch (e) {
              console.error('Error parsing user from storage', e);
            }
          }

          // Set authenticated state even if we don't have user data
          // User data will be fetched when needed (e.g., profile page)
          patchState(store, { isAuthenticated: true });
        } else {
          // Token is expired, check if we can refresh it
          const refreshToken = authService.getRefreshToken();
          if (refreshToken) {
            patchState(store, { isLoading: true });
            authService.refreshToken().subscribe({
              next: () => {
                patchState(store, {
                  isAuthenticated: true,
                  isLoading: false,
                  error: null
                });
              },
              error: () => {
                patchState(store, {
                  user: null,
                  isAuthenticated: false,
                  isLoading: false,
                  error: 'Session expired. Please login again.'
                });
                authService.logout();
              }
            });
          } else {
            // No refresh token, user needs to login again
            authService.logout();
          }
        }
      }
    },

    // Login method
    login: rxMethod<{ username: string; password: string }>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap(({ username, password }) =>
          authService.login({ username, password }).pipe(
            map((response) => {
              patchState(store, {
                user: response.user || null,
                isAuthenticated: true,
                isLoading: false,
                error: null
              });
              router.navigate(['/app/dashboard']);
              return response;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || 'Login failed. Please try again.';
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Register method
    register: rxMethod<any>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((registrationData) =>
          authService.register(registrationData).pipe(
            map((response) => {
              if (response.user) {
                // If tokens are included in the response
                if ('access' in response && 'refresh' in response) {
                  authService.storeTokens(response as any);
                }

                authService.storeUser(response.user);
                patchState(store, {
                  user: response.user,
                  isAuthenticated: true,
                  isLoading: false,
                  error: null
                });
                router.navigate(['/app/dashboard']);
              } else {
                router.navigate(['/login'], {
                  queryParams: { registered: 'true' }
                });
              }
              return response;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || 'Registration failed. Please try again.';
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Logout method
    logout() {
      authService.logout();
      patchState(store, {
        user: null,
        isAuthenticated: false,
        error: null
      });
      router.navigate(['/login']);
    },

    // Clear error
    clearError() {
      patchState(store, { error: null });
    }
  }))
);
