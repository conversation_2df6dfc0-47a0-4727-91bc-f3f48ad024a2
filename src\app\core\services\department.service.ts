import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, catchError, throwError } from 'rxjs';
import { environment } from '../../../environments/environment';
import { API_ENDPOINTS } from '../constants/api.constants';
import { Department, DepartmentInput, PaginatedDepartmentsResponse, DepartmentListParams, DepartmentDetail } from '../models/department.interface';

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private apiUrl = environment.apiUrl;
  private departmentsEndpoint = API_ENDPOINTS.DEPARTMENTS.DEPARTMENTS;

  constructor(private http: HttpClient) {}

  /**
   * Get all departments with pagination and filters
   * @param params Query parameters for filtering and pagination
   * @returns Observable of paginated departments response
   */
  getDepartments(params: DepartmentListParams = {}): Observable<PaginatedDepartmentsResponse> {
    let httpParams = new HttpParams();

    // Add pagination parameters
    if (params.page) {
      httpParams = httpParams.set('page', params.page.toString());
    }
    if (params.page_size) {
      httpParams = httpParams.set('page_size', params.page_size.toString());
    }

    // Add search parameter
    if (params.search) {
      httpParams = httpParams.set('search', params.search);
    }

    // Add ordering parameter
    if (params.ordering) {
      httpParams = httpParams.set('ordering', params.ordering);
    }

    // Add manager filter
    if (params.manager) {
      httpParams = httpParams.set('manager', params.manager.toString());
    }

    // Add manager_name filter
    if (params.manager_name) {
      httpParams = httpParams.set('manager_name', params.manager_name);
    }

    // Add status filter
    if (params.status) {
      httpParams = httpParams.set('status', params.status);
    }

    // Add name filter
    if (params.name) {
      httpParams = httpParams.set('name', params.name);
    }

    return this.http.get<PaginatedDepartmentsResponse>(`${this.apiUrl}${this.departmentsEndpoint}`, { params: httpParams })
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all departments without pagination (for dropdowns, etc.)
   * @returns Observable of Department array
   */
  getAllDepartments(): Observable<Department[]> {
    // Use a large page size to get all departments in one request
    return this.getDepartments({ page_size: 100 })
      .pipe(
        catchError(this.handleError),
        // Extract the results array from the paginated response
        // This simplifies usage in components that just need the list
        // without pagination details
        (response$: Observable<PaginatedDepartmentsResponse>) =>
          new Observable<Department[]>(observer => {
            response$.subscribe({
              next: (response) => observer.next(response.results),
              error: (error) => observer.error(error),
              complete: () => observer.complete()
            });
          })
      );
  }

  /**
   * Get department by ID
   * @param id The department ID
   * @returns Observable of Department
   */
  getDepartmentById(id: number): Observable<Department> {
    return this.http.get<Department>(`${this.apiUrl}${this.departmentsEndpoint}${id}/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get department details by ID (includes employees list)
   * @param id The department ID
   * @returns Observable of DepartmentDetail
   */
  getDepartmentDetail(id: number): Observable<DepartmentDetail> {
    return this.http.get<DepartmentDetail>(`${this.apiUrl}${this.departmentsEndpoint}${id}/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Create a new department
   * @param departmentData The department data
   * @returns Observable of the created Department
   */
  createDepartment(departmentData: DepartmentInput): Observable<Department> {
    return this.http.post<Department>(`${this.apiUrl}${this.departmentsEndpoint}`, departmentData)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update an existing department
   * @param id The department ID
   * @param departmentData The updated department data
   * @returns Observable of the updated Department
   */
  updateDepartment(id: number, departmentData: DepartmentInput): Observable<Department> {
    return this.http.put<Department>(`${this.apiUrl}${this.departmentsEndpoint}${id}/`, departmentData)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Delete a department
   * @param id The department ID
   * @returns Observable of void
   */
  deleteDepartment(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}${this.departmentsEndpoint}${id}/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Handle HTTP errors
   * @param error The HTTP error response
   * @returns Observable with error
   */
  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = error.error?.detail || error.error?.message || `Error Code: ${error.status}, Message: ${error.message}`;
    }

    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
