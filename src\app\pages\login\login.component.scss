.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 0;
  position: relative;
  overflow: hidden;
}

.login-wrapper {
  display: flex;
  width: 100%;
  height: 100vh;
  background-color: white;
  overflow: hidden;
  position: relative;
}

.login-left {
  flex: 1;
  background: linear-gradient(135deg, #ff6b35 0%, #e85a2a 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  position: relative;
  overflow: hidden;
}

.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background-color: white;
}

.login-left-content {
  max-width: 450px;
  position: relative;
  z-index: 1;
  padding: 2rem;
}

.headline {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.subheadline {
  font-size: 1.2rem;
  margin-top: 2rem;
  line-height: 1.5;
  text-align: center;
}

.decorative-circle {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  top: 20%;
  right: 10%;
  width: 100px;
  height: 100px;
}

.circle-2 {
  bottom: 15%;
  left: 5%;
  width: 150px;
  height: 150px;
}

.login-image {
  margin: 2rem 0;
  text-align: center;
  position: relative;
  z-index: 1;
  width: 90%;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  transition: all 0.5s ease;
}

.login-image:hover {
  transform: translateY(-5px);
}

.login-image img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 12px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.5s ease;
}

.login-image:hover img {
  box-shadow: 0 20px 40px rgba(232, 90, 42, 0.2);
  transform: scale(1.02);
}

.login-right-content {
  width: 100%;
  max-width: 400px;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

.logo-text {
  font-size: 2.2rem;
  font-weight: 700;
  color: #343a40;
  margin: 0 auto;
  text-align: center;
}

.logo-text .highlight {
  color: #ff6b35;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.login-header p {
  color: #666;
  font-size: 0.9rem;
}

.alert {
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  position: relative;

  i {
    margin-right: 0.5rem;
    font-size: 1rem;
  }

  .alert-close {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0.7;

    &:hover {
      opacity: 1;
    }
  }
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  border-left: 3px solid #28a745;
  color: #155724;

  i {
    color: #28a745;
  }
}

.alert-error {
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 3px solid #dc3545;
  color: #721c24;

  i {
    color: #dc3545;
  }
}

.login-form {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
}

.input-wrapper input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.input-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.85rem;
}

.remember-me {
  display: flex;
  align-items: center;
}

.remember-me input {
  margin-right: 0.5rem;
}

.forgot-password {
  color: #ff6b35;
  text-decoration: none;
}

.btn-login {
  width: 100%;
  padding: 0.75rem;
  background-color: #ff6b35;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background-color: darken(#ff6b35, 10%);
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    opacity: 0.7;
  }

  i {
    margin-right: 0.5rem;
  }
}

.account-link {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 0.85rem;
  color: #666;
}

.account-link a {
  color: #ff6b35;
  text-decoration: none;
  margin-left: 0.5rem;
  font-weight: 500;
}

.account-link a:hover {
  text-decoration: underline;
}

.login-divider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.login-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #ddd;
}

.login-divider span {
  position: relative;
  background-color: white;
  padding: 0 10px;
  font-size: 0.85rem;
  color: #666;
}

.social-login {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.btn-social {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  background-color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-social:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.btn-social.facebook {
  color: #1877f2;
}

.btn-social.google {
  color: #ea4335;
}

.btn-social.apple {
  color: #000;
}

.login-footer {
  text-align: center;
  font-size: 0.8rem;
  color: #999;
  margin-top: 2rem;
}

@media (max-width: 992px) {
  .login-wrapper {
    flex-direction: column;
  }

  .login-left, .login-right {
    padding: 2rem;
  }
}

@media (max-width: 576px) {
  .login-container {
    padding: 1rem;
  }

  .login-left, .login-right {
    padding: 1.5rem;
  }

  .login-header h2 {
    font-size: 1.5rem;
  }
}


