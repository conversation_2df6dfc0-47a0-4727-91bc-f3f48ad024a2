@import '../../../../../styles/variables/_colors';

.employee-management-dashboard {
  padding: 2rem;
  background-color: $background-light;
  min-height: 100vh;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid $border-light;

    .dashboard-title {
      font-size: 2rem;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin: 0;
    }

    .dashboard-actions {
      display: flex;
      gap: 1rem;
    }
  }

  .section-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid $border-light;
    overflow-x: auto;
    padding-bottom: 0;

    .section-tab {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 1rem 1.5rem;
      background: none;
      border: none;
      border-bottom: 3px solid transparent;
      color: $text-secondary;
      font-weight: $font-weight-medium;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
      min-width: fit-content;

      .material-icons {
        font-size: 1.2rem;
      }

      .tab-label {
        font-size: 0.9rem;
      }

      &:hover {
        color: $primary;
        background-color: rgba($primary, 0.05);
      }

      &.active {
        color: $primary;
        border-bottom-color: $primary;
        background-color: rgba($primary, 0.1);
      }
    }
  }

  .dashboard-content {
    .overview-section {
      .statistics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;

        .stat-card {
          background: $white;
          padding: 1.5rem;
          border-radius: 16px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          display: flex;
          align-items: center;
          gap: 1rem;
          transition: transform 0.3s ease, box-shadow 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
          }

          .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: linear-gradient(135deg, $primary, $primary-light);
            display: flex;
            align-items: center;
            justify-content: center;

            .material-icons {
              color: $white;
              font-size: 1.8rem;
            }
          }

          .stat-content {
            h3 {
              font-size: 2rem;
              font-weight: $font-weight-bold;
              color: $text-primary;
              margin: 0 0 0.25rem 0;
            }

            p {
              color: $text-secondary;
              margin: 0;
              font-size: 0.9rem;
            }
          }
        }
      }

      .employee-overview {
        .overview-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 1.5rem;

          .overview-card {
            background: $white;
            padding: 1.5rem;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

            h3 {
              font-size: 1.1rem;
              font-weight: $font-weight-semibold;
              color: $text-primary;
              margin: 0 0 1rem 0;
              padding-bottom: 0.5rem;
              border-bottom: 1px solid $border-light;
            }

            .loading {
              color: $text-secondary;
              font-style: italic;
            }

            .no-data {
              color: $text-secondary;
              font-style: italic;
              margin: 0;
            }

            .salary-info {
              .amount {
                font-size: 1.8rem;
                font-weight: $font-weight-bold;
                color: $primary;
                margin: 0 0 0.5rem 0;
              }

              .date {
                color: $text-secondary;
                margin: 0;
                font-size: 0.9rem;
              }
            }

            .hike-list, .leave-list {
              .hike-item, .leave-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.75rem 0;
                border-bottom: 1px solid $border-light;

                &:last-child {
                  border-bottom: none;
                }

                .hike-amount {
                  font-weight: $font-weight-semibold;
                  color: $success;
                }

                .hike-date, .leave-dates {
                  color: $text-secondary;
                  font-size: 0.9rem;
                }

                .hike-status, .leave-status {
                  padding: 0.25rem 0.75rem;
                  border-radius: 12px;
                  font-size: 0.8rem;
                  font-weight: $font-weight-medium;

                  &.status-pending {
                    background-color: rgba($warning, 0.1);
                    color: $warning;
                  }

                  &.status-approved {
                    background-color: rgba($success, 0.1);
                    color: $success;
                  }

                  &.status-rejected {
                    background-color: rgba($error, 0.1);
                    color: $error;
                  }
                }

                .leave-type {
                  font-weight: $font-weight-medium;
                  color: $text-primary;
                }
              }
            }

            .okr-list {
              .okr-item {
                padding: 0.75rem 0;
                border-bottom: 1px solid $border-light;

                &:last-child {
                  border-bottom: none;
                }

                .okr-title {
                  display: block;
                  font-weight: $font-weight-medium;
                  color: $text-primary;
                  margin-bottom: 0.5rem;
                }

                .okr-progress {
                  display: flex;
                  align-items: center;
                  gap: 0.75rem;

                  .progress-bar {
                    flex: 1;
                    height: 8px;
                    background-color: $background-light;
                    border-radius: 4px;
                    overflow: hidden;

                    .progress-fill {
                      height: 100%;
                      background: linear-gradient(90deg, $primary, $primary-light);
                      transition: width 0.3s ease;
                    }
                  }

                  .progress-text {
                    font-size: 0.9rem;
                    font-weight: $font-weight-medium;
                    color: $text-secondary;
                    min-width: 40px;
                  }
                }
              }
            }
          }
        }
      }

      .loading-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        color: $text-secondary;

        .material-icons {
          font-size: 3rem;
          margin-bottom: 1rem;
          animation: spin 2s linear infinite;
        }

        p {
          margin: 0;
          font-size: 1.1rem;
        }
      }
    }

    .section-content {
      background: $white;
      border-radius: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .error-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        color: $error;

        .material-icons {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        p {
          margin: 0;
          font-size: 1.1rem;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .employee-management-dashboard {
    padding: 1rem;

    .dashboard-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .dashboard-title {
        font-size: 1.5rem;
      }
    }

    .section-tabs {
      .section-tab {
        padding: 0.75rem 1rem;

        .tab-label {
          display: none;
        }
      }
    }

    .overview-section {
      .statistics-grid {
        grid-template-columns: 1fr;
      }

      .employee-overview {
        .overview-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
