import { Component, OnInit, On<PERSON><PERSON>roy, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Subject, takeUntil, filter, catchError, of } from 'rxjs';
import { ModalComponent } from '../../components/atoms/modal/modal.component';
import { ButtonComponent } from '../../components/atoms/button/button.component';
import { IconComponent } from '../../components/atoms/icon/icon.component';
import { SearchableDropdownComponent, DropdownOption } from '../../components/molecules/searchable-dropdown/searchable-dropdown.component';
import { ModalService } from '../../services/modal.service';
import { ToastService } from '../../services/toast.service';
import { DepartmentsStore, EmployeesStore } from '../../../core/state';
import { DepartmentInput } from '../../../core/models/department.interface';
import { Employee } from '../../../core/state/employees/employees.state';

@Component({
  selector: 'app-add-department-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ModalComponent,
    ButtonComponent,
    IconComponent,
    SearchableDropdownComponent
  ],
  templateUrl: './add-department-modal.component.html',
  styleUrl: './add-department-modal.component.scss'
})
export class AddDepartmentModalComponent implements OnInit, OnDestroy {
  private fb = inject(FormBuilder);
  private modalService = inject(ModalService);
  private toastService = inject(ToastService);
  private departmentsStore = inject(DepartmentsStore);
  private employeesStore = inject(EmployeesStore);

  // Cleanup subject
  private destroy$ = new Subject<void>();

  departmentForm!: FormGroup;
  isSubmitting = false;
  isLoadingEmployees = false;
  employeeLoadError: string | null = null;

  // Dropdown options
  statusOptions: DropdownOption[] = [
    {
      value: 'active',
      label: 'Active',
      icon: 'fa fa-check-circle',
      subtitle: 'Department is operational'
    },
    {
      value: 'inactive',
      label: 'Inactive',
      icon: 'fa fa-times-circle',
      subtitle: 'Department is not operational'
    }
  ];

  managerOptions: DropdownOption[] = [];

  constructor() {
    // Load employees for manager dropdown using effect
    effect(() => {
      const employees = this.employeesStore.employees();
      const isLoadingEmployees = this.employeesStore.isLoading();
      const employeeError = this.employeesStore.error();

      // Debug logging (can be removed in production)
      if (employees?.length || employeeError) {
        console.log('Add Department Modal - Employee Effect Triggered:', {
          employeesCount: employees?.length || 0,
          isLoading: isLoadingEmployees,
          error: employeeError
        });
      }

      this.isLoadingEmployees = isLoadingEmployees;
      this.employeeLoadError = employeeError;

      if (employeeError) {
        // Show error state in dropdown
        this.managerOptions = [{
          value: null,
          label: 'No Manager',
          icon: 'fa fa-user-slash',
          subtitle: 'Department will have no manager'
        }];
      } else if (employees && employees.length > 0) {
        // Filter active employees only for manager selection
        const activeEmployees = employees.filter(emp =>
          emp.full_name &&
          emp.status === 'active'
        );

        // Debug: Log employee processing
        console.log(`Add Department Modal - Processing ${employees.length} employees, ${activeEmployees.length} active`);

        this.managerOptions = [
          {
            value: null,
            label: 'No Manager',
            icon: 'fa fa-user-slash',
            subtitle: 'Department will have no manager'
          },
          ...activeEmployees
            .map(emp => ({
              value: emp.id,
              label: `${emp.full_name}`.trim(),
              subtitle: this.getEmployeeSubtitle(emp),
              avatar: emp.profile_picture_url || emp.avatar,
              icon: emp.profile_picture_url || emp.avatar ? undefined : 'fa fa-user'
            }))
        ];

        console.log(`Add Department Modal - Created ${this.managerOptions.length} manager options`);
      } else if (!isLoadingEmployees) {
        // Clear options if no employees are loaded and not loading
        this.managerOptions = [{
          value: null,
          label: 'No Manager',
          icon: 'fa fa-user-slash',
          subtitle: 'Department will have no manager'
        }];
      }
    });
  }

  ngOnInit(): void {
    this.initForm();
    this.loadEmployees();
    this.setupModalListeners();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Initialize the form with validation according to API schema
   */
  private initForm(): void {
    this.departmentForm = this.fb.group({
      name: ['', [
        Validators.required,
        Validators.minLength(1),
        Validators.maxLength(100)
      ]],
      description: [''], // Optional field
      manager: [null], // Nullable integer (employee ID)
      status: ['active', Validators.required] // Required enum
    });
  }

  /**
   * Load employees for manager dropdown with enhanced error handling
   */
  private loadEmployees(): void {
    console.log('Add Department Modal - Loading employees...');

    // Reset error state
    this.employeeLoadError = null;

    // Load all active employees with a larger page size to get comprehensive list
    this.employeesStore.loadEmployees({
      pageSize: 100, // Load more employees for manager selection
      page: 1
    });
  }

  /**
   * Generate subtitle for employee dropdown options with enhanced information
   * @param employee The employee object
   * @returns Formatted subtitle string
   */
  private getEmployeeSubtitle(employee: Employee): string {
    const parts: string[] = [];

    // Add position if available
    if (employee.position) {
      parts.push(employee.position);
    }

    // Add department information
    if (employee.department_name) {
      parts.push(employee.department_name);
    } else if (typeof employee.department === 'string' && employee.department) {
      parts.push(employee.department);
    } else if (typeof employee.department === 'object' && employee.department?.name) {
      parts.push(employee.department.name);
    }

    // Add employee ID for identification
    if (employee.id) {
      parts.push(`ID: ${employee.id}`);
    }

    return parts.length > 0 ? parts.join(' • ') : 'Employee';
  }

  /**
   * Get placeholder text for manager dropdown with enhanced states
   * @returns Appropriate placeholder text based on loading state
   */
  getManagerPlaceholder(): string {
    if (this.isLoadingEmployees) {
      return 'Loading employees...';
    }

    if (this.employeeLoadError) {
      return 'Error loading employees';
    }

    if (this.managerOptions.length === 1) {
      return 'No employees available';
    }

    return 'Select a manager (optional)';
  }

  /**
   * Retry loading employees when there's an error
   */
  retryLoadEmployees(): void {
    this.loadEmployees();
  }

  /**
   * Setup modal event listeners
   */
  private setupModalListeners(): void {
    // Listen for modal open events to reset form and reload employees
    this.modalService.modals$
      .pipe(
        takeUntil(this.destroy$),
        filter(modals => modals['add-department-modal'] === true)
      )
      .subscribe(() => {
        this.resetForm();
        // Reload employees when modal opens to ensure fresh data
        this.loadEmployees();
      });
  }

  /**
   * Reset form to initial state
   */
  private resetForm(): void {
    this.departmentForm.reset({
      name: '',
      description: '',
      manager: null,
      status: 'active'
    });
    this.isSubmitting = false;
  }

  /**
   * Check if a field is invalid and has been touched
   * @param field The form field name
   * @returns True if the field is invalid and touched
   */
  isFieldInvalid(field: string): boolean {
    const formControl = this.departmentForm.get(field);
    return !!formControl && formControl.invalid && (formControl.dirty || formControl.touched);
  }

  /**
   * Submit the form
   */
  onSubmit(): void {
    if (this.departmentForm.invalid) {
      // Mark all fields as touched to show validation errors
      Object.keys(this.departmentForm.controls).forEach(key => {
        this.departmentForm.get(key)?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const formValue = this.departmentForm.value;

    // Prepare department data according to API schema
    const departmentData: DepartmentInput = {
      name: formValue.name.trim(),
      description: formValue.description?.trim() || undefined,
      manager: formValue.manager || null,
      status: formValue.status
    };

    // Call the store method to create the department
    this.departmentsStore.createDepartment(departmentData);

    // Monitor store state for success/error handling
    this.handleSubmissionResponse();
  }

  /**
   * Handle the submission response from the store
   */
  private handleSubmissionResponse(): void {
    // Set up a watcher for the store state with proper error handling
    const stateWatcher = setInterval(() => {
      const currentError = this.departmentsStore.error();
      const isLoading = this.departmentsStore.isLoading();

      if (currentError) {
        clearInterval(stateWatcher);
        this.toastService.error('Failed to create department: ' + currentError);
        this.isSubmitting = false;
        return;
      }

      // Check if operation completed successfully
      if (!isLoading && this.isSubmitting) {
        clearInterval(stateWatcher);
        this.toastService.success('Department created successfully!');
        this.closeModal();
        this.isSubmitting = false;
      }
    }, 100);

    // Cleanup watcher after 10 seconds to prevent memory leaks
    setTimeout(() => {
      clearInterval(stateWatcher);
      if (this.isSubmitting) {
        this.isSubmitting = false;
        this.toastService.error('Request timeout. Please try again.');
      }
    }, 10000);
  }

  /**
   * Close the modal
   */
  closeModal(): void {
    this.modalService.close('add-department-modal');
  }

  /**
   * Get field error message
   * @param field The form field name
   * @returns The error message or null
   */
  getFieldError(field: string): string | null {
    const formControl = this.departmentForm.get(field);
    if (!formControl || !formControl.errors || !formControl.touched) {
      return null;
    }

    const errors = formControl.errors;
    if (errors['required']) {
      return `${this.getFieldLabel(field)} is required`;
    }
    if (errors['minlength']) {
      return `${this.getFieldLabel(field)} must be at least ${errors['minlength'].requiredLength} character(s)`;
    }
    if (errors['maxlength']) {
      return `${this.getFieldLabel(field)} cannot exceed ${errors['maxlength'].requiredLength} characters`;
    }
    return null;
  }

  /**
   * Get field label for error messages
   * @param field The form field name
   * @returns The field label
   */
  private getFieldLabel(field: string): string {
    const labels: { [key: string]: string } = {
      name: 'Department name',
      description: 'Description',
      manager: 'Manager',
      status: 'Status'
    };
    return labels[field] || field;
  }
}
