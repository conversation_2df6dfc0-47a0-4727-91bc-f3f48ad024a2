<div class="modal-overlay" *ngIf="isOpen" (click)="closeOnOverlayClick ? close() : null" [@overlayAnimation]>
  <div class="modal-container" [ngClass]="size" (click)="$event.stopPropagation()" [@modalAnimation]>
    <div class="modal-header">
      <h2 class="modal-title">{{ title }}</h2>
      <button class="modal-close" (click)="close()">
        <i class="fa fa-times"></i>
      </button>
    </div>
    <div class="modal-body">
      <ng-content></ng-content>
    </div>
    <div class="modal-footer" *ngIf="showFooter">
      <ng-content select="[modal-footer]"></ng-content>
    </div>
  </div>
</div>
