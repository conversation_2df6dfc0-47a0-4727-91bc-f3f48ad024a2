@use 'sass:color';
@import '../../../../styles/variables/_colors';

:host {
  display: block;
  padding: 0;
  background: $background-light;
  min-height: 100vh;
}

.department-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

// Loading and Error States
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  background: $card-bg;
  border-radius: 16px;
  box-shadow: 0 4px 20px $shadow-color;
  margin: 2rem 0;
  padding: 3rem 2rem;

  .spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba($primary, 0.1);
    border-top: 4px solid $primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba($primary, 0.2);
  }

  .error-icon {
    color: $danger;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 2px 4px rgba($danger, 0.2));
  }

  h3 {
    margin: 0 0 1rem 0;
    color: $text-primary;
    font-size: 1.5rem;
    font-weight: $font-weight-semibold;
  }

  p {
    color: $text-secondary;
    margin-bottom: 2rem;
    font-size: $font-size-base;
    line-height: $line-height-base;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Department Detail Container
.department-detail {
  // max-width: 1200px;
  margin: 0 auto;
}

// Breadcrumb Navigation
.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem 1.5rem;
  background: $card-bg;
  border-radius: 12px;
  box-shadow: 0 2px 8px $shadow-color;
  font-size: $font-size-sm;

  .breadcrumb-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: $primary;
    transition: all 0.3s ease;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-weight: $font-weight-medium;

    &:hover {
      color: $primary-dark;
      background: rgba($primary, 0.05);
      transform: translateY(-1px);
    }

    app-icon {
      margin-right: 0.5rem;
    }
  }

  .breadcrumb-separator {
    margin: 0 0.75rem;
    color: $text-secondary;
    font-weight: $font-weight-normal;
  }

  .breadcrumb-current {
    color: $text-primary;
    font-weight: $font-weight-semibold;
    padding: 0.5rem 0;
  }
}

// Header Section
.header-section {
  margin-bottom: 2rem;
  background: linear-gradient(135deg, $gray-700 0%, $gray-800 100%);
  border-radius: 20px;
  padding: 2.5rem;
  color: $white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba($gray-800, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba($white, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    position: relative;
    z-index: 1;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: 1.5rem;
    }
  }

  .title-section {
    flex: 1;

    .department-title {
      font-size: 2.5rem;
      font-weight: $font-weight-bold;
      margin: 0 0 1rem 0;
      color: $white;
      text-shadow: 0 2px 4px rgba($black, 0.2);
      line-height: $line-height-tight;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .status-badge {
      display: inline-flex;
      align-items: center;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: $font-size-sm;
      font-weight: $font-weight-semibold;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      background: rgba($white, 0.2);
      color: $white;
      backdrop-filter: blur(10px);
      border: 1px solid rgba($white, 0.3);
    }
  }

  .action-buttons {
    display: flex;
    gap: 0.75rem;
    flex-shrink: 0;

    @media (max-width: 768px) {
      justify-content: flex-end;
    }

    app-button {
      min-width: 120px;

      ::ng-deep .btn {
        background: rgba($white, 0.15);
        border: 1px solid rgba($white, 0.3);
        color: $white;
        backdrop-filter: blur(10px);
        font-weight: $font-weight-semibold;

        &:hover {
          background: rgba($white, 0.25);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba($black, 0.2);
        }

        &.btn-danger {
          background: rgba($danger, 0.8);
          border-color: rgba($danger, 0.9);

          &:hover {
            background: $danger;
          }
        }
      }
    }
  }
}

// Statistics Section
.statistics-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  .stat-card {
    background: $card-bg;
    border-radius: 16px;
    padding: 1.75rem;
    box-shadow: 0 4px 20px $shadow-color;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba($primary, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $primary 0%, color.adjust($primary, $lightness: 10%) 100%);
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 32px rgba($primary, 0.15);
      border-color: rgba($primary, 0.2);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 1.25rem;

      .stat-icon {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        position: relative;
        box-shadow: 0 4px 12px $shadow-color;

        &::before {
          content: '';
          position: absolute;
          inset: 0;
          border-radius: 16px;
          background: linear-gradient(135deg, transparent 0%, rgba($white, 0.2) 100%);
        }

        // Default (Total Employees)
        background: linear-gradient(135deg, $gray-600 0%, $gray-800 100%);
        color: $white;

        &.active {
          background: linear-gradient(135deg, $success 0%, color.adjust($success, $lightness: -10%) 100%);
        }

        &.inactive {
          background: linear-gradient(135deg, $danger 0%, color.adjust($danger, $lightness: -10%) 100%);
        }
      }

      .stat-info {
        flex: 1;

        h3 {
          font-size: 2rem;
          font-weight: $font-weight-bold;
          margin: 0 0 0.25rem 0;
          color: $text-primary;
          line-height: 1;
        }

        p {
          font-size: $font-size-sm;
          color: $text-secondary;
          margin: 0;
          font-weight: $font-weight-medium;
        }
      }

      .stat-trend {
        flex-shrink: 0;
        margin-left: auto;

        .trend-indicator {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: $font-size-xs;
          font-weight: $font-weight-semibold;
          padding: 0.375rem 0.75rem;
          border-radius: 12px;
          background: rgba($success, 0.1);
          color: $success;

          app-icon {
            font-size: 0.625rem;
          }
        }
      }
    }
  }
}

// Overview Card
.overview-card {
  margin-bottom: 2rem;
  border-radius: 20px;
  box-shadow: 0 6px 24px $shadow-color;
  background: $card-bg;
  padding: 2rem;
  border: 1px solid rgba($primary, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 32px $shadow-color-darker;
    transform: translateY(-2px);
  }

  .card-header {
    padding-bottom: 1.5rem;
    border-bottom: 2px solid $gray-100;
    margin-bottom: 2rem;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, $primary 0%, color.adjust($primary, $lightness: 10%) 100%);
      border-radius: 1px;
    }

    .card-title {
      font-size: 1.5rem;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 0.75rem;

      &::before {
        content: '';
        width: 8px;
        height: 8px;
        background: $primary;
        border-radius: 50%;
        box-shadow: 0 0 0 3px rgba($primary, 0.2);
      }
    }
  }

  .card-content {
    padding: 0;
  }

  .overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;

    .overview-item {
      padding: 1.5rem;
      background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
      border-radius: 16px;
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        border-color: rgba(255, 107, 53, 0.2);
      }

      label {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: var(--text-primary, #333);
        margin-bottom: 1rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &::before {
          content: '';
          width: 4px;
          height: 4px;
          background: var(--primary-color, #ff6b35);
          border-radius: 50%;
          margin-right: 0.5rem;
        }
      }

      p {
        margin: 0;
        color: var(--text-secondary, #666);
        line-height: 1.6;
        font-size: 0.95rem;
      }

      .manager-info {
        .manager-details {
          display: flex;
          align-items: center;
          gap: 1rem;
          padding: 1rem;
          background: var(--surface-color, #fff);
          border-radius: 12px;
          border: 1px solid #e9ecef;
          transition: all 0.3s ease;

          &:hover {
            border-color: rgba(255, 107, 53, 0.3);
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);
          }

          .manager-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            border: 2px solid var(--surface-color, #fff);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .manager-name {
            font-weight: 600;
            color: var(--text-primary, #333);
            font-size: 0.95rem;
          }
        }

        .no-manager {
          color: var(--text-secondary, #666);
          font-style: italic;
          padding: 1rem;
          text-align: center;
          background: rgba(248, 249, 250, 0.5);
          border-radius: 12px;
          border: 1px dashed #dee2e6;
        }
      }
    }
  }
}

// Employees Card
.employees-card {
  border-radius: 20px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  background: var(--surface-color, #fff);
  padding: 2rem;
  border: 1px solid rgba(255, 107, 53, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  .card-header {
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #f8f9fa;
    margin-bottom: 2rem;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, var(--primary-color, #ff6b35) 0%, #ff8c5a 100%);
      border-radius: 1px;
    }

    .card-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--text-primary, #333);
      margin: 0;
      display: flex;
      align-items: center;
      gap: 0.75rem;

      &::before {
        content: '';
        width: 8px;
        height: 8px;
        background: var(--primary-color, #ff6b35);
        border-radius: 50%;
        box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);
      }
    }
  }

  .card-content {
    padding: 0;
  }

  .employees-grid {
    display: grid;
    gap: 1rem;

    .employee-card {
      display: flex;
      align-items: center;
      gap: 1.25rem;
      padding: 1.5rem;
      background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
      border: 1px solid #e9ecef;
      border-radius: 16px;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(180deg, var(--primary-color, #ff6b35) 0%, #ff8c5a 100%);
        transform: scaleY(0);
        transition: transform 0.3s ease;
      }

      &:hover {
        background: var(--surface-color, #fff);
        border-color: rgba(255, 107, 53, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(255, 107, 53, 0.15);

        &::before {
          transform: scaleY(1);
        }

        .view-icon {
          color: var(--primary-color, #ff6b35);
          transform: translateX(4px);
        }
      }

      .employee-avatar {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        overflow: hidden;
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        border: 3px solid var(--surface-color, #fff);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .employee-info {
        flex: 1;
        min-width: 0;

        .employee-name {
          font-size: 1rem;
          font-weight: 600;
          margin: 0 0 0.25rem 0;
          color: var(--text-primary, #333);
          line-height: 1.3;
        }

        .employee-email {
          font-size: 0.875rem;
          margin: 0 0 0.25rem 0;
          color: var(--text-secondary, #666);
          opacity: 0.8;
        }

        .employee-position {
          font-size: 0.875rem;
          margin: 0 0 0.75rem 0;
          color: var(--text-secondary, #666);
          font-weight: 500;
        }

        .status-badge {
          display: inline-flex;
          align-items: center;
          padding: 0.375rem 0.75rem;
          border-radius: 20px;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.active {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color, #28a745);
            border: 1px solid rgba(40, 167, 69, 0.2);
          }

          &.inactive {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color, #dc3545);
            border: 1px solid rgba(220, 53, 69, 0.2);
          }
        }
      }

      .view-icon {
        color: var(--text-secondary, #666);
        transition: all 0.3s ease;
        flex-shrink: 0;
        margin-left: 1rem;
      }
    }
  }

  .no-employees {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
    border-radius: 16px;
    border: 2px dashed #dee2e6;

    .no-data-icon {
      color: var(--text-secondary, #666);
      margin-bottom: 1.5rem;
      opacity: 0.6;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }

    h3 {
      margin: 0 0 1rem 0;
      color: var(--text-primary, #333);
      font-size: 1.25rem;
      font-weight: 600;
    }

    p {
      color: var(--text-secondary, #666);
      margin: 0;
      font-size: 0.95rem;
      line-height: 1.5;
    }
  }
}

// Status Classes
.status-active {
  background: rgba(40, 167, 69, 0.1) !important;
  color: var(--success-color, #28a745) !important;
  border: 1px solid rgba(40, 167, 69, 0.2) !important;
}

.status-inactive {
  background: rgba(220, 53, 69, 0.1) !important;
  color: var(--danger-color, #dc3545) !important;
  border: 1px solid rgba(220, 53, 69, 0.2) !important;
}

.status-pending {
  background: rgba(255, 193, 7, 0.1) !important;
  color: var(--warning-color, #ffc107) !important;
  border: 1px solid rgba(255, 193, 7, 0.2) !important;
}

// Responsive Design
@media (max-width: 768px) {
  .department-detail {
    padding: 1rem;
  }

  .header-section {
    padding: 2rem 1.5rem;
    margin-bottom: 1.5rem;

    .header-content {
      gap: 1.5rem;
    }

    .title-section {
      .department-title {
        font-size: 2rem;
      }
    }

    .action-buttons {
      app-button {
        min-width: auto;
        flex: 1;
      }
    }
  }

  .statistics-section {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .overview-card,
  .employees-card {
    padding: 1.5rem;
    margin-bottom: 1.5rem;

    .overview-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  .employees-card {
    .employees-grid {
      .employee-card {
        padding: 1rem;
        gap: 1rem;

        .employee-avatar {
          width: 48px;
          height: 48px;
        }

        .employee-info {
          .employee-name {
            font-size: 0.9rem;
          }

          .employee-email,
          .employee-position {
            font-size: 0.8rem;
          }
        }
      }
    }
  }

  .breadcrumb {
    padding: 0.75rem 1rem;
    margin-bottom: 1.5rem;
  }
}
