import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

export interface DropdownOption {
  value: string | number;
  label: string;
  icon?: string;
}

@Component({
  selector: 'app-dropdown-select',
  standalone: true,
  imports: [CommonModule, IconComponent],
  template: `
    <div class="dropdown-container" [ngClass]="{'open': isOpen}">
      <button class="dropdown-toggle" (click)="toggleDropdown($event)">
        <span class="selected-text">{{ selectedLabel }}</span>
        <app-icon name="fa fa-chevron-down" size="sm" class="dropdown-icon"></app-icon>
      </button>
      <div class="dropdown-menu" *ngIf="isOpen">
        <div
          *ngFor="let option of options"
          class="dropdown-item"
          [ngClass]="{'active': option.value === selectedValue}"
          (click)="selectOption(option)">
          <app-icon *ngIf="option.icon" [name]="option.icon" size="sm" class="item-icon"></app-icon>
          <span>{{ option.label }}</span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dropdown-container {
      position: relative;
      display: inline-block;
      width: 100%;
    }

    .dropdown-toggle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 8px 12px;
      background-color: #fff;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      font-size: 14px;
      color: #495057;
      cursor: pointer;
      text-align: left;
    }

    .dropdown-toggle:hover {
      border-color: #ced4da;
    }

    .dropdown-icon {
      transition: transform 0.2s ease;
    }

    .open .dropdown-icon {
      transform: rotate(180deg);
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      left: 0;
      z-index: 1000;
      width: 100%;
      margin-top: 4px;
      padding: 8px 0;
      background-color: #fff;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-height: 240px;
      overflow-y: auto;
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      font-size: 14px;
      color: #495057;
      cursor: pointer;
    }

    .dropdown-item:hover {
      background-color: #f8f9fa;
    }

    .dropdown-item.active {
      background-color: #e9ecef;
      font-weight: 500;
    }

    .item-icon {
      margin-right: 8px;
    }
  `]
})
export class DropdownSelectComponent {
  @Input() options: DropdownOption[] = [];
  @Input() selectedValue: string | number = '';
  @Input() placeholder: string = 'Select an option';

  @Output() selectionChange = new EventEmitter<string | number>();

  isOpen: boolean = false;

  get selectedLabel(): string {
    const selected = this.options.find(option => option.value === this.selectedValue);
    return selected ? selected.label : this.placeholder;
  }

  toggleDropdown(event: Event): void {
    event.stopPropagation();
    this.isOpen = !this.isOpen;

    if (this.isOpen) {
      // Add click outside listener
      setTimeout(() => {
        document.addEventListener('click', this.closeDropdown);
      });
    }
  }

  closeDropdown = (): void => {
    this.isOpen = false;
    document.removeEventListener('click', this.closeDropdown);
  }

  selectOption(option: DropdownOption): void {
    this.selectedValue = option.value;
    this.selectionChange.emit(option.value);
    this.isOpen = false;
  }
}
