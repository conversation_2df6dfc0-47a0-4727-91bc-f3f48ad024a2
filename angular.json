{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"shell": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": ".", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/shell", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": [], "server": "src/main.server.ts", "prerender": false, "ssr": {"entry": "server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "20mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "20mb", "maximumError": "20mb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "shell:build:production"}, "development": {"buildTarget": "shell:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4200}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "shell:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}}