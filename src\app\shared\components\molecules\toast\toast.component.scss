.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
}

.toast {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.toast:hover {
  transform: translateY(-2px);
}

.toast-success {
  border-left: 4px solid #28a745;
}

.toast-error {
  border-left: 4px solid #dc3545;
}

.toast-info {
  border-left: 4px solid #17a2b8;
}

.toast-warning {
  border-left: 4px solid #ffc107;
}

.toast-icon {
  margin-right: 12px;
  font-size: 18px;
}

.toast-success .toast-icon {
  color: #28a745;
}

.toast-error .toast-icon {
  color: #dc3545;
}

.toast-info .toast-icon {
  color: #17a2b8;
}

.toast-warning .toast-icon {
  color: #ffc107;
}

.toast-content {
  flex: 1;
}

.toast-message {
  font-size: 14px;
  color: #343a40;
}

.toast-close {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 14px;
  margin-left: 12px;
  padding: 0;
}

.toast-close:hover {
  color: #343a40;
}
