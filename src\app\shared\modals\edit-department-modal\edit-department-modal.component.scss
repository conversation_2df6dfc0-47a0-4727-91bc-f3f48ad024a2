@import '../../../../styles/variables/colors';

.department-form {
  padding: 16px 0;

  &__group {
    margin-bottom: 24px;
  }

  &__label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: $gray-700;
    margin-bottom: 8px;
  }

  &__required {
    color: $danger;
    margin-left: 2px;
  }

  &__input,
  &__textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid $gray-300;
    border-radius: 12px;
    font-size: 14px;
    color: $gray-800;
    background-color: $white;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    &::placeholder {
      color: $gray-500;
    }

    &:hover {
      border-color: $gray-400;
    }

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.15);
    }

    &--invalid {
      border-color: $danger;
      background-color: rgba($danger, 0.02);

      &:focus {
        box-shadow: 0 0 0 3px rgba($danger, 0.15);
      }
    }
  }

  &__textarea {
    resize: vertical;
    min-height: 100px;
  }

  &__error {
    font-size: 12px;
    color: $danger;
    margin-top: 6px;
  }

  &__radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__radio-option {
    position: relative;
  }

  &__radio-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;

    &:checked + .department-form__radio-label {
      background-color: rgba($primary, 0.05);
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.15);

      .department-form__radio-icon {
        color: $primary;
      }
    }

    &:focus + .department-form__radio-label {
      box-shadow: 0 0 0 3px rgba($primary, 0.15);
    }
  }

  &__radio-label {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 1px solid $gray-300;
    border-radius: 12px;
    background-color: $white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    &:hover {
      border-color: $gray-400;
      background-color: rgba($gray-100, 0.5);
    }
  }

  &__radio-icon {
    margin-right: 12px;
    color: $gray-500;
    transition: color 0.3s ease;
  }

  &__radio-content {
    display: flex;
    flex-direction: column;
  }

  &__radio-title {
    font-size: 14px;
    font-weight: 500;
    color: $gray-800;
    margin-bottom: 2px;
  }

  &__radio-subtitle {
    font-size: 12px;
    color: $gray-600;
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  &__button {
    min-width: 120px;
  }
}
