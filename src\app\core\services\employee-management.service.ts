import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Interfaces for Employee Management
export interface EmployeeSalary {
  id: number;
  employee_id: number;
  basic_salary: number;
  allowances: number;
  deductions: number;
  gross_salary: number;
  net_salary: number;
  effective_date: string;
  created_at: string;
  updated_at: string;
}

export interface EmployeeHike {
  id: number;
  employee_id: number;
  previous_salary: number;
  new_salary: number;
  hike_percentage: number;
  hike_amount: number;
  effective_date: string;
  reason: string;
  approved_by: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
}

export interface EmployeeOKR {
  id: number;
  employee_id: number;
  title: string;
  description: string;
  objectives: string[];
  key_results: string[];
  quarter: string;
  year: number;
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  progress: number;
  score: number;
  created_at: string;
  updated_at: string;
}

export interface EmployeePerformance {
  id: number;
  employee_id: number;
  review_period: string;
  reviewer_id: number;
  overall_rating: number;
  goals_achievement: number;
  technical_skills: number;
  communication_skills: number;
  leadership_skills: number;
  teamwork: number;
  comments: string;
  status: 'draft' | 'submitted' | 'approved';
  created_at: string;
  updated_at: string;
}

export interface EmployeeLeave {
  id: number;
  employee_id: number;
  leave_type: string;
  start_date: string;
  end_date: string;
  days_requested: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  approved_by?: number;
  created_at: string;
  updated_at: string;
}

export interface EmployeeAttendance {
  id: number;
  employee_id: number;
  date: string;
  check_in_time: string;
  check_out_time: string;
  total_hours: number;
  status: 'present' | 'absent' | 'late' | 'half_day';
  created_at: string;
  updated_at: string;
}

export interface EmployeeTraining {
  id: number;
  employee_id: number;
  training_name: string;
  training_type: string;
  start_date: string;
  end_date: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  completion_percentage: number;
  certificate_url?: string;
  created_at: string;
  updated_at: string;
}

export interface EmployeeAsset {
  id: number;
  employee_id: number;
  asset_name: string;
  asset_type: string;
  asset_id: string;
  assigned_date: string;
  return_date?: string;
  status: 'assigned' | 'returned' | 'damaged' | 'lost';
  condition: string;
  created_at: string;
  updated_at: string;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

@Injectable({
  providedIn: 'root'
})
export class EmployeeManagementService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  // Error handling
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred!';
    if (error.error instanceof ErrorEvent) {
      errorMessage = `Error: ${error.error.message}`;
    } else {
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  // Salary Management
  getEmployeeSalaries(employeeId?: number, page: number = 1, pageSize: number = 10): Observable<PaginatedResponse<EmployeeSalary>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    if (employeeId) {
      params = params.set('employee_id', employeeId.toString());
    }

    return this.http.get<PaginatedResponse<EmployeeSalary>>(`${this.apiUrl}/api/v1/employee-salaries/`, { params })
      .pipe(catchError(this.handleError));
  }

  createEmployeeSalary(salaryData: Partial<EmployeeSalary>): Observable<EmployeeSalary> {
    return this.http.post<EmployeeSalary>(`${this.apiUrl}/api/v1/employee-salaries/`, salaryData)
      .pipe(catchError(this.handleError));
  }

  updateEmployeeSalary(id: number, salaryData: Partial<EmployeeSalary>): Observable<EmployeeSalary> {
    return this.http.put<EmployeeSalary>(`${this.apiUrl}/api/v1/employee-salaries/${id}/`, salaryData)
      .pipe(catchError(this.handleError));
  }

  deleteEmployeeSalary(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/api/v1/employee-salaries/${id}/`)
      .pipe(catchError(this.handleError));
  }

  // Hike Management
  getEmployeeHikes(employeeId?: number, page: number = 1, pageSize: number = 10): Observable<PaginatedResponse<EmployeeHike>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    if (employeeId) {
      params = params.set('employee_id', employeeId.toString());
    }

    return this.http.get<PaginatedResponse<EmployeeHike>>(`${this.apiUrl}/api/v1/employee-hikes/`, { params })
      .pipe(catchError(this.handleError));
  }

  createEmployeeHike(hikeData: Partial<EmployeeHike>): Observable<EmployeeHike> {
    return this.http.post<EmployeeHike>(`${this.apiUrl}/api/v1/employee-hikes/`, hikeData)
      .pipe(catchError(this.handleError));
  }

  updateEmployeeHike(id: number, hikeData: Partial<EmployeeHike>): Observable<EmployeeHike> {
    return this.http.put<EmployeeHike>(`${this.apiUrl}/api/v1/employee-hikes/${id}/`, hikeData)
      .pipe(catchError(this.handleError));
  }

  approveEmployeeHike(id: number): Observable<EmployeeHike> {
    return this.http.patch<EmployeeHike>(`${this.apiUrl}/api/v1/employee-hikes/${id}/approve/`, {})
      .pipe(catchError(this.handleError));
  }

  rejectEmployeeHike(id: number, reason: string): Observable<EmployeeHike> {
    return this.http.patch<EmployeeHike>(`${this.apiUrl}/api/v1/employee-hikes/${id}/reject/`, { reason })
      .pipe(catchError(this.handleError));
  }

  // OKR Management
  getEmployeeOKRs(employeeId?: number, quarter?: string, year?: number, page: number = 1, pageSize: number = 10): Observable<PaginatedResponse<EmployeeOKR>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    if (employeeId) {
      params = params.set('employee_id', employeeId.toString());
    }
    if (quarter) {
      params = params.set('quarter', quarter);
    }
    if (year) {
      params = params.set('year', year.toString());
    }

    return this.http.get<PaginatedResponse<EmployeeOKR>>(`${this.apiUrl}/api/v1/employee-okrs/`, { params })
      .pipe(catchError(this.handleError));
  }

  createEmployeeOKR(okrData: Partial<EmployeeOKR>): Observable<EmployeeOKR> {
    return this.http.post<EmployeeOKR>(`${this.apiUrl}/api/v1/employee-okrs/`, okrData)
      .pipe(catchError(this.handleError));
  }

  updateEmployeeOKR(id: number, okrData: Partial<EmployeeOKR>): Observable<EmployeeOKR> {
    return this.http.put<EmployeeOKR>(`${this.apiUrl}/api/v1/employee-okrs/${id}/`, okrData)
      .pipe(catchError(this.handleError));
  }

  updateOKRProgress(id: number, progress: number): Observable<EmployeeOKR> {
    return this.http.patch<EmployeeOKR>(`${this.apiUrl}/api/v1/employee-okrs/${id}/progress/`, { progress })
      .pipe(catchError(this.handleError));
  }

  // Performance Management
  getEmployeePerformances(employeeId?: number, page: number = 1, pageSize: number = 10): Observable<PaginatedResponse<EmployeePerformance>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    if (employeeId) {
      params = params.set('employee_id', employeeId.toString());
    }

    return this.http.get<PaginatedResponse<EmployeePerformance>>(`${this.apiUrl}/api/v1/employee-performances/`, { params })
      .pipe(catchError(this.handleError));
  }

  createEmployeePerformance(performanceData: Partial<EmployeePerformance>): Observable<EmployeePerformance> {
    return this.http.post<EmployeePerformance>(`${this.apiUrl}/api/v1/employee-performances/`, performanceData)
      .pipe(catchError(this.handleError));
  }

  updateEmployeePerformance(id: number, performanceData: Partial<EmployeePerformance>): Observable<EmployeePerformance> {
    return this.http.put<EmployeePerformance>(`${this.apiUrl}/api/v1/employee-performances/${id}/`, performanceData)
      .pipe(catchError(this.handleError));
  }

  // Leave Management
  getEmployeeLeaves(employeeId?: number, status?: string, page: number = 1, pageSize: number = 10): Observable<PaginatedResponse<EmployeeLeave>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    if (employeeId) {
      params = params.set('employee_id', employeeId.toString());
    }
    if (status) {
      params = params.set('status', status);
    }

    return this.http.get<PaginatedResponse<EmployeeLeave>>(`${this.apiUrl}/api/v1/employee-leaves/`, { params })
      .pipe(catchError(this.handleError));
  }

  createEmployeeLeave(leaveData: Partial<EmployeeLeave>): Observable<EmployeeLeave> {
    return this.http.post<EmployeeLeave>(`${this.apiUrl}/api/v1/employee-leaves/`, leaveData)
      .pipe(catchError(this.handleError));
  }

  updateEmployeeLeave(id: number, leaveData: Partial<EmployeeLeave>): Observable<EmployeeLeave> {
    return this.http.put<EmployeeLeave>(`${this.apiUrl}/api/v1/employee-leaves/${id}/`, leaveData)
      .pipe(catchError(this.handleError));
  }

  approveEmployeeLeave(id: number): Observable<EmployeeLeave> {
    return this.http.patch<EmployeeLeave>(`${this.apiUrl}/api/v1/employee-leaves/${id}/approve/`, {})
      .pipe(catchError(this.handleError));
  }

  rejectEmployeeLeave(id: number, reason: string): Observable<EmployeeLeave> {
    return this.http.patch<EmployeeLeave>(`${this.apiUrl}/api/v1/employee-leaves/${id}/reject/`, { reason })
      .pipe(catchError(this.handleError));
  }

  // Attendance Management
  getEmployeeAttendance(employeeId?: number, date?: string, page: number = 1, pageSize: number = 10): Observable<PaginatedResponse<EmployeeAttendance>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    if (employeeId) {
      params = params.set('employee_id', employeeId.toString());
    }
    if (date) {
      params = params.set('date', date);
    }

    return this.http.get<PaginatedResponse<EmployeeAttendance>>(`${this.apiUrl}/api/v1/employee-attendance/`, { params })
      .pipe(catchError(this.handleError));
  }

  createEmployeeAttendance(attendanceData: Partial<EmployeeAttendance>): Observable<EmployeeAttendance> {
    return this.http.post<EmployeeAttendance>(`${this.apiUrl}/api/v1/employee-attendance/`, attendanceData)
      .pipe(catchError(this.handleError));
  }

  updateEmployeeAttendance(id: number, attendanceData: Partial<EmployeeAttendance>): Observable<EmployeeAttendance> {
    return this.http.put<EmployeeAttendance>(`${this.apiUrl}/api/v1/employee-attendance/${id}/`, attendanceData)
      .pipe(catchError(this.handleError));
  }

  // Training Management
  getEmployeeTrainings(employeeId?: number, status?: string, page: number = 1, pageSize: number = 10): Observable<PaginatedResponse<EmployeeTraining>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    if (employeeId) {
      params = params.set('employee_id', employeeId.toString());
    }
    if (status) {
      params = params.set('status', status);
    }

    return this.http.get<PaginatedResponse<EmployeeTraining>>(`${this.apiUrl}/api/v1/employee-trainings/`, { params })
      .pipe(catchError(this.handleError));
  }

  createEmployeeTraining(trainingData: Partial<EmployeeTraining>): Observable<EmployeeTraining> {
    return this.http.post<EmployeeTraining>(`${this.apiUrl}/api/v1/employee-trainings/`, trainingData)
      .pipe(catchError(this.handleError));
  }

  updateEmployeeTraining(id: number, trainingData: Partial<EmployeeTraining>): Observable<EmployeeTraining> {
    return this.http.put<EmployeeTraining>(`${this.apiUrl}/api/v1/employee-trainings/${id}/`, trainingData)
      .pipe(catchError(this.handleError));
  }

  // Asset Management
  getEmployeeAssets(employeeId?: number, status?: string, page: number = 1, pageSize: number = 10): Observable<PaginatedResponse<EmployeeAsset>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    if (employeeId) {
      params = params.set('employee_id', employeeId.toString());
    }
    if (status) {
      params = params.set('status', status);
    }

    return this.http.get<PaginatedResponse<EmployeeAsset>>(`${this.apiUrl}/api/v1/employee-assets/`, { params })
      .pipe(catchError(this.handleError));
  }

  createEmployeeAsset(assetData: Partial<EmployeeAsset>): Observable<EmployeeAsset> {
    return this.http.post<EmployeeAsset>(`${this.apiUrl}/api/v1/employee-assets/`, assetData)
      .pipe(catchError(this.handleError));
  }

  updateEmployeeAsset(id: number, assetData: Partial<EmployeeAsset>): Observable<EmployeeAsset> {
    return this.http.put<EmployeeAsset>(`${this.apiUrl}/api/v1/employee-assets/${id}/`, assetData)
      .pipe(catchError(this.handleError));
  }

  returnEmployeeAsset(id: number, condition: string): Observable<EmployeeAsset> {
    return this.http.patch<EmployeeAsset>(`${this.apiUrl}/api/v1/employee-assets/${id}/return/`, { condition })
      .pipe(catchError(this.handleError));
  }

  // Employee Statistics and Reports
  getEmployeeStatistics(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/api/v1/employee-statistics/`)
      .pipe(catchError(this.handleError));
  }

  getEmployeeReport(employeeId: number, reportType: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/api/v1/employees/${employeeId}/reports/${reportType}/`)
      .pipe(catchError(this.handleError));
  }

  exportEmployeeData(format: 'csv' | 'excel' | 'pdf', filters?: any): Observable<Blob> {
    let params = new HttpParams().set('format', format);

    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key] !== null && filters[key] !== undefined) {
          params = params.set(key, filters[key].toString());
        }
      });
    }

    return this.http.get(`${this.apiUrl}/api/v1/employees/export/`, {
      params,
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }
}
