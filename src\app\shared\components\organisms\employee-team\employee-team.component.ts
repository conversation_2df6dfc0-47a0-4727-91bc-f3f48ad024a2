import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-team',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="team-container">
      <div class="team-header">
        <h3 class="section-title">Team Birthday</h3>
        <div class="header-actions">
          <button class="action-btn">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      
      <div class="team-content">
        <div class="birthday-card">
          <div class="birthday-avatar">
            <div class="avatar">
              <span class="initials">AJ</span>
            </div>
          </div>
          <div class="birthday-info">
            <h4 class="member-name"><PERSON></h4>
            <p class="birthday-date">14 May 2023</p>
          </div>
        </div>
        
        <div class="team-actions">
          <div class="action-item">
            <h5 class="action-title">Leave Policy</h5>
            <p class="action-subtitle">Last Updated Today</p>
            <button class="action-btn secondary">
              <i class="fa fa-file-alt"></i>
              <span>PDF</span>
            </button>
          </div>
          
          <div class="action-item">
            <h5 class="action-title">Next Holiday</h5>
            <p class="action-subtitle">Mon, 17 May</p>
            <button class="action-btn secondary">
              <i class="fa fa-calendar"></i>
              <span>View</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .team-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .team-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e9ecef;
    }
    
    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }
    
    .action-btn {
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      font-size: 16px;
    }
    
    .team-content {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
    
    .birthday-card {
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 8px;
    }
    
    .birthday-avatar {
      margin-right: 16px;
    }
    
    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #ff6b35;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: 600;
      color: white;
    }
    
    .birthday-info {
      flex: 1;
    }
    
    .member-name {
      margin: 0 0 4px 0;
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }
    
    .birthday-date {
      margin: 0;
      font-size: 14px;
      color: #6c757d;
    }
    
    .team-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }
    
    .action-item {
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
    }
    
    .action-title {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #343a40;
    }
    
    .action-subtitle {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #6c757d;
    }
    
    .action-btn.secondary {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 16px;
      background-color: #e9ecef;
      color: #495057;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      gap: 8px;
    }
    
    .action-btn.secondary:hover {
      background-color: #dee2e6;
    }
  `]
})
export class EmployeeTeamComponent {
  @Input() employee: any;
}
