@use 'sass:color';
@import '../../../../../styles/variables/_colors';

.employee-statistics {
  margin-bottom: 2rem;

  .statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1rem;

    .stat-card {
      background: $white;
      padding: 1.25rem;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      display: flex;
      align-items: center;
      gap: 1rem;
      transition: all 0.3s ease;
      border: 1px solid $border-light;
      position: relative;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        transform: translateY(-1px);
      }

      &__icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        i {
          font-size: 1.25rem;
          color: $white;
        }
      }

      &__content {
        flex: 1;
        min-width: 0;
      }

      &__label {
        color: $text-secondary;
        margin: 0 0 0.25rem 0;
        font-size: 0.875rem;
        font-weight: $font-weight-medium;
        line-height: 1.2;
      }

      &__value {
        font-size: 1.75rem;
        font-weight: $font-weight-bold;
        color: $text-primary;
        margin: 0;
        line-height: 1;

        &--loading {
          font-size: 1.25rem;
          color: $text-secondary;

          i {
            color: $text-secondary;
          }
        }
      }

      &__trend {
        flex-shrink: 0;
        margin-left: auto;

        .trend-indicator {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.75rem;
          font-weight: $font-weight-medium;
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          background: rgba($success, 0.1);
          color: $success;

          &--positive {
            background: rgba($success, 0.1);
            color: $success;
          }

          &--negative {
            background: rgba($danger, 0.1);
            color: $danger;
          }

          i {
            font-size: 0.625rem;
          }
        }
      }

      // Total Employees Card (Black/Dark)
      &--total {
        .stat-card__icon {
          background: #2d3748;
        }
      }

      // Active Employees Card (Green)
      &--active {
        .stat-card__icon {
          background: #38a169;
        }
      }

      // Inactive Employees Card (Red)
      &--inactive {
        .stat-card__icon {
          background: #e53e3e;
        }
      }

      // New Joiners Card (Blue)
      &--new-joiners {
        .stat-card__icon {
          background: #3182ce;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .employee-statistics {
    margin-bottom: 1.5rem;

    .statistics-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem;

      .stat-card {
        padding: 1rem;

        &__icon {
          width: 40px;
          height: 40px;

          i {
            font-size: 1rem;
          }
        }

        &__content {
          .stat-card__label {
            font-size: 0.75rem;
          }

          .stat-card__value {
            font-size: 1.5rem;
          }
        }

        &__trend {
          .trend-indicator {
            font-size: 0.625rem;
            padding: 0.125rem 0.375rem;

            i {
              font-size: 0.5rem;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .employee-statistics {
    .statistics-grid {
      grid-template-columns: 1fr;
    }
  }
}

// Animation for loading spinner
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.fa-spin {
  animation: spin 1s linear infinite;
}
