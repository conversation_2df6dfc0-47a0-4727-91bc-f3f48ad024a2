import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StatCardComponent } from '../../atoms/stat-card/stat-card.component';

export interface StatItem {
  label: string;
  value: string | number;
  icon: string;
  backgroundColor: string;
  trendImageUrl: string;
}

@Component({
  selector: 'app-stats-overview',
  standalone: true,
  imports: [CommonModule, StatCardComponent],
  template: `
    <div class="stats-overview">
      <app-stat-card
        *ngFor="let stat of stats"
        [label]="stat.label"
        [value]="stat.value"
        [icon]="stat.icon"
        [backgroundColor]="stat.backgroundColor"
        [trendImageUrl]="stat.trendImageUrl"
      ></app-stat-card>
    </div>
  `,
  styles: [`
    .stats-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }
    
    @media (max-width: 768px) {
      .stats-overview {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class StatsOverviewComponent {
  @Input() stats: StatItem[] = [];
}
