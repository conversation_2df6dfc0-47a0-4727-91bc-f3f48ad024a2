import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, catchError, throwError } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface EmployeeDocument {
  id: number;
  employee_id: number;
  name: string; // Updated to match API
  document_name: string; // Keep for backward compatibility
  document_type: string;
  description?: string; // Added description field
  file_url: string;
  document?: string; // API file field
  uploaded_on: string;
  file_size?: number;
  created_at: string;
  updated_at: string;
}

export interface PaginatedDocumentsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: EmployeeDocument[];
}

@Injectable({
  providedIn: 'root'
})
export class EmployeeDocumentsService {
  private apiUrl = environment.apiUrl;
  private documentsEndpoint = '/api/v1/employee-documents/';

  constructor(private http: HttpClient) {}

  /**
   * Get all documents for an employee
   * @param employeeId The employee ID
   * @param page Page number (optional)
   * @param pageSize Number of items per page (optional)
   * @returns Observable of documents response
   */
  getEmployeeDocuments(employeeId: number, page?: number, pageSize?: number): Observable<PaginatedDocumentsResponse> {
    let params = new HttpParams();

    if (page) {
      params = params.set('page', page.toString());
    }

    if (pageSize) {
      params = params.set('page_size', pageSize.toString());
    }
    if(employeeId) {
      params = params.set('employee', employeeId.toString());
    }

    return this.http.get<PaginatedDocumentsResponse>(`${this.apiUrl}${this.documentsEndpoint}`, { params })
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get a specific document by ID
   * @param documentId The document ID
   * @returns Observable of document
   */
  getDocumentById(documentId: number): Observable<EmployeeDocument> {
    return this.http.get<EmployeeDocument>(`${this.apiUrl}${this.documentsEndpoint}${documentId}/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Upload a new document
   * @param documentData FormData containing document information and file
   * @returns Observable of created document
   */
  uploadDocument(documentData: FormData): Observable<EmployeeDocument> {
    // Log the FormData contents for debugging
    console.log('Uploading document with FormData');

    return this.http.post<EmployeeDocument>(`${this.apiUrl}${this.documentsEndpoint}`, documentData, {
      headers: {
        // Don't set Content-Type header - let browser set it with boundary for multipart/form-data
      }
    }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Update an existing document
   * @param documentId The document ID
   * @param documentData FormData containing updated document information
   * @returns Observable of updated document
   */
  updateDocument(documentId: number, documentData: FormData): Observable<EmployeeDocument> {
    return this.http.put<EmployeeDocument>(`${this.apiUrl}${this.documentsEndpoint}${documentId}/`, documentData)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Partially update a document (without file)
   * @param documentId The document ID
   * @param documentData Partial document data
   * @returns Observable of updated document
   */
  patchDocument(documentId: number, documentData: Partial<EmployeeDocument>): Observable<EmployeeDocument> {
    return this.http.patch<EmployeeDocument>(`${this.apiUrl}${this.documentsEndpoint}${documentId}/`, documentData)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Delete a document
   * @param documentId The document ID
   * @returns Observable of void
   */
  deleteDocument(documentId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}${this.documentsEndpoint}${documentId}/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Download a document
   * @param documentId The document ID
   * @returns Observable of blob
   */
  downloadDocument(documentId: number): Observable<Blob> {
    return this.http.get(`${this.apiUrl}${this.documentsEndpoint}${documentId}/download/`, {
      responseType: 'blob'
    }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get document types
   * @returns Observable of document types
   */
  getDocumentTypes(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}${this.documentsEndpoint}types/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Search documents
   * @param employeeId The employee ID
   * @param searchTerm Search term
   * @param documentType Document type filter (optional)
   * @param page Page number (optional)
   * @param pageSize Number of items per page (optional)
   * @returns Observable of documents response
   */
  searchDocuments(
    employeeId: number,
    searchTerm: string,
    documentType?: string,
    page?: number,
    pageSize?: number
  ): Observable<PaginatedDocumentsResponse> {
    let params = new HttpParams()
      .set('search', searchTerm);

    if (documentType) {
      params = params.set('document_type', documentType);
    }

    if (page) {
      params = params.set('page', page.toString());
    }

    if (pageSize) {
      params = params.set('page_size', pageSize.toString());
    }

    return this.http.get<PaginatedDocumentsResponse>(`${this.apiUrl}${this.documentsEndpoint}${employeeId}/search/`, { params })
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Bulk delete documents
   * @param employeeId The employee ID
   * @param documentIds Array of document IDs
   * @returns Observable of void
   */
  bulkDeleteDocuments(employeeId: number, documentIds: number[]): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}${this.documentsEndpoint}${employeeId}/bulk-delete/`, {
      document_ids: documentIds
    }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get document statistics for an employee
   * @param employeeId The employee ID
   * @returns Observable of document statistics
   */
  getDocumentStatistics(employeeId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}${this.documentsEndpoint}${employeeId}/statistics/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Handle HTTP errors
   * @param error HTTP error response
   * @returns Observable error
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage = 'Bad request. Please check your input.';
          break;
        case 401:
          errorMessage = 'Unauthorized. Please log in again.';
          break;
        case 403:
          errorMessage = 'Forbidden. You do not have permission to perform this action.';
          break;
        case 404:
          errorMessage = 'Document not found.';
          break;
        case 413:
          errorMessage = 'File too large. Please select a smaller file.';
          break;
        case 415:
          errorMessage = 'Unsupported file type.';
          break;
        case 500:
          errorMessage = 'Internal server error. Please try again later.';
          break;
        default:
          errorMessage = `Error ${error.status}: ${error.message}`;
      }

      // If there's a detailed error message from the server, use it
      if (error.error && typeof error.error === 'object') {
        if (error.error.detail) {
          errorMessage = error.error.detail;
        } else if (error.error.message) {
          errorMessage = error.error.message;
        } else if (error.error.error) {
          errorMessage = error.error.error;
        }
      }
    }

    console.error('Employee Documents Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
