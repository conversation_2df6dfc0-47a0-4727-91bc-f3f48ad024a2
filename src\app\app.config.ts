import { APP_INITIALIZER, ApplicationConfig } from '@angular/core';
import { provideRouter, withComponentInputBinding } from '@angular/router';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';

import { routes } from './app.routes';
import { provideClientHydration } from '@angular/platform-browser';
import { CORE_PROVIDERS } from './core/providers';
import { authInterceptor } from './core/interceptors/auth.interceptor.fn';
import { AuthStore } from './core/state';

/**
 * Function to initialize the auth store
 *
 * This function is used as an APP_INITIALIZER to initialize the auth store
 * when the application starts. It calls the init() method on the AuthStore
 * which checks for an existing token and loads the user profile if available.
 *
 * NOTE: This is part of the NgRx Signals implementation. If you encounter build
 * issues, you may need to comment out this code until all dependencies are resolved.
 */
function initializeAuthStore(authStore: any) {
  return () => {
    // Call the init method on the AuthStore instance
    if (authStore && typeof authStore.init === 'function') {
      authStore.init();
    }
  };
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes, withComponentInputBinding()),
    provideClientHydration(),
    provideHttpClient(
      withInterceptors([authInterceptor])
    ),
    provideAnimations(),
    ...CORE_PROVIDERS,
    // Initialize auth store on app startup
    // NOTE: Comment out this provider if you encounter build issues
    // until all dependencies are resolved
    {
      provide: APP_INITIALIZER,
      useFactory: initializeAuthStore,
      deps: [AuthStore],
      multi: true
    }
  ]
};
