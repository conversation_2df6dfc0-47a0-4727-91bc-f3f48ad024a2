import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutService } from '../../../../core/services/layout.service';
import { HeaderPosition } from '../../../../core/models/theme.interface';

@Component({
  selector: 'app-header-position-toggle',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './header-position-toggle.component.html',
  styleUrls: ['./header-position-toggle.component.scss']
})
export class HeaderPositionToggleComponent {
  private readonly layoutService = inject(LayoutService);

  // Expose layout service signals
  readonly layoutConfig = this.layoutService.layoutConfig;
  readonly headerPosition = this.layoutService.headerPosition;

  /**
   * Set header position
   */
  setHeaderPosition(position: HeaderPosition): void {
    this.layoutService.setHeaderPosition(position);
  }

  /**
   * Get header position display name
   */
  getHeaderPositionDisplayName(position: HeaderPosition): string {
    return this.layoutService.getHeaderPositionDisplayName(position);
  }

  /**
   * Get available header positions
   */
  getAvailableHeaderPositions(): { value: HeaderPosition; label: string; description: string; icon: string }[] {
    return [
      {
        value: 'fixed',
        label: 'Fixed',
        description: 'Header stays at top during scroll',
        icon: 'fas fa-thumbtack'
      },
      {
        value: 'static',
        label: 'Static',
        description: 'Header scrolls with content',
        icon: 'fas fa-arrows-alt-v'
      },
      {
        value: 'sticky',
        label: 'Sticky',
        description: 'Header becomes fixed after scrolling',
        icon: 'fas fa-sticky-note'
      }
    ];
  }

  /**
   * Get current header position icon
   */
  getCurrentHeaderPositionIcon(): string {
    const currentPosition = this.headerPosition();
    const positions = this.getAvailableHeaderPositions();
    const position = positions.find(p => p.value === currentPosition);
    return position?.icon || 'fas fa-thumbtack';
  }

  /**
   * Get current header position description
   */
  getCurrentHeaderPositionDescription(): string {
    const currentPosition = this.headerPosition();
    const positions = this.getAvailableHeaderPositions();
    const position = positions.find(p => p.value === currentPosition);
    return position?.description || '';
  }

  /**
   * Check if header position is active
   */
  isHeaderPositionActive(position: HeaderPosition): boolean {
    return this.headerPosition() === position;
  }
}
