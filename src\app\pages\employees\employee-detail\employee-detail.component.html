<div class="employee-detail">
  <!-- Header with <PERSON>ton -->
  <div class="employee-detail__header">
    <button class="back-btn" (click)="goBack()">
      <i class="fas fa-arrow-left"></i>
      Employee Details
    </button>

    <div class="header-actions">
      <button class="btn btn-primary">
        <i class="fas fa-briefcase"></i>
        Rank & Statutory
      </button>
    </div>
  </div>

  <!-- Loading State -->
  @if (isLoading()) {
    <div class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading employee details...</p>
      </div>
    </div>
  }

  <!-- Error State -->
  @if (error()) {
    <div class="error-container">
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Error Loading Employee</h3>
        <p>{{ error() }}</p>
        <button class="btn btn-primary" (click)="goBack()">
          <i class="fas fa-arrow-left"></i>
          Go Back
        </button>
      </div>
    </div>
  }

  @if (employee()) {
    <!-- Employee Profile Header -->
    <div class="employee-profile">
      <!-- Profile Banner -->
      <div class="profile-banner">
        <div class="profile-avatar">
          @if (employee()?.avatar) {
            <img [src]="employee()?.avatar" [alt]="employee()?.first_name + ' ' + employee()?.last_name">
          } @else {
            <div class="avatar-placeholder">
              <i class="fas fa-user"></i>
            </div>
          }
        </div>

        <div class="profile-info">
          <h1 class="employee-name">{{ employee()?.first_name }} {{ employee()?.last_name }}</h1>
          <p class="employee-position">{{ employee()?.position || 'Software Developer' }}</p>
          <p class="employee-experience">{{ getExperienceText() }}</p>
        </div>

        <div class="profile-actions">
          <button class="btn btn-outline" (click)="editInfo()">
            <i class="fas fa-edit"></i>
            Edit Info
          </button>
          <button class="btn btn-primary" (click)="sendMessage()">
            <i class="fas fa-envelope"></i>
            Message
          </button>
        </div>
      </div>

      <!-- Employee Details Grid -->
      <div class="employee-details-grid">
        <!-- Basic Information Card -->
        <div class="info-card">
          <div class="card-header">
            <h3>Basic Information</h3>
            <button class="edit-btn" (click)="editBasicInfo()">
              <i class="fas fa-edit"></i>
            </button>
          </div>
          <div class="card-content">
            <div class="info-row">
              <span class="label">Client ID</span>
              <span class="value">{{ employee()?.id || 'CLI-0024' }}</span>
            </div>
            <div class="info-row">
              <span class="label">Team</span>
              <span class="value">{{ employee()?.department || 'UI/UX Design' }}</span>
            </div>
            <div class="info-row">
              <span class="label">Date Of Join</span>
              <span class="value">{{ formatDate(employee()?.hire_date) || '1st Jan 2023' }}</span>
            </div>
            <div class="info-row">
              <span class="label">Report Office</span>
              <span class="value">{{ employee()?.department || 'UI/UX Design' }}</span>
            </div>
            <div class="info-row">
              <span class="label">Phone</span>
              <span class="value">{{ employee()?.phone || '(163) 2459 315' }}</span>
            </div>
            <div class="info-row">
              <span class="label">Email</span>
              <span class="value">{{ employee()?.email || '<EMAIL>' }}</span>
            </div>
            <div class="info-row">
              <span class="label">Status</span>
              <span class="value">{{ getStatusLabel(employee()?.status) || 'Active' }}</span>
            </div>
            <div class="info-row">
              <span class="label">Salary</span>
              <span class="value">{{ employee()?.salary ? '$' + employee()?.salary : 'Not specified' }}</span>
            </div>
            <div class="info-row">
              <span class="label">Address</span>
              <span class="value">{{ getFullAddress() || '1861 Bayonne Ave, Manchester, NJ, 08759' }}</span>
            </div>
          </div>
        </div>

        <!-- About Employee Card -->
        <div class="info-card expandable">
          <div class="card-header">
            <h3>About Employee</h3>
            <div class="card-actions">
              <button class="edit-btn" (click)="editAbout()">
                <i class="fas fa-edit"></i>
              </button>
              <button class="expand-btn" (click)="toggleAbout()">
                <i class="fas {{ aboutExpanded ? 'fa-chevron-up' : 'fa-chevron-down' }}"></i>
              </button>
            </div>
          </div>
          <div class="card-content" [class.expanded]="aboutExpanded">
            <p>{{ getEmployeeDescription() }}</p>
          </div>
        </div>

        <!-- Bank Information Card -->
        <div class="info-card expandable">
          <div class="card-header">
            <h3>Bank Information</h3>
            <div class="card-actions">
              <button class="edit-btn" (click)="editBankInfo()">
                <i class="fas fa-edit"></i>
              </button>
              <button class="expand-btn" (click)="toggleBankInfo()">
                <i class="fas {{ bankInfoExpanded ? 'fa-chevron-up' : 'fa-chevron-down' }}"></i>
              </button>
            </div>
          </div>
          <div class="card-content" [class.expanded]="bankInfoExpanded">
            <div class="info-row">
              <span class="label">Account Number</span>
              <span class="value">****-****-****-1234</span>
            </div>
            <div class="info-row">
              <span class="label">Bank Name</span>
              <span class="value">Chase Bank</span>
            </div>
            <div class="info-row">
              <span class="label">IFSC Code</span>
              <span class="value">CHAS0001234</span>
            </div>
          </div>
        </div>

        <!-- Family Information Card -->
        <div class="info-card expandable">
          <div class="card-header">
            <h3>Family Information</h3>
            <div class="card-actions">
              <button class="edit-btn" (click)="editFamilyInfo()">
                <i class="fas fa-edit"></i>
              </button>
              <button class="expand-btn" (click)="toggleFamilyInfo()">
                <i class="fas {{ familyInfoExpanded ? 'fa-chevron-up' : 'fa-chevron-down' }}"></i>
              </button>
            </div>
          </div>
          <div class="card-content" [class.expanded]="familyInfoExpanded">
            <div class="info-row">
              <span class="label">Marital Status</span>
              <span class="value">Married</span>
            </div>
            <div class="info-row">
              <span class="label">Spouse Name</span>
              <span class="value">Jane Doe</span>
            </div>
            <div class="info-row">
              <span class="label">Children</span>
              <span class="value">2</span>
            </div>
          </div>
        </div>

        <!-- Education Details Card -->
        <div class="info-card expandable">
          <div class="card-header">
            <h3>Education Details</h3>
            <div class="card-actions">
              <button class="edit-btn" (click)="editEducation()">
                <i class="fas fa-edit"></i>
              </button>
              <button class="expand-btn" (click)="toggleEducation()">
                <i class="fas {{ educationExpanded ? 'fa-chevron-up' : 'fa-chevron-down' }}"></i>
              </button>
            </div>
          </div>
          <div class="card-content" [class.expanded]="educationExpanded">
            <div class="education-item">
              <h4>Bachelor's in Computer Science</h4>
              <p>University of Technology • 2018-2022</p>
            </div>
          </div>
        </div>

        <!-- Experience Card -->
        <div class="info-card expandable">
          <div class="card-header">
            <h3>Experience</h3>
            <div class="card-actions">
              <button class="edit-btn" (click)="editExperience()">
                <i class="fas fa-edit"></i>
              </button>
              <button class="expand-btn" (click)="toggleExperience()">
                <i class="fas {{ experienceExpanded ? 'fa-chevron-up' : 'fa-chevron-down' }}"></i>
              </button>
            </div>
          </div>
          <div class="card-content" [class.expanded]="experienceExpanded">
            <div class="experience-item">
              <h4>Senior Software Developer</h4>
              <p>Current Company • 2023 - Present</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Management Sections -->
      <div class="management-sections">
        <div class="section-tabs">
          <button
            class="tab-btn"
            [class.active]="activeManagementTab === 'projects'"
            (click)="setActiveManagementTab('projects')">
            Projects
          </button>
          <button
            class="tab-btn"
            [class.active]="activeManagementTab === 'assets'"
            (click)="setActiveManagementTab('assets')">
            Assets
          </button>
          <button
            class="tab-btn"
            [class.active]="activeManagementTab === 'documents'"
            (click)="setActiveManagementTab('documents')">
            Documents
          </button>
        </div>

        <div class="tab-content">
          @if (activeManagementTab === 'projects') {
            <div class="projects-section">
              <div class="section-header">
                <h3>Employee Projects</h3>
                <button class="btn btn-primary" (click)="addProject()">
                  <i class="fas fa-plus"></i>
                  Add Project
                </button>
              </div>

              <div class="project-grid">
                <div class="project-card">
                  <div class="project-icon world-health">
                    <i class="fas fa-globe"></i>
                  </div>
                  <div class="project-info">
                    <h4>World Health</h4>
                    <div class="project-stats">
                      <span class="tasks">8 Tasks</span>
                      <span class="completed">15 Completed</span>
                    </div>
                    <div class="project-meta">
                      <span class="deadline">Deadline: 31 July 2025</span>
                      <div class="project-lead">
                        <span>Project Lead:</span>
                        <div class="lead-avatar">
                          <i class="fas fa-user-circle"></i>
                        </div>
                        <span>Leona</span>
                      </div>
                    </div>
                    <div class="project-actions">
                      <button class="btn-action edit" (click)="editProject(1)">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn-action delete" (click)="deleteProject(1)">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>

                <div class="project-card">
                  <div class="project-icon hospital-admin">
                    <i class="fas fa-hospital"></i>
                  </div>
                  <div class="project-info">
                    <h4>Hospital Administration</h4>
                    <div class="project-stats">
                      <span class="tasks">8 Tasks</span>
                      <span class="completed">15 Completed</span>
                    </div>
                    <div class="project-meta">
                      <span class="deadline">Deadline: 31 July 2025</span>
                      <div class="project-lead">
                        <span>Project Lead:</span>
                        <div class="lead-avatar">
                          <i class="fas fa-user-circle"></i>
                        </div>
                        <span>Leona</span>
                      </div>
                    </div>
                    <div class="project-actions">
                      <button class="btn-action edit" (click)="editProject(2)">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn-action delete" (click)="deleteProject(2)">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          }

          @if (activeManagementTab === 'assets') {
            <div class="assets-section">
              <div class="section-header">
                <h3>Employee Assets</h3>
                <button class="btn btn-primary" (click)="addAsset()">
                  <i class="fas fa-plus"></i>
                  Assign Asset
                </button>
              </div>

              @if (managementLoading.assets) {
                <div class="loading-state">
                  <i class="fas fa-spinner fa-spin"></i>
                  <p>Loading assets...</p>
                </div>
              } @else if (employeeAssets.length === 0) {
                <div class="empty-state">
                  <i class="fas fa-laptop"></i>
                  <p>No assets assigned to this employee</p>
                </div>
              } @else {
                <div class="assets-grid">
                  @for (asset of employeeAssets; track asset.id) {
                    <div class="asset-card">
                      <div class="asset-icon">
                        <i class="fas fa-laptop"></i>
                      </div>
                      <div class="asset-info">
                        <h4>{{ asset.asset_name }}</h4>
                        <p class="asset-type">{{ asset.asset_type }}</p>
                        <p class="asset-serial">Serial: {{ asset.serial_number }}</p>
                        <div class="asset-status" [class]="'status-' + asset.status">
                          {{ asset.status }}
                        </div>
                        <div class="asset-dates">
                          <span>Assigned: {{ formatDate(asset.assigned_date) }}</span>
                          @if (asset.return_date) {
                            <span>Returned: {{ formatDate(asset.return_date) }}</span>
                          }
                        </div>
                      </div>
                      <div class="asset-actions">
                        <button class="btn-action edit" (click)="editAsset(asset)">
                          <i class="fas fa-edit"></i>
                        </button>
                        @if (asset.status === 'assigned') {
                          <button class="btn-action return" (click)="returnAsset(asset.id)">
                            <i class="fas fa-undo"></i>
                          </button>
                        }
                      </div>
                    </div>
                  }
                </div>
              }
            </div>
          }

          @if (activeManagementTab === 'documents') {
            <div class="documents-section">
              @if (employee()?.id) {
                <app-employee-documents-section [employeeId]="employee()!.id"></app-employee-documents-section>
              } @else {
                <div class="loading-state">
                  <i class="fas fa-spinner fa-spin"></i>
                  <p>Loading employee information...</p>
                </div>
              }
            </div>
          }
        </div>
      </div>
    </div>
  }
</div>
