import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthStore } from '../../core/state';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule],
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent {
  private authStore = inject(AuthStore);
  private fb = inject(FormBuilder);
  
  // Access state from the store using signals
  user = this.authStore.user;
  isAdmin = this.authStore.isAdmin;
  
  // Form state
  passwordForm: FormGroup;
  notificationForm: FormGroup;
  appearanceForm: FormGroup;
  
  // UI state
  activeTab = 'account'; // 'account', 'password', 'notifications', 'appearance'
  isSaving = false;
  successMessage: string | null = null;
  errorMessage: string | null = null;
  
  constructor() {
    // Initialize password form
    this.passwordForm = this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
    
    // Initialize notification form
    this.notificationForm = this.fb.group({
      emailNotifications: [true],
      pushNotifications: [true],
      emailUpdates: [true],
      emailMarketing: [false],
      smsNotifications: [false]
    });
    
    // Initialize appearance form
    this.appearanceForm = this.fb.group({
      theme: ['light'], // 'light', 'dark', 'system'
      fontSize: ['medium'], // 'small', 'medium', 'large'
      compactMode: [false],
      animationsEnabled: [true]
    });
  }
  
  // Password match validator
  passwordMatchValidator(formGroup: FormGroup) {
    const newPassword = formGroup.get('newPassword')?.value;
    const confirmPassword = formGroup.get('confirmPassword')?.value;
    
    if (newPassword !== confirmPassword) {
      formGroup.get('confirmPassword')?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    } else {
      return null;
    }
  }
  
  // Set active tab
  setActiveTab(tab: string): void {
    this.activeTab = tab;
    this.clearMessages();
  }
  
  // Save password
  savePassword(): void {
    if (this.passwordForm.invalid) {
      this.passwordForm.markAllAsTouched();
      return;
    }
    
    this.isSaving = true;
    
    // Simulate API call
    setTimeout(() => {
      this.isSaving = false;
      this.successMessage = 'Password updated successfully';
      this.passwordForm.reset();
    }, 1000);
  }
  
  // Save notification settings
  saveNotificationSettings(): void {
    this.isSaving = true;
    
    // Simulate API call
    setTimeout(() => {
      this.isSaving = false;
      this.successMessage = 'Notification settings updated successfully';
    }, 1000);
  }
  
  // Save appearance settings
  saveAppearanceSettings(): void {
    this.isSaving = true;
    
    // Simulate API call
    setTimeout(() => {
      this.isSaving = false;
      this.successMessage = 'Appearance settings updated successfully';
    }, 1000);
  }
  
  // Clear success and error messages
  clearMessages(): void {
    this.successMessage = null;
    this.errorMessage = null;
  }
  
  // Logout
  logout(): void {
    this.authStore.logout();
  }
}
