import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaintenanceService } from '../../core/services/maintenance.service';

@Component({
  selector: 'app-maintenance',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './maintenance.component.html',
  styleUrls: ['./maintenance.component.scss']
})
export class MaintenanceComponent implements OnInit {
  estimatedCompletion: string = '';
  maintenanceMessage: string = '';

  constructor(private maintenanceService: MaintenanceService) {}

  ngOnInit(): void {
    // Get maintenance information from the service
    const config = this.maintenanceService.getMaintenanceConfig();
    this.maintenanceMessage = config.message;
    this.estimatedCompletion = this.maintenanceService.getEstimatedCompletionTime();
  }

  refreshPage(): void {
    window.location.reload();
  }
}
