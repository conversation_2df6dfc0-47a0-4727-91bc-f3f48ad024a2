@use 'sass:color';
@import '../../../../../styles/variables/_colors';

// AI Chat Widget Container
.ai-chat-widget {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 9999;
  font-family: $font-family-base;

  @media (max-width: 768px) {
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
  }
}

// Floating Chat Bubble (Collapsed State)
.chat-bubble {
  position: relative;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--theme-primary, $primary) 0%, var(--theme-primary-dark, color.adjust($primary, $lightness: -10%)) 100%);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(var(--theme-primary-rgb, 59, 130, 246), 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--theme-on-primary, $white);
  border: 2px solid rgba($white, 0.2);

  &:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 40px rgba(var(--theme-primary-rgb, 59, 130, 246), 0.4);

    .chat-bubble__tooltip {
      opacity: 1;
      visibility: visible;
      transform: translateX(-50%) translateY(-8px);
    }
  }

  &:focus {
    outline: 3px solid rgba(var(--theme-primary-rgb, 59, 130, 246), 0.3);
    outline-offset: 2px;
  }

  &:active {
    transform: translateY(-2px) scale(1.02);
  }

  &__icon {
    font-size: 22px;
    animation: pulse 3s infinite;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    // Ensure perfect centering
    app-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }

  &__notification {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 20px;
    height: 20px;
    background: $danger;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid $white;
    animation: bounce 2s infinite;

    .notification-dot {
      width: 8px;
      height: 8px;
      background: $white;
      border-radius: 50%;
    }
  }

  &__tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-4px);
    background: $gray-900;
    color: $white;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;

    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 6px solid transparent;
      border-top-color: $gray-900;
    }
  }

}

// Chat Panel (Expanded State)
.chat-panel {
  width: 400px;
  height: 600px;
  background: var(--theme-card-background, $white);
  border-radius: 16px;
  box-shadow: var(--theme-shadow-xl, 0 20px 60px rgba(0, 0, 0, 0.15));
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--theme-card-border, $card-border-color);

  @media (max-width: 768px) {
    width: 100%;
    height: 70vh;
    max-height: 600px;
    border-radius: 16px 16px 0 0;
  }

  @media (max-width: 480px) {
    height: 80vh;
    border-radius: 12px 12px 0 0;
  }
}

// Chat Header
.chat-header {
  background: linear-gradient(135deg, $gray-700 0%, $gray-800 100%);
  color: $white;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba($white, 0.1);


  &__content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
  }

  &__avatar {
    width: 40px;
    height: 40px;
    background: rgba($white, 0.15);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;

    // Ensure perfect centering for header avatar
    app-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }


  &__info {
    flex: 1;
  }

  &__title {
    margin: 0;
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    line-height: 1.2;
  }

  &__status {
    margin: 0;
    font-size: $font-size-xs;
    opacity: 0.8;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &--typing {
      color: $success;
    }
  }

  &__actions {
    display: flex;
    gap: 0.25rem;
  }

  &__action {
    width: 32px;
    height: 32px;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: $white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: rgba($white, 0.15);
    }

    &:focus {
      outline: 2px solid rgba($white, 0.3);
      outline-offset: 1px;
    }

    &--close:hover {
      background: $danger;
    }
  }
}

// Error Banner
.error-banner {
  background: linear-gradient(135deg, rgba($danger, 0.08) 0%, rgba($danger, 0.04) 100%);
  border-bottom: 1px solid rgba($danger, 0.15);
  padding: 1rem 1.5rem;
  backdrop-filter: blur(10px);

  &__content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: $danger;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
  }

  &__message {
    flex: 1;
    line-height: $line-height-base;
  }

  &__retry,
  &__close {
    background: rgba($danger, 0.1);
    border: 1px solid rgba($danger, 0.2);
    color: $danger;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;

    &:hover {
      background: rgba($danger, 0.15);
      border-color: rgba($danger, 0.3);
      transform: translateY(-1px);
    }

    &:focus {
      outline: 2px solid rgba($danger, 0.3);
      outline-offset: 2px;
    }
  }
}

// Messages Container
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  background: linear-gradient(180deg, rgba($gray-50, 0.3) 0%, transparent 100%);

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba($gray-100, 0.5);
    border-radius: 4px;
    margin: 8px 0;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, $gray-300 0%, $gray-400 100%);
    border-radius: 4px;
    border: 1px solid rgba($white, 0.2);

    &:hover {
      background: linear-gradient(180deg, $gray-400 0%, $gray-500 100%);
    }
  }
}

// Welcome Message
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem 1rem;

  &__avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--theme-primary, $primary) 0%, var(--theme-primary-light, color.adjust($primary, $lightness: 10%)) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $white;
    font-size: 1.75rem;
    margin-bottom: 1rem;

    // Ensure perfect centering for welcome avatar
    app-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }

  &__content {
    h4 {
      margin: 0 0 0.5rem 0;
      color: $text-primary;
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
    }

    p {
      margin: 0 0 1.5rem 0;
      color: $text-secondary;
      font-size: $font-size-sm;
      line-height: $line-height-relaxed;
    }
  }

  &__suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
  }
}

.suggestion-chip {
  background: $gray-100;
  border: 1px solid $gray-200;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: $font-size-sm;
  color: $text-secondary;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: $primary;
    color: $white;
    border-color: $primary;
  }
}

// Messages List
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

// Individual Messages
.message {
  display: flex;
  gap: 0.75rem;
  max-width: 85%;

  &--user {
    align-self: flex-end;
    flex-direction: row-reverse;

    .message__bubble {
      background: $primary;
      color: $white;
      border-bottom-right-radius: 8px;
      box-shadow: 0 4px 12px rgba($primary, 0.2);
    }

    .message__timestamp {
      color: rgba($white, 0.8);
      text-align: right;
    }
  }

  &--ai {
    align-self: flex-start;

    .message__bubble {
      background: $gray-100;
      color: $text-primary;
      border-bottom-left-radius: 8px;
      box-shadow: 0 2px 8px rgba($black, 0.04);
    }

    .message__timestamp {
      color: $text-muted;
    }
  }

  &--error {
    .message__bubble {
      background: rgba($danger, 0.1);
      border: 1px solid rgba($danger, 0.2);
      color: $danger;
    }
  }

  &__avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 1rem;

    // Ensure perfect centering for message avatars
    app-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }

  &--user &__avatar {
    background: $primary;
    color: $white;
  }

  &--ai &__avatar {
    background: $gray-200;
    color: $gray-600;
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__bubble {
    padding: 0.75rem 1rem;
    border-radius: 16px;
    position: relative;
    word-wrap: break-word;

    &--loading {
      padding: 1rem;
    }
  }

  &__text {
    font-size: $font-size-sm;
    line-height: $line-height-relaxed;
    margin-bottom: 0.25rem;
  }

  &__timestamp {
    font-size: $font-size-xs;
    opacity: 0.7;
    margin-top: 0.25rem;
  }
}

// Loading and Typing Indicators
.loading-dots,
.typing-indicator {
  display: flex;
  gap: 0.375rem;
  align-items: center;

  span {
    width: 8px;
    height: 8px;
    background: currentColor;
    border-radius: 50%;
    animation: loadingDots 1.6s infinite ease-in-out;
    opacity: 0.4;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }
}

.typing-indicator {
  span {
    background: #10b981;
    box-shadow: 0 0 8px rgba(#10b981, 0.3);
  }
}

// Chat Input
.chat-input {
  border-top: 1px solid $card-border-color;
  padding: 1rem;
  background: $card-bg;

  &__form {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  &__container {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
  }

  &__field {
    flex: 1;
    border: 1px solid $input-border-color;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: $font-size-sm;
    font-family: inherit;
    resize: none;
    min-height: 40px;
    max-height: 120px;
    background: $input-bg;
    color: $text-primary;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.1);
    }

    &:disabled {
      background: $gray-100;
      color: $text-muted;
      cursor: not-allowed;
    }

    &::placeholder {
      color: $text-muted;
    }
  }

  &__send {
    width: 40px;
    height: 40px;
    background: $primary;
    border: none;
    border-radius: 50%;
    color: $white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;

    &:hover:not(:disabled) {
      background: color.adjust($primary, $lightness: -10%);
      transform: scale(1.05);
    }

    &:disabled {
      background: $gray-300;
      cursor: not-allowed;
      transform: none;
    }

    &:focus {
      outline: 3px solid rgba($primary, 0.3);
      outline-offset: 2px;
    }
  }

  &__counter {
    font-size: $font-size-xs;
    color: $text-muted;
    text-align: right;
  }
}

// Animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }
}

@keyframes bounce {

  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }

  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }

  70% {
    transform: translate3d(0, -4px, 0);
  }

  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes loadingDots {

  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .ai-chat-widget {
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
  }

  .chat-panel {
    width: 100%;
    height: 75vh;
    max-height: 640px;
  }
}

@media (max-width: 480px) {
  .ai-chat-widget {
    bottom: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
  }

  .chat-panel {
    height: 85vh;
    border-radius: 16px 16px 0 0;
  }

  .chat-bubble {
    width: 56px;
    height: 56px;
    border-radius: 16px;

    &__icon {
      font-size: 20px;
    }
  }

  .chat-header {
    padding: 1rem 1.25rem;

    &__avatar {
      width: 40px;
      height: 40px;
      font-size: 1.375rem;
      border-radius: 10px;
    }

    &__title {
      font-size: 1rem;
    }

    &__status {
      font-size: $font-size-xs;
    }
  }

  .chat-messages {
    padding: 1rem;
  }

  .welcome-message {
    padding: 2rem 1rem;

    &__avatar {
      width: 64px;
      height: 64px;
      border-radius: 16px;
      font-size: 1.625rem;
    }
  }

  .message {
    max-width: 92%;

    &__avatar {
      width: 32px;
      height: 32px;
      font-size: 0.875rem;
      border-radius: 10px;
    }

    &__bubble {
      padding: 0.875rem 1rem;
      border-radius: 16px;
    }

    &__text {
      font-size: $font-size-sm;
    }
  }

  .chat-input {
    padding: 1.25rem;

    &__container {
      border-radius: 14px;
    }

    &__field {
      padding: 0.75rem 1rem;
      font-size: $font-size-sm;
      min-height: 40px;
    }

    &__send {
      width: 40px;
      height: 40px;
      border-radius: 10px;
    }
  }

  .suggestion-chip {
    padding: 0.625rem 1rem;
    font-size: $font-size-xs;
    border-radius: 20px;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .chat-bubble {
    border: 2px solid $white;
  }

  .message__bubble {
    border: 1px solid currentColor;
  }

  .chat-input__field {
    border: 2px solid $input-border-color;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {

  .chat-bubble,
  .chat-panel,
  .message__bubble,
  .chat-input__send {
    transition: none;
    animation: none;
  }

  .chat-bubble__icon {
    animation: none;
  }

  .chat-bubble__notification {
    animation: none;
  }

  .loading-dots span,
  .typing-indicator span {
    animation: none;
  }
}

// Dark theme support (if implemented)
@media (prefers-color-scheme: dark) {
  .ai-chat-widget {
    // Dark theme styles would go here
    // This can be expanded when dark theme is implemented
  }
}

// Focus visible support for better accessibility
.chat-bubble:focus-visible,
.chat-header__action:focus-visible,
.chat-input__send:focus-visible,
.suggestion-chip:focus-visible {
  outline: 2px solid $primary;
  outline-offset: 2px;
}

// Print styles
@media print {
  .ai-chat-widget {
    display: none;
  }
}

// Animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-4px);
  }

  60% {
    transform: translateY(-2px);
  }
}

@keyframes loadingDots {

  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}