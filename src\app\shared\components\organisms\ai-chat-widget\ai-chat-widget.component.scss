@use 'sass:color';
@import '../../../../../styles/variables/_colors';

// AI Chat Widget Container
.ai-chat-widget {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 9999;
  font-family: $font-family-base;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &--dragging {
    z-index: 10000;

    .chat-bubble {
      transform: scale(1.1);
      box-shadow: 0 20px 60px rgba($black, 0.25), 0 8px 32px rgba($black, 0.2);
      cursor: grabbing;
    }
  }

  @media (max-width: 768px) {
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
  }
}

// Floating Chat Bubble (Collapsed State)
.chat-bubble {
  position: relative;
  width: 68px;
  height: 68px;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  border-radius: 20px;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 16px 48px rgba($black, 0.12),
    0 8px 24px rgba($black, 0.08),
    0 4px 12px rgba($black, 0.04),
    inset 0 1px 0 rgba($white, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  color: $white;
  border: 1px solid rgba($white, 0.12);
  backdrop-filter: blur(24px);
  user-select: none;

  // Professional multi-layer gradient overlay
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 20px;
    background: linear-gradient(135deg,
      rgba($white, 0.12) 0%,
      transparent 30%,
      transparent 70%,
      rgba($black, 0.08) 100%);
    pointer-events: none;
  }

  // Inner glow effect
  &::after {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 18px;
    background: linear-gradient(135deg,
      rgba($white, 0.05) 0%,
      transparent 50%);
    pointer-events: none;
  }

  &:hover:not(.dragging) {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
      0 24px 64px rgba($black, 0.16),
      0 12px 32px rgba($black, 0.12),
      0 6px 16px rgba($black, 0.08),
      inset 0 1px 0 rgba($white, 0.15);
    border-color: rgba($primary, 0.4);

    .chat-bubble__tooltip {
      opacity: 1;
      visibility: visible;
      transform: translateX(-50%) translateY(-16px);
    }
  }

  &:focus {
    outline: 3px solid rgba($primary, 0.5);
    outline-offset: 4px;
  }

  &:active:not(.dragging) {
    transform: translateY(-6px) scale(1.02);
  }

  &.dragging {
    cursor: grabbing;
    transform: scale(1.08);
    box-shadow:
      0 32px 80px rgba($black, 0.2),
      0 16px 40px rgba($black, 0.16),
      0 8px 20px rgba($black, 0.12);
    border-color: rgba($primary, 0.6);
  }

  &__icon {
    font-size: 1.875rem;
    animation: pulse 4s infinite;
    filter: drop-shadow(0 2px 6px rgba($black, 0.3));
    z-index: 1;
  }

  &__notification {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 26px;
    height: 26px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid $white;
    animation: bounce 2s infinite;
    box-shadow:
      0 6px 16px rgba(#ef4444, 0.4),
      0 3px 8px rgba(#ef4444, 0.2);

    .notification-dot {
      width: 12px;
      height: 12px;
      background: $white;
      border-radius: 50%;
      box-shadow: 0 1px 2px rgba($black, 0.2);
    }
  }

  &__tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-12px);
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: $white;
    padding: 0.875rem 1.25rem;
    border-radius: 14px;
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    box-shadow:
      0 12px 32px rgba($black, 0.24),
      0 6px 16px rgba($black, 0.16);
    border: 1px solid rgba($white, 0.12);
    backdrop-filter: blur(24px);

    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 10px solid transparent;
      border-top-color: #1f2937;
    }
  }
}

// Chat Panel (Expanded State)
.chat-panel {
  width: 440px;
  height: 680px;
  background: linear-gradient(145deg, $white 0%, #fafbfc 100%);
  border-radius: 24px;
  box-shadow:
    0 32px 96px rgba($black, 0.08),
    0 16px 48px rgba($black, 0.06),
    0 8px 24px rgba($black, 0.04),
    0 4px 12px rgba($black, 0.02),
    inset 0 1px 0 rgba($white, 0.8);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba($gray-200, 0.6);
  backdrop-filter: blur(32px);
  position: relative;

  // Multi-layer gradient overlay for premium depth
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba($primary, 0.3) 25%,
      rgba($primary, 0.5) 50%,
      rgba($primary, 0.3) 75%,
      transparent 100%);
    z-index: 1;
  }

  // Subtle inner shadow for depth
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 24px;
    box-shadow: inset 0 2px 4px rgba($black, 0.02);
    pointer-events: none;
    z-index: 0;
  }

  @media (max-width: 768px) {
    width: 100%;
    height: 75vh;
    max-height: 680px;
    border-radius: 24px 24px 0 0;
  }

  @media (max-width: 480px) {
    height: 85vh;
    border-radius: 20px 20px 0 0;
  }
}

// Chat Header
.chat-header {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  color: $white;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba($white, 0.06);
  position: relative;
  z-index: 2;

  // Premium gradient overlay with multiple layers
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
      rgba($white, 0.08) 0%,
      transparent 30%,
      transparent 70%,
      rgba($black, 0.04) 100%);
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba($primary, 0.4) 25%,
      rgba($primary, 0.6) 50%,
      rgba($primary, 0.4) 75%,
      transparent 100%);
  }

  &__content {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    flex: 1;
  }

  &__avatar {
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, $primary 0%, color.adjust($primary, $lightness: 20%) 50%, color.adjust($primary, $lightness: 10%) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.625rem;
    box-shadow:
      0 8px 24px rgba($primary, 0.3),
      0 4px 12px rgba($primary, 0.2),
      inset 0 1px 0 rgba($white, 0.2);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 16px;
      background: linear-gradient(135deg,
        rgba($white, 0.15) 0%,
        transparent 50%,
        rgba($black, 0.05) 100%);
    }

    &::after {
      content: '';
      position: absolute;
      inset: 2px;
      border-radius: 14px;
      background: linear-gradient(135deg,
        rgba($white, 0.1) 0%,
        transparent 100%);
    }
  }

  &__info {
    flex: 1;
  }

  &__title {
    margin: 0 0 0.375rem 0;
    font-size: 1.25rem;
    font-weight: $font-weight-bold;
    line-height: 1.2;
    letter-spacing: -0.025em;
    text-shadow: 0 1px 2px rgba($black, 0.1);
  }

  &__status {
    margin: 0;
    font-size: $font-size-sm;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 0.625rem;
    font-weight: $font-weight-medium;

    &--typing {
      color: #10b981;
      text-shadow: 0 0 8px rgba(#10b981, 0.3);
    }
  }

  &__actions {
    display: flex;
    gap: 0.75rem;
  }

  &__action {
    width: 40px;
    height: 40px;
    background: rgba($white, 0.06);
    border: 1px solid rgba($white, 0.1);
    border-radius: 12px;
    color: $white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(16px);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 12px;
      background: linear-gradient(135deg,
        rgba($white, 0.08) 0%,
        transparent 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      background: rgba($white, 0.12);
      border-color: rgba($white, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba($black, 0.15);

      &::before {
        opacity: 1;
      }
    }

    &:focus {
      outline: 2px solid rgba($primary, 0.6);
      outline-offset: 3px;
    }

    &--close:hover {
      background: rgba(#ef4444, 0.8);
      border-color: #ef4444;
      box-shadow: 0 8px 24px rgba(#ef4444, 0.3);
    }
  }
}

// Error Banner
.error-banner {
  background: linear-gradient(135deg, rgba($danger, 0.08) 0%, rgba($danger, 0.04) 100%);
  border-bottom: 1px solid rgba($danger, 0.15);
  padding: 1rem 1.5rem;
  backdrop-filter: blur(10px);

  &__content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: $danger;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
  }

  &__message {
    flex: 1;
    line-height: $line-height-base;
  }

  &__retry,
  &__close {
    background: rgba($danger, 0.1);
    border: 1px solid rgba($danger, 0.2);
    color: $danger;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;

    &:hover {
      background: rgba($danger, 0.15);
      border-color: rgba($danger, 0.3);
      transform: translateY(-1px);
    }

    &:focus {
      outline: 2px solid rgba($danger, 0.3);
      outline-offset: 2px;
    }
  }
}

// Messages Container
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  background: linear-gradient(180deg, rgba($gray-50, 0.3) 0%, transparent 100%);

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba($gray-100, 0.5);
    border-radius: 4px;
    margin: 8px 0;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, $gray-300 0%, $gray-400 100%);
    border-radius: 4px;
    border: 1px solid rgba($white, 0.2);

    &:hover {
      background: linear-gradient(180deg, $gray-400 0%, $gray-500 100%);
    }
  }
}

// Welcome Message
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2.5rem 1.5rem;
  background: linear-gradient(135deg, rgba($primary, 0.02) 0%, rgba($gray-50, 0.5) 100%);
  border-radius: 16px;
  margin: 1rem 0;
  border: 1px solid rgba($primary, 0.08);

  &__avatar {
    width: 72px;
    height: 72px;
    background: linear-gradient(135deg, $primary 0%, color.adjust($primary, $lightness: 15%) 100%);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $white;
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 24px rgba($primary, 0.2);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 18px;
      background: linear-gradient(135deg, transparent 0%, rgba($white, 0.15) 100%);
    }
  }

  &__content {
    max-width: 320px;

    h4 {
      margin: 0 0 0.75rem 0;
      color: $text-primary;
      font-size: 1.25rem;
      font-weight: $font-weight-bold;
      letter-spacing: -0.025em;
    }

    p {
      margin: 0 0 2rem 0;
      color: $text-secondary;
      font-size: $font-size-base;
      line-height: $line-height-relaxed;
      font-weight: $font-weight-regular;
    }
  }

  &__suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    max-width: 100%;
  }
}

.suggestion-chip {
  background: linear-gradient(135deg, $white 0%, $gray-50 100%);
  border: 1px solid $gray-200;
  border-radius: 24px;
  padding: 0.75rem 1.25rem;
  font-size: $font-size-sm;
  color: $text-primary;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: $font-weight-medium;
  box-shadow: 0 2px 8px rgba($black, 0.04);
  backdrop-filter: blur(10px);

  &:hover {
    background: linear-gradient(135deg, $primary 0%, color.adjust($primary, $lightness: 10%) 100%);
    color: $white;
    border-color: $primary;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba($primary, 0.25);
  }

  &:active {
    transform: translateY(0);
  }
}

// Messages List
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

// Individual Messages
.message {
  display: flex;
  gap: 1.125rem;
  max-width: 85%;
  margin-bottom: 0.75rem;

  &--user {
    align-self: flex-end;
    flex-direction: row-reverse;

    .message__bubble {
      background: linear-gradient(135deg, $primary 0%, color.adjust($primary, $lightness: -8%) 50%, color.adjust($primary, $lightness: -12%) 100%);
      color: $white;
      border-bottom-right-radius: 6px;
      box-shadow:
        0 8px 24px rgba($primary, 0.25),
        0 4px 12px rgba($primary, 0.15),
        0 2px 6px rgba($primary, 0.1),
        inset 0 1px 0 rgba($white, 0.2);
      border: 1px solid rgba($white, 0.1);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: linear-gradient(135deg,
          rgba($white, 0.15) 0%,
          transparent 50%,
          rgba($black, 0.05) 100%);
        pointer-events: none;
      }
    }

    .message__timestamp {
      color: rgba($white, 0.9);
      text-align: right;
    }
  }

  &--ai {
    align-self: flex-start;

    .message__bubble {
      background: linear-gradient(135deg, $white 0%, #f8fafc 50%, #f1f5f9 100%);
      color: $text-primary;
      border: 1px solid rgba($gray-200, 0.8);
      border-bottom-left-radius: 6px;
      box-shadow:
        0 6px 20px rgba($black, 0.04),
        0 3px 10px rgba($black, 0.03),
        0 1px 4px rgba($black, 0.02),
        inset 0 1px 0 rgba($white, 0.8);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: linear-gradient(135deg,
          rgba($white, 0.6) 0%,
          transparent 100%);
        pointer-events: none;
      }
    }

    .message__timestamp {
      color: $text-muted;
    }
  }

  &--error {
    .message__bubble {
      background: linear-gradient(135deg, rgba(#fef2f2, 0.9) 0%, rgba(#fee2e2, 0.8) 100%);
      border: 1px solid rgba(#fca5a5, 0.4);
      color: #dc2626;
      box-shadow:
        0 4px 16px rgba(#ef4444, 0.08),
        0 2px 8px rgba(#ef4444, 0.06);
    }
  }

  &__avatar {
    width: 40px;
    height: 40px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 1.125rem;
    margin-top: 0.25rem;
    position: relative;
  }

  &--user &__avatar {
    background: linear-gradient(135deg, $primary 0%, color.adjust($primary, $lightness: 15%) 50%, color.adjust($primary, $lightness: 8%) 100%);
    color: $white;
    box-shadow:
      0 6px 18px rgba($primary, 0.25),
      0 3px 9px rgba($primary, 0.15),
      inset 0 1px 0 rgba($white, 0.2);
    border: 1px solid rgba($white, 0.1);

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 14px;
      background: linear-gradient(135deg,
        rgba($white, 0.2) 0%,
        transparent 100%);
    }
  }

  &--ai &__avatar {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 50%, #94a3b8 100%);
    color: #475569;
    box-shadow:
      0 4px 12px rgba($black, 0.08),
      0 2px 6px rgba($black, 0.04),
      inset 0 1px 0 rgba($white, 0.6);
    border: 1px solid rgba($white, 0.8);

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 14px;
      background: linear-gradient(135deg,
        rgba($white, 0.4) 0%,
        transparent 100%);
    }
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__bubble {
    padding: 1.125rem 1.5rem;
    border-radius: 22px;
    position: relative;
    word-wrap: break-word;
    backdrop-filter: blur(16px);

    &--loading {
      padding: 1.375rem 1.5rem;
    }
  }

  &__text {
    font-size: $font-size-base;
    line-height: $line-height-relaxed;
    margin-bottom: 0.625rem;
    font-weight: $font-weight-regular;
  }

  &__timestamp {
    font-size: $font-size-xs;
    opacity: 0.8;
    margin-top: 0.625rem;
    font-weight: $font-weight-medium;
  }
}

// Loading and Typing Indicators
.loading-dots,
.typing-indicator {
  display: flex;
  gap: 0.375rem;
  align-items: center;

  span {
    width: 8px;
    height: 8px;
    background: currentColor;
    border-radius: 50%;
    animation: loadingDots 1.6s infinite ease-in-out;
    opacity: 0.4;

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

.typing-indicator {
  span {
    background: #10b981;
    box-shadow: 0 0 8px rgba(#10b981, 0.3);
  }
}

// Chat Input
.chat-input {
  border-top: 1px solid rgba($gray-200, 0.6);
  padding: 1.5rem;
  background: linear-gradient(180deg, rgba($gray-50, 0.3) 0%, $white 100%);
  backdrop-filter: blur(20px);

  &__form {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  &__container {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
    background: $white;
    border: 1px solid $gray-200;
    border-radius: 16px;
    padding: 0.5rem;
    box-shadow: 0 2px 8px rgba($black, 0.04);
    transition: all 0.2s ease;

    &:focus-within {
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.08), 0 4px 16px rgba($black, 0.08);
    }
  }

  &__field {
    flex: 1;
    border: none;
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-size: $font-size-base;
    font-family: inherit;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    background: transparent;
    color: $text-primary;
    transition: all 0.2s ease;
    line-height: $line-height-base;

    &:focus {
      outline: none;
    }

    &:disabled {
      background: $gray-50;
      color: $text-muted;
      cursor: not-allowed;
    }

    &::placeholder {
      color: $text-muted;
      font-weight: $font-weight-regular;
    }
  }

  &__send {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, $primary 0%, color.adjust($primary, $lightness: -5%) 100%);
    border: none;
    border-radius: 12px;
    color: $white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba($primary, 0.2);

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, color.adjust($primary, $lightness: -5%) 0%, color.adjust($primary, $lightness: -10%) 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba($primary, 0.3);
    }

    &:disabled {
      background: $gray-300;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    &:focus {
      outline: 2px solid rgba($primary, 0.3);
      outline-offset: 2px;
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }
  }

  &__counter {
    font-size: $font-size-xs;
    color: $text-muted;
    text-align: right;
    font-weight: $font-weight-medium;
    padding: 0 0.5rem;
  }
}

// Animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .ai-chat-widget {
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
  }

  .chat-panel {
    width: 100%;
    height: 75vh;
    max-height: 640px;
  }
}

@media (max-width: 480px) {
  .ai-chat-widget {
    bottom: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
  }

  .chat-panel {
    height: 85vh;
    border-radius: 16px 16px 0 0;
  }

  .chat-bubble {
    width: 56px;
    height: 56px;
    border-radius: 16px;

    &__icon {
      font-size: 1.5rem;
    }
  }

  .chat-header {
    padding: 1rem 1.25rem;

    &__avatar {
      width: 40px;
      height: 40px;
      font-size: 1.25rem;
      border-radius: 10px;
    }

    &__title {
      font-size: 1rem;
    }

    &__status {
      font-size: $font-size-xs;
    }
  }

  .chat-messages {
    padding: 1rem;
  }

  .welcome-message {
    padding: 2rem 1rem;

    &__avatar {
      width: 64px;
      height: 64px;
      border-radius: 16px;
    }
  }

  .message {
    max-width: 92%;

    &__avatar {
      width: 32px;
      height: 32px;
      font-size: 0.875rem;
      border-radius: 10px;
    }

    &__bubble {
      padding: 0.875rem 1rem;
      border-radius: 16px;
    }

    &__text {
      font-size: $font-size-sm;
    }
  }

  .chat-input {
    padding: 1.25rem;

    &__container {
      border-radius: 14px;
    }

    &__field {
      padding: 0.75rem 1rem;
      font-size: $font-size-sm;
      min-height: 40px;
    }

    &__send {
      width: 40px;
      height: 40px;
      border-radius: 10px;
    }
  }

  .suggestion-chip {
    padding: 0.625rem 1rem;
    font-size: $font-size-xs;
    border-radius: 20px;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .chat-bubble {
    border: 2px solid $white;
  }

  .message__bubble {
    border: 1px solid currentColor;
  }

  .chat-input__field {
    border: 2px solid $input-border-color;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .chat-bubble,
  .chat-panel,
  .message__bubble,
  .chat-input__send {
    transition: none;
    animation: none;
  }

  .chat-bubble__icon {
    animation: none;
  }

  .chat-bubble__notification {
    animation: none;
  }

  .loading-dots span,
  .typing-indicator span {
    animation: none;
  }
}

// Dark theme support (if implemented)
@media (prefers-color-scheme: dark) {
  .ai-chat-widget {
    // Dark theme styles would go here
    // This can be expanded when dark theme is implemented
  }
}

// Focus visible support for better accessibility
.chat-bubble:focus-visible,
.chat-header__action:focus-visible,
.chat-input__send:focus-visible,
.suggestion-chip:focus-visible {
  outline: 2px solid $primary;
  outline-offset: 2px;
}

// Print styles
@media print {
  .ai-chat-widget {
    display: none;
  }
}

// Utility Classes
:global(.user-select-none) {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

// Animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(24px) scale(0.92);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-6px) scale(1.05);
  }
  60% {
    transform: translateY(-3px) scale(1.02);
  }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.4;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

// Drag animation
@keyframes dragPulse {
  0%, 100% {
    box-shadow:
      0 32px 80px rgba($black, 0.2),
      0 16px 40px rgba($black, 0.16),
      0 8px 20px rgba($black, 0.12);
  }
  50% {
    box-shadow:
      0 40px 100px rgba($black, 0.25),
      0 20px 50px rgba($black, 0.2),
      0 10px 25px rgba($black, 0.15);
  }
}
