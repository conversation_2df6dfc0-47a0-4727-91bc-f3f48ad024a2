// Variables
$primary-color: #ff6b35;
$primary-light: #fff0eb;
$text-dark: #111827;
$text-medium: #4b5563;
$text-light: #6b7280;
$border-color: #e5e7eb;
$border-light: #f0f0f0;
$background-light: #f9fafb;
$background-lighter: #f3f4f6;
$success-color: #10b981;
$success-light: #ecfdf5;
$warning-color: #f59e0b;
$warning-light: #fffbeb;
$danger-color: #ef4444;
$danger-light: #fef2f2;
$white: #ffffff;
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 16px;
$border-radius-full: 20px;

// Main container
.data-table {
  width: 100%;
  overflow-x: auto;
  border-radius: $border-radius-lg;
  background-color: $white;

  // Table element
  &__table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    box-shadow: $shadow-sm;
  }

  // Table head
  &__head {
    background: linear-gradient(to right, $background-light, $background-lighter);
    position: sticky;
    top: 0;
    z-index: 10;
  }

  // Table row
  &__row {
    position: relative;
    cursor: pointer;
    will-change: background-color;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba($primary-color, 0.03);
      transform: translateY(-1px);

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        box-shadow: $shadow-md;
        pointer-events: none;
        z-index: -1;
      }
    }

    &--highlighted {
      background-color: $background-light;
    }

    &--selected {
      background-color: rgba($primary-color, 0.08);
      border-left: 3px solid $primary-color;
      position: relative;
      z-index: 1;

      td {
        color: $text-dark;
        font-weight: 500;
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        box-shadow: 0 0 15px rgba($primary-color, 0.15);
        z-index: -1;
        pointer-events: none;
      }

      &:hover {
        background-color: rgba($primary-color, 0.12);
      }
    }

    &--empty {
      cursor: default;

      &:hover {
        background-color: transparent;
        transform: none;

        &::after {
          display: none;
        }
      }
    }
  }

  // Table cell
  &__cell {
    padding: 18px 24px;
    border-bottom: 1px solid $border-light;
    color: $text-medium;
    transition: all 0.2s ease;

    &--header {
      padding: 18px 24px;
      font-weight: 600;
      text-align: left;
      color: $text-medium;
      border-bottom: 1px solid $border-color;
      white-space: nowrap;
      transition: background-color 0.2s ease;
      position: relative;

      &:first-child {
        border-top-left-radius: $border-radius-lg;
      }

      &:last-child {
        border-top-right-radius: $border-radius-lg;
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: transparent;
        transition: background-color 0.2s ease;
      }
    }

    &--sortable {
      cursor: pointer;

      &:hover::after {
        background-color: $border-color;
      }
    }

    &--select {
      width: 48px;
      text-align: center;
    }

    &--empty {
      text-align: center;
      padding: 32px;
      color: $text-light;
    }
  }

  // Row selection - adjust first cell padding to compensate for the border
  &__row--selected &__cell:first-child {
    padding-left: 21px;
  }

  // Header
  &__header {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__header-icon {
    font-size: 16px;
    color: $text-light;
  }

  &__header-text {
    font-weight: 600;
  }

  &__sort-icon {
    margin-left: 4px;
    font-size: 12px;
    color: $text-light;
  }

  // Checkbox
  &__checkbox {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
  }

  &__checkbox-input {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 2;
  }

  &__checkbox-label {
    position: absolute;
    top: 0;
    left: 0;
    width: 18px;
    height: 18px;
    border-radius: $border-radius-sm;
    background-color: $white;
    border: 1px solid $border-color;
    cursor: pointer;
    transition: all 0.2s ease;

    &::after {
      content: '';
      position: absolute;
      top: 5px;
      left: 4px;
      width: 8px;
      height: 4px;
      border-left: 2px solid $white;
      border-bottom: 2px solid $white;
      transform: rotate(-45deg);
      opacity: 0;
      transition: opacity 0.2s ease;
    }
  }

  &__checkbox-input:checked + &__checkbox-label {
    background-color: $primary-color;
    border-color: $primary-color;

    &::after {
      opacity: 1;
    }
  }

  // Badge
  &__badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 14px;
    font-size: 12px;
    font-weight: 600;
    border-radius: $border-radius-full;
    text-align: center;
    gap: 8px;
    box-shadow: $shadow-sm;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
      transform: translateX(-100%);
      transition: transform 0.6s ease;
    }

    &:hover::before {
      transform: translateX(100%);
    }

    &--primary {
      background-color: $primary-light;
      color: $primary-color;
      border: 1px solid rgba($primary-color, 0.2);

      .data-table__badge-dot {
        background-color: $primary-color;
      }
    }

    &--success {
      background-color: $success-light;
      color: $success-color;
      border: 1px solid rgba($success-color, 0.2);

      .data-table__badge-dot {
        background-color: $success-color;
      }
    }

    &--warning {
      background-color: $warning-light;
      color: $warning-color;
      border: 1px solid rgba($warning-color, 0.2);

      .data-table__badge-dot {
        background-color: $warning-color;
      }
    }

    &--danger {
      background-color: $danger-light;
      color: $danger-color;
      border: 1px solid rgba($danger-color, 0.2);

      .data-table__badge-dot {
        background-color: $danger-color;
      }
    }
  }

  &__badge-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.6);
  }

  // Avatar
  &__avatar {
    display: flex;
    align-items: center;
    gap: 14px;
  }

  &__avatar-name {
    font-weight: 600;
    color: $text-dark;
    transition: color 0.2s ease;

    &:hover {
      color: $primary-color;
    }
  }

  &__avatar-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid $white;
    box-shadow: $shadow-md;
    flex-shrink: 0;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;

    &:hover {
      box-shadow: $shadow-lg;
      border: 2px solid $primary-color;
    }
  }

  // Selected row avatar styling
  &__row--selected &__avatar-name {
    color: $primary-color;
    font-weight: 600;
  }

  &__row--selected &__avatar-image {
    border-color: $primary-color;
  }

  // Location
  &__location {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
  }

  &__location-flag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: $shadow-md;
    background-color: $background-lighter;
    border: 1px solid $border-color;
    font-size: 16px;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: $shadow-lg;
    }
  }

  // Actions
  &__actions {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
    white-space: nowrap;
  }

  &__action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: $border-radius-md;
    border: none;
    background-color: $white;
    color: $text-medium;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: $shadow-sm;
    border: 1px solid $border-color;

    &:hover {
      transform: translateY(-1px);
      box-shadow: $shadow-md;
    }

    &:active {
      transform: translateY(0);
      box-shadow: $shadow-sm;
    }

    &--view {
      color: $primary-color;

      &:hover {
        background-color: rgba($primary-color, 0.1);
        border-color: $primary-color;
      }
    }

    &--edit {
      color: $warning-color;

      &:hover {
        background-color: rgba($warning-color, 0.1);
        border-color: $warning-color;
      }
    }

    &--delete {
      color: $danger-color;

      &:hover {
        background-color: rgba($danger-color, 0.1);
        border-color: $danger-color;
      }
    }

    &--custom {
      color: $text-light;
      width: auto;
      padding: 0 12px;
      font-size: 12px;
      font-weight: 500;

      &:hover {
        background-color: rgba($text-light, 0.1);
      }
    }
  }

  &__action-icon {
    font-size: 16px;
    position: relative;
    z-index: 1;
  }

  // Date
  &__date {
    font-weight: 500;
  }

  // Text
  &__text {
    font-weight: 500;
  }
}
