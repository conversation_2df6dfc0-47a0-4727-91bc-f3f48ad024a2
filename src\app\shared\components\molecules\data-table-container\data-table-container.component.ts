import { Component, Input, Output, EventEmitter, TemplateRef, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTableComponent, TableColumn } from '../../atoms/data-table/data-table.component';
import { PaginationComponent } from '../../atoms/pagination/pagination.component';
import { SearchInputComponent } from '../../atoms/search-input/search-input.component';

import { MultiSelectDropdownComponent } from '../../molecules/multi-select-dropdown/multi-select-dropdown.component';
import { DropdownOption } from '../../atoms/dropdown-select/dropdown-select.component';

@Component({
  selector: 'app-data-table-container',
  standalone: true,
  imports: [CommonModule, DataTableComponent, PaginationComponent, SearchInputComponent, MultiSelectDropdownComponent],
  template: `
    <div class="data-table-container">
      <div class="data-table-header" *ngIf="showHeader">
        <div class="data-table-title-section">
          <h3 class="data-table-title" *ngIf="tableTitle">{{ tableTitle }}</h3>
          <div class="data-table-controls">
            <div class="data-table-search">
              <app-search-input
                [placeholder]="'Search employees...'"
                (search)="onSearch($event)"
                (clear)="onClearSearch()">
              </app-search-input>
            </div>
            <div class="data-table-filters">
              <app-multi-select-dropdown
                [options]="departmentOptions"
                [placeholder]="departmentOptions.length === 0 ? 'Loading departments...' : 'Filter by Department'"
                [searchPlaceholder]="'Search departments...'"
                [disabled]="departmentOptions.length === 0"
                [value]="selectedDepartmentIds"
                (selectionChange)="onDepartmentFilterChange($event)">
              </app-multi-select-dropdown>
            </div>
          </div>
        </div>
      </div>

      <app-data-table
        [columns]="columns"
        [data]="data"
        [selectable]="selectable"
        [customTemplate]="customTemplate"
        [dynamicColumns]="dynamicColumns"
        [excludeColumns]="excludeColumns"
        [columnOrder]="columnOrder"
        [columnTypes]="columnTypes"
        [columnLabels]="columnLabels"
        (rowClick)="onRowClick($event)"
        (selectionChange)="onSelectionChange($event)"
        (viewAction)="onViewAction($event)"
        (editAction)="onEditAction($event)"
        (deleteAction)="onDeleteAction($event)"
        (customAction)="onCustomAction($event)">
      </app-data-table>

      <app-pagination
        *ngIf="showPagination"
        [currentPage]="currentPage"
        [pageSize]="pageSize"
        [totalItems]="totalItems"
        [pageSizeOptions]="pageSizeOptions"
        (pageChange)="onPageChange($event)"
        (pageSizeChange)="onPageSizeChange($event)">
      </app-pagination>
    </div>
  `,
  styles: [`
    .data-table-container {
      background-color: #fff;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      margin-bottom: 24px;
    }

    .data-table-header {
      padding: 20px 24px;
      border-bottom: 1px solid #e5e7eb;
    }

    .data-table-title-section {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .data-table-title {
      font-size: 18px;
      font-weight: 600;
      color: #374151;
      margin: 0;
    }

    .data-table-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .data-table-search {
      width: 280px;
    }

    .data-table-filters {
      display: flex;
      gap: 12px;
      min-width: 220px; /* Match the dropdown width */
    }

    .filter-button {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background-color: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      font-size: 14px;
      color: #4b5563;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .filter-button:hover {
      background-color: #f3f4f6;
      border-color: #d1d5db;
    }

    .filter-icon {
      font-size: 12px;
      color: #9ca3af;
    }

    .filter-arrow {
      font-size: 10px;
      color: #9ca3af;
      margin-left: 4px;
    }

    .action-button {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background-color: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      color: #4b5563;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .action-button:hover {
      background-color: #f3f4f6;
      border-color: #d1d5db;
    }

    .export-button {
      background-color: #f9fafb;
    }

    @media (max-width: 768px) {
      .data-table-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }

      .data-table-search {
        width: 100%;
      }

      .data-table-filters {
        flex-wrap: wrap;
        width: 100%;
      }
    }
  `]
})
export class DataTableContainerComponent implements OnInit {
  ngOnInit(): void {
    console.log('DataTableContainerComponent initialized');
    console.log('Department options:', this.departmentOptions);
  }
  @Input() columns: TableColumn[] = [];
  @Input() data: any[] = [];
  @Input() selectable: boolean = false;
  @Input() customTemplate: TemplateRef<any> | null = null;
  @Input() showPagination: boolean = true;
  @Input() showHeader: boolean = true;
  @Input() tableTitle: string = '';
  @Input() currentPage: number = 1;
  @Input() pageSize: number = 10;
  @Input() totalItems: number = 0;
  @Input() pageSizeOptions: number[] = [10, 25, 50, 100];

  // Dynamic columns support
  @Input() dynamicColumns: boolean = false;
  @Input() excludeColumns: string[] = [];
  @Input() columnOrder: string[] = [];
  @Input() columnTypes: {[key: string]: string} = {};
  @Input() columnLabels: {[key: string]: string} = {};

  // Filter options
  @Input() filters: { label: string, value: string }[] = [
    { label: 'Department', value: 'department' },
    { label: 'Lifecycle', value: 'lifecycle' },
    { label: 'Status', value: 'status' }
  ];

  // Department filter options
  @Input() departmentOptions: DropdownOption[] = [];
  @Input() selectedDepartmentIds: (string | number)[] = [];

  @Output() filterClick = new EventEmitter<{ label: string, value: string }>();
  @Output() departmentFilterChange = new EventEmitter<(string | number)[]>();
  @Output() search = new EventEmitter<string>();
  @Output() clearSearch = new EventEmitter<void>();
  @Output() export = new EventEmitter<void>();
  @Output() addEmployee = new EventEmitter<void>();

  @Output() rowClick = new EventEmitter<any>();
  @Output() selectionChange = new EventEmitter<any[]>();
  @Output() pageChange = new EventEmitter<number>();
  @Output() pageSizeChange = new EventEmitter<number>();
  @Output() viewAction = new EventEmitter<any>();
  @Output() editAction = new EventEmitter<any>();
  @Output() deleteAction = new EventEmitter<any>();
  @Output() customAction = new EventEmitter<{action: string, row: any}>();

  onRowClick(row: any): void {
    this.rowClick.emit(row);
  }

  onSelectionChange(selection: any[]): void {
    this.selectionChange.emit(selection);
  }

  onPageChange(page: number): void {
    this.pageChange.emit(page);
  }

  onPageSizeChange(size: number): void {
    this.pageSizeChange.emit(size);
  }

  onFilterClick(filter: { label: string, value: string }): void {
    this.filterClick.emit(filter);
  }

  onViewAction(row: any): void {
    this.viewAction.emit(row);
  }

  onEditAction(row: any): void {
    this.editAction.emit(row);
  }

  onDeleteAction(row: any): void {
    this.deleteAction.emit(row);
  }

  onCustomAction(event: {action: string, row: any}): void {
    this.customAction.emit(event);
  }

  onSearch(query: string): void {
    this.search.emit(query);
  }

  onClearSearch(): void {
    this.clearSearch.emit();
  }

  onExport(): void {
    this.export.emit();
  }

  onAddEmployee(): void {
    this.addEmployee.emit();
  }

  onDepartmentFilterChange(selectedDepartments: (string | number)[]): void {
    this.departmentFilterChange.emit(selectedDepartments);
  }
}
