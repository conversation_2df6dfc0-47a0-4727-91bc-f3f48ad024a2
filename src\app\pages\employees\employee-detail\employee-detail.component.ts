import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { EmployeesStore, Employee, CompaniesStore } from '../../../core/state';
// import { ButtonComponent } from '../../../shared/components/atoms/button/button.component';
import { ModalService } from '../../../core/services/modal.service';
import { ToastService } from '../../../shared/services/toast.service';
import { EmployeeManagementService } from '../../../core/services/employee-management.service';
import { EmployeeDetailsService } from '../../../core/services/employee-details.service';
import { EmployeeDocumentsSectionComponent } from '../../../shared/components/organisms/employee-documents-section/employee-documents-section.component';

// Import management components
// import { EmployeeManagementDashboardComponent } from '../../../shared/components/organisms/employee-management-dashboard/employee-management-dashboard.component';

// Management section type
export type ManagementSection = 'overview' | 'salary' | 'hike' | 'okr' | 'performance' | 'leave' | 'attendance' | 'training' | 'asset';

@Component({
  selector: 'app-employee-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, EmployeeDocumentsSectionComponent],
  templateUrl: './employee-detail.component.html',
  styleUrls: ['./employee-detail.component.scss']
})
export class EmployeeDetailComponent implements OnInit, OnDestroy {
  private employeesStore = inject(EmployeesStore);
  private companiesStore = inject(CompaniesStore);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private fb = inject(FormBuilder);
  private coreModalService = inject(ModalService);
  private toastService = inject(ToastService);
  private employeeManagementService = inject(EmployeeManagementService);
  private employeeDetailsService = inject(EmployeeDetailsService);

  // Access state from the store using signals
  employee = this.employeesStore.selectedEmployee;
  isLoading = this.employeesStore.isLoading;
  error = this.employeesStore.error;

  // Companies for dropdown
  companies = this.companiesStore.companies;

  // Form state
  employeeForm: FormGroup;
  isEditing = false;
  isSaving = false;
  formInitialized = false;
  lastError: string | null = null;
  onDestroy: () => void = () => {};

  // Tab state
  activeTab = 'details'; // 'details', 'management', 'documents', 'settings'

  // Management sections
  activeManagementSection: ManagementSection = 'overview';
  activeManagementTab = 'projects'; // 'projects', 'assets'

  // Expandable sections state
  aboutExpanded = false;
  bankInfoExpanded = false;
  familyInfoExpanded = false;
  educationExpanded = false;
  experienceExpanded = false;

  // Management data
  employeeSalaries: any[] = [];
  employeeAssets: any[] = [];
  employeeLeaves: any[] = [];
  employeeAttendance: any[] = [];
  employeeTrainings: any[] = [];
  employeePerformance: any[] = [];
  employeeOkrs: any[] = [];
  employeeHikes: any[] = [];

  // Loading states for management sections
  managementLoading = {
    salaries: false,
    assets: false,
    leaves: false,
    attendance: false,
    trainings: false,
    performance: false,
    okrs: false,
    hikes: false
  };

  constructor() {
    this.employeeForm = this.fb.group({
      first_name: ['', Validators.required],
      last_name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      position: [''],
      department: [''],
      hire_date: [''],
      salary: [''],
      status: ['active'],
      company_id: ['', Validators.required],
      address: [''],
      city: [''],
      state: [''],
      zip: [''],
      country: ['']
    });
  }

  ngOnInit(): void {
    // Load companies for dropdown
    this.companiesStore.loadCompanies();

    // Get employee ID from route params
    this.route.paramMap.subscribe(params => {
      const id = Number(params.get('id'));
      if (id) {
        // Load employee details
        this.employeesStore.loadEmployee(id);
        // Load management data
        this.loadManagementData(id);
      }
    });

    // Check if we're in edit mode from route data
    this.route.data.subscribe(data => {
      if (data['mode'] === 'edit') {
        this.isEditing = true;
      }
    });

    // Set up an effect to initialize the form when the employee data changes
    // Since we can't use subscribe with signals, we'll use a different approach

    // Create a watcher for employee changes
    const employeeWatcher = setInterval(() => {
      const currentEmployee = this.employee();
      if (currentEmployee && !this.formInitialized) {
        this.initForm(currentEmployee);
        this.formInitialized = true;
      }
    }, 100);

    // Create a watcher for error changes
    const errorWatcher = setInterval(() => {
      const currentError = this.error();
      if (currentError && currentError !== this.lastError) {
        console.error('Error loading employee:', currentError);
        this.lastError = currentError;
        // You could show a toast or notification here
      }
    }, 100);

    // Clean up the watchers when the component is destroyed
    this.onDestroy = () => {
      clearInterval(employeeWatcher);
      clearInterval(errorWatcher);
    };
  }

  private initForm(employee: Employee): void {
    this.employeeForm.patchValue({
      first_name: employee.first_name,
      last_name: employee.last_name,
      email: employee.email,
      phone: employee.phone || '',
      position: employee.position || '',
      department: employee.department || '',
      hire_date: employee.hire_date || '',
      salary: employee.salary || '',
      status: employee.status || 'active',
      company_id: employee.company_id,
      address: employee.address || '',
      city: employee.city || '',
      state: employee.state || '',
      zip: employee.zip || '',
      country: employee.country || ''
    });
  }

  toggleEdit(): void {
    this.isEditing = !this.isEditing;
    if (!this.isEditing && this.employee()) {
      // Reset form when canceling edit
      this.initForm(this.employee()!);
    }
  }

  saveEmployee(): void {
    if (this.employeeForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.employeeForm.controls).forEach(key => {
        const control = this.employeeForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSaving = true;
    const employee = this.employee();

    if (employee) {
      // Update existing employee
      this.employeesStore.updateEmployee({
        id: employee.id,
        data: this.employeeForm.value
      });

      // Set up a watcher to monitor loading state
      const loadingWatcher = setInterval(() => {
        if (!this.isLoading()) {
          this.isSaving = false;
          this.isEditing = false;
          clearInterval(loadingWatcher);

          // Show success message
          this.toastService.success(`Employee ${employee.first_name} ${employee.last_name} updated successfully`);
        }
      }, 100);
    }
  }

  async deleteEmployee(): Promise<void> {
    const employee = this.employee();
    if (!employee) return;

    // Use the modal service for confirmation
    const confirmed = await this.coreModalService.confirm({
      title: 'Delete Employee',
      message: `Are you sure you want to delete ${employee.first_name} ${employee.last_name}?`,
      confirmText: 'Delete',
      cancelText: 'Cancel'
    });

    if (confirmed) {
      this.employeesStore.deleteEmployee(employee.id);

      // Set up a watcher to monitor loading state
      const loadingWatcher = setInterval(() => {
        if (!this.isLoading()) {
          clearInterval(loadingWatcher);

          // Show success message
          this.toastService.success(`Employee ${employee.first_name} ${employee.last_name} deleted successfully`);

          // Navigate back to employees list after deletion
          this.router.navigate(['/app/employees']);
        }
      }, 100);
    }
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  setActiveManagementSection(section: ManagementSection): void {
    this.activeManagementSection = section;
  }

  setActiveManagementTab(tab: string): void {
    this.activeManagementTab = tab;
  }

  // Profile actions
  editInfo(): void {
    this.isEditing = true;
  }

  sendMessage(): void {
    // TODO: Implement messaging functionality
    this.toastService.info('Messaging feature coming soon');
  }

  // Section edit methods
  editBasicInfo(): void {
    // TODO: Open basic info edit modal
    this.toastService.info('Basic info editing coming soon');
  }

  editAbout(): void {
    // TODO: Open about edit modal
    this.toastService.info('About editing coming soon');
  }

  editBankInfo(): void {
    // TODO: Open bank info edit modal
    this.toastService.info('Bank info editing coming soon');
  }

  editFamilyInfo(): void {
    // TODO: Open family info edit modal
    this.toastService.info('Family info editing coming soon');
  }

  editEducation(): void {
    // TODO: Open education edit modal
    this.toastService.info('Education editing coming soon');
  }

  editExperience(): void {
    // TODO: Open experience edit modal
    this.toastService.info('Experience editing coming soon');
  }

  // Toggle methods for expandable sections
  toggleAbout(): void {
    this.aboutExpanded = !this.aboutExpanded;
  }

  toggleBankInfo(): void {
    this.bankInfoExpanded = !this.bankInfoExpanded;
  }

  toggleFamilyInfo(): void {
    this.familyInfoExpanded = !this.familyInfoExpanded;
  }

  toggleEducation(): void {
    this.educationExpanded = !this.educationExpanded;
  }

  toggleExperience(): void {
    this.experienceExpanded = !this.experienceExpanded;
  }

  // Utility methods
  getExperienceText(): string {
    const employee = this.employee();
    if (!employee?.hire_date) {
      return '10+ years of experience and 350+ projects completed worldwide with satisfied customers';
    }

    const hireDate = new Date(employee.hire_date);
    const now = new Date();
    const years = Math.floor((now.getTime() - hireDate.getTime()) / (1000 * 60 * 60 * 24 * 365));

    return `${years} years of experience and 350+ projects completed worldwide with satisfied customers`;
  }

  formatDate(dateString: string | undefined): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    };

    return date.toLocaleDateString('en-US', options);
  }

  getFullAddress(): string {
    const employee = this.employee();
    if (!employee) return '';

    const parts = [
      employee.address,
      employee.city,
      employee.state,
      employee.zip
    ].filter(part => part && part.trim() !== '');

    return parts.join(', ');
  }

  getEmployeeDescription(): string {
    const employee = this.employee();
    if (!employee) {
      return 'As an award winning designer, I deliver exceptional quality work and bring value to your brand! With 10 years of experience and 350+ projects completed worldwide with satisfied customers, I developed the 360° brand approach, which helped me to create numerous brands that are relevant, meaningful and loved.';
    }

    // You can customize this based on employee data
    const position = employee.position || 'professional';
    const department = employee.department || 'team';

    return `As an experienced ${position} in ${department}, I deliver exceptional quality work and bring value to your organization. With extensive experience and numerous projects completed, I am committed to excellence and continuous improvement.`;
  }

  // Load management data from API
  loadManagementData(employeeId: number): void {
    // Load employee salaries
    this.managementLoading.salaries = true;
    this.employeeManagementService.getEmployeeSalaries(employeeId).subscribe({
      next: (response: any) => {
        this.employeeSalaries = response.results;
        this.managementLoading.salaries = false;
      },
      error: (error: any) => {
        console.error('Error loading employee salaries:', error);
        this.managementLoading.salaries = false;
      }
    });

    // Load employee assets
    this.managementLoading.assets = true;
    this.employeeManagementService.getEmployeeAssets(employeeId).subscribe({
      next: (response: any) => {
        this.employeeAssets = response.results;
        this.managementLoading.assets = false;
      },
      error: (error: any) => {
        console.error('Error loading employee assets:', error);
        this.managementLoading.assets = false;
      }
    });

    // Load employee leaves
    this.managementLoading.leaves = true;
    this.employeeManagementService.getEmployeeLeaves(employeeId).subscribe({
      next: (response: any) => {
        this.employeeLeaves = response.results;
        this.managementLoading.leaves = false;
      },
      error: (error: any) => {
        console.error('Error loading employee leaves:', error);
        this.managementLoading.leaves = false;
      }
    });

    // Load employee attendance
    this.managementLoading.attendance = true;
    this.employeeManagementService.getEmployeeAttendance(employeeId).subscribe({
      next: (response: any) => {
        this.employeeAttendance = response.results;
        this.managementLoading.attendance = false;
      },
      error: (error: any) => {
        console.error('Error loading employee attendance:', error);
        this.managementLoading.attendance = false;
      }
    });

    // Load employee trainings
    this.managementLoading.trainings = true;
    this.employeeManagementService.getEmployeeTrainings(employeeId).subscribe({
      next: (response: any) => {
        this.employeeTrainings = response.results;
        this.managementLoading.trainings = false;
      },
      error: (error: any) => {
        console.error('Error loading employee trainings:', error);
        this.managementLoading.trainings = false;
      }
    });

    // Load employee performance
    this.managementLoading.performance = true;
    this.employeeManagementService.getEmployeePerformances(employeeId).subscribe({
      next: (response: any) => {
        this.employeePerformance = response.results;
        this.managementLoading.performance = false;
      },
      error: (error: any) => {
        console.error('Error loading employee performance:', error);
        this.managementLoading.performance = false;
      }
    });

    // Load employee OKRs
    this.managementLoading.okrs = true;
    this.employeeManagementService.getEmployeeOKRs(employeeId).subscribe({
      next: (response: any) => {
        this.employeeOkrs = response.results;
        this.managementLoading.okrs = false;
      },
      error: (error: any) => {
        console.error('Error loading employee OKRs:', error);
        this.managementLoading.okrs = false;
      }
    });

    // Load employee hikes
    this.managementLoading.hikes = true;
    this.employeeManagementService.getEmployeeHikes(employeeId).subscribe({
      next: (response: any) => {
        this.employeeHikes = response.results;
        this.managementLoading.hikes = false;
      },
      error: (error: any) => {
        console.error('Error loading employee hikes:', error);
        this.managementLoading.hikes = false;
      }
    });
  }

  // Management section actions
  addSalary(): void {
    const employee = this.employee();
    if (!employee) return;

    // TODO: Open add salary modal
    this.toastService.info('Add salary functionality coming soon');
  }

  editSalary(salary: any): void {
    // TODO: Open edit salary modal
    this.toastService.info('Edit salary functionality coming soon');
  }

  deleteSalary(salaryId: number): void {
    // TODO: Implement delete salary with confirmation
    this.toastService.info('Delete salary functionality coming soon');
  }

  addAsset(): void {
    const employee = this.employee();
    if (!employee) return;

    // TODO: Open add asset modal
    this.toastService.info('Add asset functionality coming soon');
  }

  editAsset(asset: any): void {
    // TODO: Open edit asset modal
    this.toastService.info('Edit asset functionality coming soon');
  }

  returnAsset(assetId: number): void {
    // TODO: Implement return asset functionality
    this.toastService.info('Return asset functionality coming soon');
  }

  addLeave(): void {
    const employee = this.employee();
    if (!employee) return;

    // TODO: Open add leave modal
    this.toastService.info('Add leave functionality coming soon');
  }

  editLeave(leave: any): void {
    // TODO: Open edit leave modal
    this.toastService.info('Edit leave functionality coming soon');
  }

  approveLeave(leaveId: number): void {
    // TODO: Implement approve leave functionality
    this.toastService.info('Approve leave functionality coming soon');
  }

  rejectLeave(leaveId: number): void {
    // TODO: Implement reject leave functionality
    this.toastService.info('Reject leave functionality coming soon');
  }

  // Project management methods
  addProject(): void {
    const employee = this.employee();
    if (!employee) return;

    // TODO: Open add project modal
    this.toastService.info('Add project functionality coming soon');
  }

  editProject(projectId: number): void {
    // TODO: Open edit project modal
    this.toastService.info('Edit project functionality coming soon');
  }

  deleteProject(projectId: number): void {
    // TODO: Implement delete project with confirmation
    this.toastService.info('Delete project functionality coming soon');
  }

  goBack(): void {
    this.router.navigate(['/app/employees']);
  }

  ngOnDestroy(): void {
    // Clean up watchers when component is destroyed
    this.onDestroy();
  }

  getStatusClass(status: string | undefined): string {
    if (!status) return 'status-inactive';

    switch (status.toLowerCase()) {
      case 'active':
        return 'status-active';
      case 'on_leave':
        return 'status-on-leave';
      case 'inactive':
        return 'status-inactive';
      default:
        return 'status-inactive';
    }
  }

  getStatusLabel(status: string | undefined): string {
    if (!status) return 'Inactive';

    switch (status.toLowerCase()) {
      case 'active':
        return 'Active';
      case 'on_leave':
        return 'On Leave';
      case 'inactive':
        return 'Inactive';
      default:
        return status;
    }
  }
}
