import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuItemComponent } from '../../atoms/menu-item/menu-item.component';

export interface MenuItem {
  icon?: string;
  label: string;
  route: string;
  active?: boolean;
  badge?: string;
  badgeType?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  hasSubmenu?: boolean;
}

@Component({
  selector: 'app-menu-group',
  standalone: true,
  imports: [CommonModule, MenuItemComponent],
  templateUrl: './menu-group.component.html',
  styleUrl: './menu-group.component.scss'
})
export class MenuGroupComponent {
  @Input() title: string = '';
  @Input() items: MenuItem[] = [];
  @Input() expanded: boolean = true;
  @Input() collapsed: boolean = false;

  toggleExpand() {
    this.expanded = !this.expanded;
  }
}
