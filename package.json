{"name": "shell", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr": "node dist/shell/server/server.mjs"}, "private": true, "dependencies": {"@angular-devkit/core": "19.2.10", "@angular/animations": "19.2.11", "@angular/common": "19.2.11", "@angular/compiler": "19.2.11", "@angular/core": "19.2.11", "@angular/forms": "19.2.11", "@angular/platform-browser": "19.2.11", "@angular/platform-browser-dynamic": "19.2.11", "@angular/platform-server": "19.2.11", "@angular/router": "19.2.11", "@angular/ssr": "19.2.12", "@ngrx/effects": "19.2.0", "@ngrx/signals": "19.2.0", "@ngrx/store": "19.2.0", "cors": "2.8.5", "express": "4.18.2", "rxjs": "7.8.1", "subsink": "^1.0.2", "tslib": "2.6.2", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "19.2.10", "@angular/build": "19.2.11", "@angular/cli": "19.2.10", "@angular/compiler-cli": "19.2.11", "@types/express": "4.17.17", "@types/jasmine": "5.1.4", "@types/node": "18.18.0", "jasmine-core": "5.1.1", "karma": "6.4.2", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.1.0", "typescript": "5.5.4"}}