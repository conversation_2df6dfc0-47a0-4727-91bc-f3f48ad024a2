import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * Service to manage dropdown states globally
 * Ensures only one dropdown is open at a time
 */
@Injectable({
  providedIn: 'root'
})
export class DropdownService {
  private openDropdownSubject = new BehaviorSubject<string | null>(null);
  public openDropdown$ = this.openDropdownSubject.asObservable();

  /**
   * Register a dropdown and get its open state
   * @param dropdownId Unique identifier for the dropdown
   * @returns Observable indicating if this dropdown is open
   */
  isOpen(dropdownId: string): Observable<boolean> {
    return new Observable<boolean>(observer => {
      this.openDropdown$.subscribe(openId => {
        observer.next(openId === dropdownId);
      });
    });
  }

  /**
   * Open a dropdown (closes all others)
   * @param dropdownId Unique identifier for the dropdown to open
   */
  open(dropdownId: string): void {
    this.openDropdownSubject.next(dropdownId);
  }

  /**
   * Close a specific dropdown
   * @param dropdownId Unique identifier for the dropdown to close
   */
  close(dropdownId: string): void {
    const currentOpen = this.openDropdownSubject.getValue();
    if (currentOpen === dropdownId) {
      this.openDropdownSubject.next(null);
    }
  }

  /**
   * Close all dropdowns
   */
  closeAll(): void {
    this.openDropdownSubject.next(null);
  }

  /**
   * Get the currently open dropdown ID
   * @returns The ID of the currently open dropdown or null
   */
  getCurrentOpen(): string | null {
    return this.openDropdownSubject.getValue();
  }
}
