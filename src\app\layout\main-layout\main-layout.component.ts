import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SideMenuComponent } from '../../shared/components/organisms/side-menu/side-menu.component';
import { HeaderComponent } from '../../shared/components/organisms/header/header.component';
import { AiChatWidgetComponent } from '../../shared/components/organisms/ai-chat-widget/ai-chat-widget.component';
import { ThemeService } from '../../core/services/theme.service';
import { LayoutService } from '../../core/services/layout.service';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [CommonModule, RouterModule, SideMenuComponent, HeaderComponent, AiChatWidgetComponent],
  templateUrl: './main-layout.component.html',
  styleUrl: './main-layout.component.scss'
})
export class MainLayoutComponent {
  private readonly themeService = inject(ThemeService);
  private readonly layoutService = inject(LayoutService);

  // Logo URL
  logoUrl: string = 'assets/images/logo.svg';

  // Side menu collapse state
  menuCollapsed: boolean = false;

  // Expose theme and layout services
  readonly currentTheme = this.themeService.currentTheme;
  readonly themeColors = this.themeService.themeColors;
  readonly layoutConfig = this.layoutService.layoutConfig;
  readonly headerPosition = this.layoutService.headerPosition;
  readonly layoutClasses = this.layoutService.layoutClasses;

  // Toggle side menu collapse state
  toggleMenuCollapse() {
    this.menuCollapsed = !this.menuCollapsed;
  }

  // Check if dark theme is active
  isDarkTheme(): boolean {
    return this.themeService.isDarkTheme();
  }

  // Check if header is fixed
  isHeaderFixed(): boolean {
    return this.layoutService.isHeaderFixed();
  }

  // Check if header is static
  isHeaderStatic(): boolean {
    return this.layoutService.isHeaderStatic();
  }

  // Check if header is sticky
  isHeaderSticky(): boolean {
    return this.layoutService.isHeaderSticky();
  }
}
