import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SideMenuComponent } from '../../shared/components/organisms/side-menu/side-menu.component';
import { HeaderComponent } from '../../shared/components/organisms/header/header.component';
import { AiChatWidgetComponent } from '../../shared/components/organisms/ai-chat-widget/ai-chat-widget.component';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [CommonModule, RouterModule, SideMenuComponent, HeaderComponent, AiChatWidgetComponent],
  templateUrl: './main-layout.component.html',
  styleUrl: './main-layout.component.scss'
})
export class MainLayoutComponent {
  // Logo URL
  logoUrl: string = 'assets/images/logo.svg';

  // Side menu collapse state
  menuCollapsed: boolean = false;

  // Toggle side menu collapse state
  toggleMenuCollapse() {
    this.menuCollapsed = !this.menuCollapsed;
  }
}
