import { Component, OnInit, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ToastComponent } from './shared/components/molecules/toast/toast.component';
import { ConfirmationDialogComponent } from './shared/components/atoms/confirmation-dialog/confirmation-dialog.component';
import { ModalService } from './core/services/modal.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, RouterOutlet, ToastComponent, ConfirmationDialogComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'shell';

  // Confirmation dialog state
  confirmationDialogOpen = false;
  confirmationDialogTitle = '';
  confirmationDialogMessage = '';
  confirmationDialogConfirmText = '';
  confirmationDialogCancelText = '';

  private modalService = inject(ModalService);

  ngOnInit(): void {
    // Subscribe to confirmation dialog state
    this.modalService.confirmationDialog$.subscribe(state => {
      this.confirmationDialogOpen = state.isOpen;
      this.confirmationDialogTitle = state.config.title || 'Confirm Action';
      this.confirmationDialogMessage = state.config.message;
      this.confirmationDialogConfirmText = state.config.confirmText || 'Confirm';
      this.confirmationDialogCancelText = state.config.cancelText || 'Cancel';
    });
  }

  onConfirmDialogConfirm(): void {
    this.modalService.confirmDialog();
  }

  onConfirmDialogCancel(): void {
    this.modalService.cancelDialog();
  }
}
