import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ThemeService } from '../../../../core/services/theme.service';
import { ThemeMode } from '../../../../core/models/theme.interface';

@Component({
  selector: 'app-theme-toggle',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './theme-toggle.component.html',
  styleUrls: ['./theme-toggle.component.scss']
})
export class ThemeToggleComponent {
  private readonly themeService = inject(ThemeService);

  // Expose theme service signals
  readonly themeConfig = this.themeService.themeConfig;
  readonly currentTheme = this.themeService.currentTheme;
  readonly isDarkTheme = this.themeService.isDarkTheme();

  /**
   * Toggle between light and dark themes
   */
  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  /**
   * Set specific theme mode
   */
  setThemeMode(mode: ThemeMode): void {
    this.themeService.setThemeMode(mode);
  }

  /**
   * Get theme mode display name
   */
  getThemeModeDisplayName(mode: ThemeMode): string {
    return this.themeService.getThemeModeDisplayName(mode);
  }

  /**
   * Get available theme modes
   */
  getAvailableThemeModes(): { value: ThemeMode; label: string; icon: string }[] {
    return [
      {
        value: 'light',
        label: 'Light',
        icon: 'fas fa-sun'
      },
      {
        value: 'dark',
        label: 'Dark',
        icon: 'fas fa-moon'
      },
      {
        value: 'auto',
        label: 'System',
        icon: 'fas fa-desktop'
      }
    ];
  }

  /**
   * Get current theme icon
   */
  getCurrentThemeIcon(): string {
    const currentMode = this.themeConfig().mode;
    const modes = this.getAvailableThemeModes();
    const mode = modes.find(m => m.value === currentMode);
    return mode?.icon || 'fas fa-sun';
  }

  /**
   * Get toggle button icon (for simple toggle)
   */
  getToggleIcon(): string {
    return this.currentTheme() === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
  }

  /**
   * Get toggle button tooltip
   */
  getToggleTooltip(): string {
    const currentMode = this.themeConfig().mode;
    if (currentMode === 'auto') {
      const systemTheme = this.currentTheme();
      return `Switch to ${systemTheme === 'dark' ? 'light' : 'dark'} theme`;
    }
    return `Switch to ${this.currentTheme() === 'dark' ? 'light' : 'dark'} theme`;
  }
}
