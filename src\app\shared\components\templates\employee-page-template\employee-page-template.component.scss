@import '../../../../../styles/variables/colors';

.employee-page {
  min-height: 100vh;
  background: $body-bg;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  &__header {
    background: $white;
    border-bottom: 1px solid $gray-200;
    padding: 1.5rem 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  &__header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  &__back {
    flex-shrink: 0;
  }

  &__title-section {
    flex: 1;
    min-width: 0;
  }

  &__title {
    font-size: 1.875rem;
    font-weight: 700;
    color: $gray-900;
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
  }

  &__subtitle {
    font-size: 1rem;
    color: $gray-600;
    margin: 0;
    line-height: 1.4;
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
  }

  &__search {
    max-width: 500px;
  }

  &__stats {
    padding: 1.5rem 2rem;
  }

  &__error {
    margin: 1rem 2rem;
    padding: 1rem;
    background: rgba($danger, 0.05);
    border: 1px solid rgba($danger, 0.2);
    border-radius: 12px;
    color: $danger;
  }

  &__error-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
  }

  &__content {
    padding: 1.5rem 2rem;
    flex: 1;
  }

  &__form-container {
    max-width: 900px;
    margin: 0 auto;
  }

  &__detail-container {
    max-width: 800px;
    margin: 0 auto;
  }

  &__details-manager {
    max-width: 1200px;
    margin: 0 auto;
  }

  &__profile-card {
    background: $white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }

  &__profile-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    background: linear-gradient(135deg, rgba($primary, 0.05) 0%, rgba($primary, 0.02) 100%);
    border-bottom: 1px solid $gray-200;
  }

  &__profile-avatar {
    flex-shrink: 0;
  }

  &__avatar-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid $primary;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &__profile-info {
    flex: 1;
    min-width: 0;
  }

  &__profile-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: $gray-900;
    margin: 0 0 0.25rem 0;
  }

  &__profile-title {
    font-size: 1rem;
    font-weight: 500;
    color: $primary;
    margin: 0 0 0.25rem 0;
  }

  &__profile-department {
    font-size: 0.875rem;
    color: $gray-600;
    margin: 0;
  }

  &__profile-actions {
    flex-shrink: 0;
    display: flex;
    gap: 0.75rem;
  }

  &__profile-details {
    padding: 2rem;
  }

  &__detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  &__detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    label {
      font-size: 0.75rem;
      font-weight: 500;
      color: $gray-500;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    span {
      font-size: 0.875rem;
      color: $gray-900;
      font-weight: 500;
    }
  }

  &__status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;

    &[data-status="active"] {
      background: rgba($success, 0.1);
      color: $success;
    }

    &[data-status="inactive"] {
      background: rgba($gray-500, 0.1);
      color: $gray-500;
    }

    &[data-status="on_leave"] {
      background: rgba($warning, 0.1);
      color: $warning;
    }

    &[data-status="terminated"] {
      background: rgba($danger, 0.1);
      color: $danger;
    }
  }

  &__loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba($white, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
  }

  &__loading-spinner {
    text-align: center;
    color: $gray-600;
  }

  &__spinner {
    width: 40px;
    height: 40px;
    border: 3px solid $gray-200;
    border-top: 3px solid $primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // Responsive design
  @media (max-width: 768px) {
    &__header {
      padding: 1rem;
    }

    &__header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    &__title-section {
      text-align: center;
    }

    &__title {
      font-size: 1.5rem;
    }

    &__actions {
      justify-content: center;
    }

    &__content {
      padding: 1rem;
    }

    &__profile-header {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    &__profile-actions {
      flex-direction: column;
      width: 100%;
    }

    &__detail-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    &__stats {
      padding: 1rem;
    }
  }

  @media (max-width: 480px) {
    &__header {
      padding: 0.75rem;
    }

    &__content {
      padding: 0.75rem;
    }

    &__profile-header {
      padding: 1.5rem;
    }

    &__profile-details {
      padding: 1.5rem;
    }

    &__title {
      font-size: 1.25rem;
    }

    &__subtitle {
      font-size: 0.875rem;
    }
  }
}
