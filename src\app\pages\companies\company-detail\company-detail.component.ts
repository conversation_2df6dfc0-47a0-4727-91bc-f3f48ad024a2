import { Component, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CompaniesStore, Company, EmployeesStore } from '../../../core/state';


@Component({
  selector: 'app-company-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule],
  templateUrl: './company-detail.component.html',
  styleUrls: ['./company-detail.component.scss']
})
export class CompanyDetailComponent implements OnInit, OnDestroy {
  private companiesStore = inject(CompaniesStore);
  private employeesStore = inject(EmployeesStore);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private fb = inject(FormBuilder);

  // Access state from the store using signals
  company = this.companiesStore.selectedCompany;
  isLoading = this.companiesStore.isLoading;
  error = this.companiesStore.error;

  // Employees for this company
  employees = this.employeesStore.employees;
  employeesCount = this.employeesStore.employeesCount;

  // Form state
  companyForm: FormGroup;
  isEditing = false;
  isSaving = false;
  formInitialized = false;
  lastError: string | null = null;
  onDestroy: () => void = () => {};

  // Tab state
  activeTab = 'details'; // 'details', 'employees', 'documents', 'settings'

  constructor() {
    this.companyForm = this.fb.group({
      name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      website: [''],
      address: [''],
      city: [''],
      state: [''],
      zip: [''],
      country: [''],
      industry: [''],
      description: ['']
    });
  }

  ngOnInit(): void {
    // Get company ID from route params
    this.route.paramMap.subscribe(params => {
      const id = Number(params.get('id'));
      if (id) {
        // Load company details
        this.companiesStore.loadCompany(id);

        // Load employees for this company
        this.employeesStore.loadEmployeesByCompany(id);
      }
    });

    // Set up an effect to initialize the form when the company data changes
    // Since we can't use subscribe with signals, we'll use a different approach

    // Create a watcher for company changes
    const companyWatcher = setInterval(() => {
      const currentCompany = this.company();
      if (currentCompany && !this.formInitialized) {
        this.initForm(currentCompany);
        this.formInitialized = true;
      }
    }, 100);

    // Create a watcher for error changes
    const errorWatcher = setInterval(() => {
      const currentError = this.error();
      if (currentError && currentError !== this.lastError) {
        console.error('Error loading company:', currentError);
        this.lastError = currentError;
        // You could show a toast or notification here
      }
    }, 100);

    // Clean up the watchers when the component is destroyed
    this.onDestroy = () => {
      clearInterval(companyWatcher);
      clearInterval(errorWatcher);
    };
  }

  private initForm(company: Company): void {
    this.companyForm.patchValue({
      name: company.name,
      email: company.email,
      phone: company.phone || '',
      website: company.website || '',
      address: company.address || '',
      city: company.city || '',
      state: company.state || '',
      zip: company.zip || '',
      country: company.country || '',
      industry: company.industry || '',
      description: company.description || ''
    });
  }

  toggleEdit(): void {
    this.isEditing = !this.isEditing;
    if (!this.isEditing && this.company()) {
      // Reset form when canceling edit
      this.initForm(this.company()!);
    }
  }

  saveCompany(): void {
    if (this.companyForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.companyForm.controls).forEach(key => {
        const control = this.companyForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSaving = true;
    const company = this.company();

    if (company) {
      // Update existing company
      this.companiesStore.updateCompany({
        id: company.id,
        data: this.companyForm.value
      });

      // Set up a watcher to monitor loading state
      const loadingWatcher = setInterval(() => {
        if (!this.isLoading()) {
          this.isSaving = false;
          this.isEditing = false;
          clearInterval(loadingWatcher);
        }
      }, 100);
    }
  }

  deleteCompany(): void {
    const company = this.company();
    if (company && confirm(`Are you sure you want to delete ${company.name}?`)) {
      this.companiesStore.deleteCompany(company.id);

      // Navigate back to companies list after deletion
      this.router.navigate(['/app/companies']);
    }
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  goBack(): void {
    this.router.navigate(['/app/companies']);
  }

  ngOnDestroy(): void {
    // Clean up watchers when component is destroyed
    this.onDestroy();
  }
}
