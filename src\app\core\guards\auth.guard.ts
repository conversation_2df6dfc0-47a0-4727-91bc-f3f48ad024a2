import { Injectable } from '@angular/core';
import {
  CanActivate,
  CanActivateChild,
  CanLoad,
  Route,
  UrlSegment,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { tap, map, take } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild, CanLoad {

  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAuth(state.url);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.canActivate(childRoute, state);
  }

  canLoad(
    route: Route,
    segments: UrlSegment[]
  ): Observable<boolean> {
    const url = segments.map(segment => `/${segment.path}`).join('');
    return this.checkAuth(url);
  }

  private checkAuth(url: string): Observable<boolean> {
    // If user is already authenticated, allow access
    if (this.authService.hasValidToken()) {
      return this.authService.checkTokenValidity().pipe(
        tap(isValid => {
          if (!isValid) {
            this.router.navigate(['/login'], { queryParams: { returnUrl: url } });
          }
        })
      );
    }

    // Redirect to login page with return URL
    this.router.navigate(['/login'], { queryParams: { returnUrl: url } });
    return of(false);
  }
}
