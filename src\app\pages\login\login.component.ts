import { Component, OnD<PERSON>roy, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AuthStore } from '../../core/state';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit, OnDestroy {
  username: string = '';
  password: string = '';
  showPassword: boolean = false;
  rememberMe: boolean = false;
  registeredMessage: string | null = null;

  private authStore = inject(AuthStore);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private queryParamsSub?: Subscription;

  // Access state from the store
  isLoading = this.authStore.isLoading;
  error = this.authStore.error;

  ngOnInit(): void {
    // Check if user just registered
    this.queryParamsSub = this.route.queryParams.subscribe(params => {
      if (params['registered'] === 'true') {
        this.registeredMessage = 'Registration successful! Please log in with your credentials.';
      }
    });
  }

  ngOnDestroy(): void {
    this.queryParamsSub?.unsubscribe();
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  login(): void {
    if (!this.username || !this.password) {
      return;
    }

    // Use the login method from the auth store
    this.authStore.login({ username: this.username, password: this.password });
  }

  loginWithMicrosoft(): void {
    // Implement Microsoft Teams authentication
    console.log('Logging in with Microsoft Teams');
    // This would typically call an OAuth service
  }

  loginWithGoogle(): void {
    // Implement Google authentication
    console.log('Logging in with Google');
    // This would typically call an OAuth service
  }

  loginWithApple(): void {
    // Implement Apple authentication
    console.log('Logging in with Apple');
    // This would typically call an OAuth service
  }

  navigateToRegister(event: Event): void {
    event.preventDefault();
    this.router.navigate(['/register']);
  }

  clearError(): void {
    this.authStore.clearError();
  }
}
