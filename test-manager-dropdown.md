# Manager Dropdown Implementation Test

## Changes Made

### 1. Updated DepartmentFilters Interface
- Added `manager: number[]` field for manager IDs
- Kept `manager_name: string[]` for backward compatibility

### 2. Updated Department Store
- Added `manager: []` to initial state filters
- Modified `loadDepartments` to use manager IDs from filters
- Added new `setManagerIdFilter(manager: number[])` method
- Updated `clearAllFilters` to include manager field

### 3. Updated Department Table Component
- Changed form control from `manager_name` to `manager`
- Modified manager options to use employee IDs as values and names as labels
- Updated filter subscription to convert string IDs to numbers
- Updated form initialization and reset methods

### 4. Updated HTML Template
- Changed `formControlName` from `manager_name` to `manager`

## Expected Behavior

1. **Manager Dropdown Options**: Should display employee full names as labels but use employee IDs as values
2. **API Requests**: When a manager is selected, the API should receive `manager=<employee_id>` instead of `manager_name=<employee_name>`
3. **Form State**: The form should store and manage manager IDs internally
4. **Backward Compatibility**: The system still supports `manager_name` parameter for existing functionality

## Testing Steps

1. Navigate to the departments page
2. Open the manager filter dropdown
3. Verify that employee names are displayed
4. Select a manager from the dropdown
5. Check the network request to verify it sends `manager=<id>` parameter
6. Verify that the filtering works correctly

## Key Code Changes

### Manager Options Creation
```typescript
this.managerOptions = employees
  .filter(emp => emp.full_name && emp.id)
  .map(emp => ({
    value: emp.id.toString(), // Use employee ID as value
    label: `${emp.full_name}`.trim() // Display full name as label
  }));
```

### Filter Subscription
```typescript
this.filterForm.get('manager')?.valueChanges
  .pipe(takeUntil(this.destroy$))
  .subscribe(manager => {
    // Convert string IDs to numbers for the API
    const managerIds = (manager || []).map((id: string) => parseInt(id, 10)).filter((id: number) => !isNaN(id));
    this.departmentsStore.setManagerIdFilter(managerIds);
  });
```

### API Parameter Building
```typescript
manager: params.manager ?? (currentFilters.manager.length > 0 ? currentFilters.manager[0] : undefined)
```
