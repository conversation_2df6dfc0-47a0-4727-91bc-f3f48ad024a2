<div class="profile-page">
  <div class="profile-header">
    <div class="profile-cover"></div>
    <div class="profile-info">
      <div class="profile-avatar">
        <img src="assets/images/user-avatar.jpg" alt="User Avatar">
        @if (isAdmin()) {
          <span class="profile-badge">Admin</span>
        }
      </div>
      <div class="profile-details">
        <h1 class="profile-name">{{ user()?.first_name }} {{ user()?.last_name }}</h1>
        <p class="profile-email">{{ user()?.email }}</p>
      </div>
      <div class="profile-actions">
        <button class="btn-edit" (click)="toggleEdit()" *ngIf="!isEditing">
          <i class="fa fa-edit"></i> Edit Profile
        </button>
        <button class="btn-cancel" (click)="toggleEdit()" *ngIf="isEditing">
          <i class="fa fa-times"></i> Cancel
        </button>
        <button class="btn-save" (click)="saveProfile()" *ngIf="isEditing" [disabled]="isSaving">
          <i class="fa" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
          {{ isSaving ? 'Saving...' : 'Save Changes' }}
        </button>
      </div>
    </div>
  </div>
  
  <div class="profile-tabs">
    <ul class="tabs-list">
      <li class="tab-item" [class.active]="activeTab === 'profile'">
        <button class="tab-link" (click)="setActiveTab('profile')">
          <i class="fa fa-user"></i> Profile
        </button>
      </li>
      <li class="tab-item" [class.active]="activeTab === 'security'">
        <button class="tab-link" (click)="setActiveTab('security')">
          <i class="fa fa-lock"></i> Security
        </button>
      </li>
      <li class="tab-item" [class.active]="activeTab === 'notifications'">
        <button class="tab-link" (click)="setActiveTab('notifications')">
          <i class="fa fa-bell"></i> Notifications
        </button>
      </li>
      <li class="tab-item" [class.active]="activeTab === 'preferences'">
        <button class="tab-link" (click)="setActiveTab('preferences')">
          <i class="fa fa-cog"></i> Preferences
        </button>
      </li>
    </ul>
  </div>
  
  <div class="profile-content">
    <!-- Profile Tab -->
    <div class="tab-content" *ngIf="activeTab === 'profile'">
      <form [formGroup]="profileForm" class="profile-form">
        <div class="form-row">
          <div class="form-group">
            <label for="firstName">First Name</label>
            <input 
              type="text" 
              id="firstName" 
              formControlName="firstName"
              [readonly]="!isEditing"
              [class.readonly]="!isEditing"
            >
            <div class="form-error" *ngIf="profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched">
              First name is required
            </div>
          </div>
          
          <div class="form-group">
            <label for="lastName">Last Name</label>
            <input 
              type="text" 
              id="lastName" 
              formControlName="lastName"
              [readonly]="!isEditing"
              [class.readonly]="!isEditing"
            >
            <div class="form-error" *ngIf="profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched">
              Last name is required
            </div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="email">Email</label>
            <input 
              type="email" 
              id="email" 
              formControlName="email"
              [readonly]="!isEditing"
              [class.readonly]="!isEditing"
            >
            <div class="form-error" *ngIf="profileForm.get('email')?.invalid && profileForm.get('email')?.touched">
              Please enter a valid email address
            </div>
          </div>
          
          <div class="form-group">
            <label for="phone">Phone</label>
            <input 
              type="tel" 
              id="phone" 
              formControlName="phone"
              [readonly]="!isEditing"
              [class.readonly]="!isEditing"
            >
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="position">Position</label>
            <input 
              type="text" 
              id="position" 
              formControlName="position"
              [readonly]="!isEditing"
              [class.readonly]="!isEditing"
            >
          </div>
          
          <div class="form-group">
            <label for="department">Department</label>
            <input 
              type="text" 
              id="department" 
              formControlName="department"
              [readonly]="!isEditing"
              [class.readonly]="!isEditing"
            >
          </div>
        </div>
        
        <div class="form-group">
          <label for="bio">Bio</label>
          <textarea 
            id="bio" 
            formControlName="bio"
            rows="4"
            [readonly]="!isEditing"
            [class.readonly]="!isEditing"
          ></textarea>
        </div>
      </form>
    </div>
    
    <!-- Security Tab -->
    <div class="tab-content" *ngIf="activeTab === 'security'">
      <div class="tab-placeholder">
        <i class="fa fa-lock placeholder-icon"></i>
        <h3>Security Settings</h3>
        <p>Change your password and manage security settings</p>
      </div>
    </div>
    
    <!-- Notifications Tab -->
    <div class="tab-content" *ngIf="activeTab === 'notifications'">
      <div class="tab-placeholder">
        <i class="fa fa-bell placeholder-icon"></i>
        <h3>Notification Preferences</h3>
        <p>Manage your notification settings</p>
      </div>
    </div>
    
    <!-- Preferences Tab -->
    <div class="tab-content" *ngIf="activeTab === 'preferences'">
      <div class="tab-placeholder">
        <i class="fa fa-cog placeholder-icon"></i>
        <h3>User Preferences</h3>
        <p>Customize your application experience</p>
      </div>
    </div>
  </div>
</div>
