<div class="multi-select-dropdown" [ngClass]="{'is-open': isOpen, 'is-disabled': disabled, 'is-invalid': invalid}">
  <button 
    type="button"
    class="multi-select-dropdown__toggle" 
    [disabled]="disabled"
    (click)="toggleDropdown($event)">
    <span class="multi-select-dropdown__selected-text">{{ selectedLabel }}</span>
    <app-icon 
      name="fa fa-chevron-down" 
      size="sm" 
      class="multi-select-dropdown__toggle-icon">
    </app-icon>
  </button>
  
  <div class="multi-select-dropdown__menu" *ngIf="isOpen">
    <div class="multi-select-dropdown__actions">
      <button 
        type="button" 
        class="multi-select-dropdown__action-btn" 
        (click)="selectAll($event)">
        Select All
      </button>
      <button 
        type="button" 
        class="multi-select-dropdown__action-btn" 
        (click)="clearAll($event)">
        Clear All
      </button>
    </div>
    
    <div class="multi-select-dropdown__search" *ngIf="showSearch">
      <input 
        type="text" 
        class="multi-select-dropdown__search-input" 
        [placeholder]="searchPlaceholder"
        [formControl]="searchControl"
        (click)="$event.stopPropagation()">
      <app-icon 
        name="fa fa-search" 
        size="sm" 
        class="multi-select-dropdown__search-icon">
      </app-icon>
    </div>
    
    <div class="multi-select-dropdown__options">
      <div 
        *ngFor="let option of filteredOptions" 
        class="multi-select-dropdown__option" 
        [ngClass]="{'is-active': isSelected(option.value)}"
        (click)="toggleOption(option, $event)">
        <div class="multi-select-dropdown__checkbox">
          <input 
            type="checkbox" 
            [id]="'option-' + option.value" 
            [checked]="isSelected(option.value)" 
            (click)="$event.stopPropagation()">
          <label [for]="'option-' + option.value"></label>
        </div>
        <app-icon 
          *ngIf="option.icon" 
          [name]="option.icon" 
          size="sm" 
          class="multi-select-dropdown__option-icon">
        </app-icon>
        <span class="multi-select-dropdown__option-label">{{ option.label }}</span>
      </div>
      
      <div *ngIf="filteredOptions.length === 0" class="multi-select-dropdown__no-results">
        No results found
      </div>
    </div>
  </div>
</div>
