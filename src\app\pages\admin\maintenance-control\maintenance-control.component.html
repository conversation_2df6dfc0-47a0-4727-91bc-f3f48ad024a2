<div class="maintenance-control">
  <div class="maintenance-control__header">
    <h1 class="maintenance-control__title">Maintenance Mode Control</h1>
    <p class="maintenance-control__description">
      Enable or disable maintenance mode for the entire application.
    </p>
  </div>

  <div class="maintenance-control__status">
    <div class="maintenance-control__status-indicator" [class.is-active]="isMaintenanceMode">
      <i class="fa" [class.fa-check-circle]="isMaintenanceMode" [class.fa-times-circle]="!isMaintenanceMode"></i>
      <span>Maintenance Mode is {{ isMaintenanceMode ? 'Active' : 'Inactive' }}</span>
    </div>
  </div>

  <div class="maintenance-control__actions">
    <button 
      class="maintenance-control__button" 
      [class.maintenance-control__button--enable]="!isMaintenanceMode"
      [class.maintenance-control__button--disable]="isMaintenanceMode"
      (click)="toggleMaintenanceMode()">
      {{ isMaintenanceMode ? 'Disable' : 'Enable' }} Maintenance Mode
    </button>
  </div>

  <div class="maintenance-control__config" *ngIf="isMaintenanceMode">
    <h2 class="maintenance-control__subtitle">Maintenance Configuration</h2>
    
    <div class="maintenance-control__form-group">
      <label class="maintenance-control__label">Estimated Completion Time</label>
      <div class="maintenance-control__value">{{ estimatedCompletion }}</div>
    </div>
    
    <div class="maintenance-control__form-group">
      <label class="maintenance-control__label">Message</label>
      <textarea 
        class="maintenance-control__textarea"
        [(ngModel)]="maintenanceMessage"
        rows="3"></textarea>
    </div>
    
    <button 
      class="maintenance-control__button maintenance-control__button--update"
      (click)="updateMaintenanceConfig()">
      Update Configuration
    </button>
  </div>
</div>
