import { Injectable } from '@angular/core';

export interface JwtPayload {
  exp: number;
  iat: number;
  jti: string;
  token_type: string;
  user_id: number;
  [key: string]: any;
}

@Injectable({
  providedIn: 'root'
})
export class JwtService {
  
  /**
   * Decode a JWT token
   * @param token JWT token string
   * @returns Decoded token payload or null if invalid
   */
  decodeToken(token: string): JwtPayload | null {
    if (!token) {
      return null;
    }

    try {
      // Split the token into parts
      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      // Decode the payload (middle part)
      const payload = parts[1];
      const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error decoding JWT token:', error);
      return null;
    }
  }

  /**
   * Check if a token is expired
   * @param token JWT token string
   * @returns true if token is expired or invalid, false otherwise
   */
  isTokenExpired(token: string): boolean {
    const payload = this.decodeToken(token);
    if (!payload) {
      return true;
    }

    // Get expiration timestamp and current time
    const expirationTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();

    // Check if token is expired
    return currentTime >= expirationTime;
  }

  /**
   * Get time remaining until token expiration in seconds
   * @param token JWT token string
   * @returns Seconds until expiration, or 0 if token is expired or invalid
   */
  getTokenExpiryTime(token: string): number {
    const payload = this.decodeToken(token);
    if (!payload) {
      return 0;
    }

    const expirationTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    
    if (currentTime >= expirationTime) {
      return 0;
    }
    
    return Math.floor((expirationTime - currentTime) / 1000);
  }

  /**
   * Get user ID from token
   * @param token JWT token string
   * @returns User ID from token or null if invalid
   */
  getUserIdFromToken(token: string): number | null {
    const payload = this.decodeToken(token);
    return payload?.user_id || null;
  }
}
