@use 'sass:color';
@import '../../../../styles/variables/_colors';

.maintenance-control {
  padding: 2rem;
  background-color: $white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba($black, 0.05);

  &__header {
    margin-bottom: 2rem;
  }

  &__title {
    font-size: 1.75rem;
    font-weight: $font-weight-bold;
    color: $gray-900;
    margin-bottom: 0.5rem;
  }

  &__description {
    color: $gray-600;
    font-size: 1rem;
  }

  &__status {
    margin-bottom: 2rem;
  }

  &__status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    border-radius: 4px;
    background-color: $gray-100;
    color: $gray-700;
    font-weight: $font-weight-medium;

    i {
      margin-right: 0.5rem;
      font-size: 1.25rem;
    }

    &.is-active {
      background-color: rgba($warning, 0.1);
      color: $warning;
    }
  }

  &__actions {
    margin-bottom: 2rem;
  }

  &__button {
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: $font-weight-medium;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;

    &--enable {
      background-color: $warning;
      color: $white;

      &:hover {
        background-color: color.adjust($warning, $lightness: -10%);
      }
    }

    &--disable {
      background-color: $gray-700;
      color: $white;

      &:hover {
        background-color: color.adjust($gray-700, $lightness: -10%);
      }
    }

    &--update {
      background-color: $primary;
      color: $white;
      margin-top: 1rem;

      &:hover {
        background-color: color.adjust($primary, $lightness: -10%);
      }
    }
  }

  &__config {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid $gray-200;
  }

  &__subtitle {
    font-size: 1.25rem;
    font-weight: $font-weight-semibold;
    color: $gray-800;
    margin-bottom: 1.5rem;
  }

  &__form-group {
    margin-bottom: 1.5rem;
  }

  &__label {
    display: block;
    font-weight: $font-weight-medium;
    color: $gray-700;
    margin-bottom: 0.5rem;
  }

  &__value {
    padding: 0.75rem;
    background-color: $gray-100;
    border-radius: 4px;
    color: $gray-800;
    font-weight: $font-weight-medium;
  }

  &__textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid $gray-300;
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
    resize: vertical;
    transition: border-color 0.2s ease;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba($primary, 0.1);
    }
  }
}


