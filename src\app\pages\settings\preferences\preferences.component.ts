import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { ThemeService } from '../../../core/services/theme.service';
import { LayoutService } from '../../../core/services/layout.service';
import { ThemeMode, HeaderPosition } from '../../../core/models/theme.interface';

@Component({
  selector: 'app-preferences',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './preferences.component.html',
  styleUrls: ['./preferences.component.scss']
})
export class PreferencesComponent implements OnInit {
  private readonly fb = inject(FormBuilder);
  private readonly themeService = inject(ThemeService);
  private readonly layoutService = inject(LayoutService);

  preferencesForm!: FormGroup;

  // Expose services for template
  readonly currentTheme = this.themeService.currentTheme;
  readonly themeConfig = this.themeService.themeConfig;
  readonly layoutConfig = this.layoutService.layoutConfig;

  // Theme and layout options
  readonly themeOptions = [
    { value: 'light', label: 'Light', description: 'Clean and bright interface', icon: 'fas fa-sun' },
    { value: 'dark', label: 'Dark', description: 'Easy on the eyes in low light', icon: 'fas fa-moon' },
    { value: 'auto', label: 'System', description: 'Follows your system preference', icon: 'fas fa-desktop' }
  ];

  readonly headerPositionOptions = [
    { 
      value: 'fixed', 
      label: 'Fixed Header', 
      description: 'Header stays at top during scroll',
      icon: 'fas fa-thumbtack'
    },
    { 
      value: 'static', 
      label: 'Static Header', 
      description: 'Header scrolls with content',
      icon: 'fas fa-arrows-alt-v'
    },
    { 
      value: 'sticky', 
      label: 'Sticky Header', 
      description: 'Header becomes fixed after scrolling',
      icon: 'fas fa-sticky-note'
    }
  ];

  readonly layoutTypeOptions = [
    {
      value: 'vertical',
      label: 'Vertical Layout',
      description: 'Traditional sidebar layout',
      icon: 'fas fa-columns'
    },
    {
      value: 'horizontal',
      label: 'Horizontal Layout',
      description: 'Top navigation layout',
      icon: 'fas fa-bars'
    }
  ];

  readonly sidebarPositionOptions = [
    {
      value: 'left',
      label: 'Left Sidebar',
      description: 'Sidebar on the left side',
      icon: 'fas fa-align-left'
    },
    {
      value: 'right',
      label: 'Right Sidebar',
      description: 'Sidebar on the right side',
      icon: 'fas fa-align-right'
    }
  ];

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormSubscriptions();
  }

  /**
   * Initialize the preferences form
   */
  private initializeForm(): void {
    this.preferencesForm = this.fb.group({
      // Theme settings
      themeMode: [this.themeConfig().mode],
      
      // Layout settings
      headerPosition: [this.layoutConfig().headerPosition],
      layoutType: [this.layoutConfig().layoutType],
      sidebarPosition: [this.layoutConfig().sidebarPosition],
      sidebarCollapsed: [this.layoutConfig().sidebarCollapsed]
    });
  }

  /**
   * Setup form value change subscriptions
   */
  private setupFormSubscriptions(): void {
    // Theme mode changes
    this.preferencesForm.get('themeMode')?.valueChanges.subscribe((mode: ThemeMode) => {
      this.themeService.setThemeMode(mode);
    });

    // Header position changes
    this.preferencesForm.get('headerPosition')?.valueChanges.subscribe((position: HeaderPosition) => {
      this.layoutService.setHeaderPosition(position);
    });

    // Layout type changes
    this.preferencesForm.get('layoutType')?.valueChanges.subscribe((type: 'vertical' | 'horizontal') => {
      this.layoutService.setLayoutType(type);
    });

    // Sidebar position changes
    this.preferencesForm.get('sidebarPosition')?.valueChanges.subscribe((position: 'left' | 'right') => {
      this.layoutService.setSidebarPosition(position);
    });

    // Sidebar collapsed changes
    this.preferencesForm.get('sidebarCollapsed')?.valueChanges.subscribe((collapsed: boolean) => {
      if (collapsed !== this.layoutConfig().sidebarCollapsed) {
        this.layoutService.toggleSidebar();
      }
    });
  }

  /**
   * Reset all preferences to default
   */
  resetToDefaults(): void {
    this.themeService.resetTheme();
    this.layoutService.resetLayout();
    this.initializeForm();
  }

  /**
   * Check if current theme mode is selected
   */
  isThemeModeSelected(mode: ThemeMode): boolean {
    return this.themeConfig().mode === mode;
  }

  /**
   * Check if current header position is selected
   */
  isHeaderPositionSelected(position: HeaderPosition): boolean {
    return this.layoutConfig().headerPosition === position;
  }

  /**
   * Check if current layout type is selected
   */
  isLayoutTypeSelected(type: 'vertical' | 'horizontal'): boolean {
    return this.layoutConfig().layoutType === type;
  }

  /**
   * Check if current sidebar position is selected
   */
  isSidebarPositionSelected(position: 'left' | 'right'): boolean {
    return this.layoutConfig().sidebarPosition === position;
  }

  /**
   * Get current theme display name
   */
  getCurrentThemeDisplayName(): string {
    return this.themeService.getThemeModeDisplayName(this.themeConfig().mode);
  }

  /**
   * Get current header position display name
   */
  getCurrentHeaderPositionDisplayName(): string {
    return this.layoutService.getHeaderPositionDisplayName(this.layoutConfig().headerPosition);
  }
}
