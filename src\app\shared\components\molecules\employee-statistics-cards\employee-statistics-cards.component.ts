import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface EmployeeStatistics {
  totalEmployees: number;
  activeEmployees: number;
  onLeaveEmployees: number;
  newJoiners?: number;
}

@Component({
  selector: 'app-employee-statistics-cards',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './employee-statistics-cards.component.html',
  styleUrls: ['./employee-statistics-cards.component.scss']
})
export class EmployeeStatisticsCardsComponent {
  @Input() statistics: EmployeeStatistics = {
    totalEmployees: 0,
    activeEmployees: 0,
    onLeaveEmployees: 0,
    newJoiners: 0
  };
  @Input() isLoading: boolean = false;
}
