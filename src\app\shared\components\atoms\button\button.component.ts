import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'app-button',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './button.component.html',
  styleUrl: './button.component.scss'
})
export class ButtonComponent {
  @Input() label: string = '';
  @Input() variant: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' = 'primary';
  @Input() size: 'sm' | 'md' | 'lg' | null = null;
  @Input() icon: string = '';
  @Input() iconRight: string = '';
  @Input() iconSize: 'sm' | 'md' | 'lg' = 'sm';
  @Input() block: boolean = false;
  @Input() iconOnly: boolean = false;
  @Input() disabled: boolean = false;

  @Output() onClick = new EventEmitter<MouseEvent>();
}
