import { Component, EventEmitter, Input, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthStore } from '../../../../core/state';
import { ThemeToggleComponent } from '../../atoms/theme-toggle/theme-toggle.component';
import { HeaderPositionToggleComponent } from '../../atoms/header-position-toggle/header-position-toggle.component';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ThemeToggleComponent,
    HeaderPositionToggleComponent
  ],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent {
  @Input() menuCollapsed: boolean = false;
  @Output() toggleMenuCollapse = new EventEmitter<void>();

  private authStore = inject(AuthStore);

  // Access state from the store using signals
  user = this.authStore.user;
  isAuthenticated = this.authStore.isAuthenticated;
  fullName = this.authStore.fullName;
  isAdmin = this.authStore.isAdmin;

  // User menu state
  userMenuOpen: boolean = false;

  toggleSideMenu() {
    this.toggleMenuCollapse.emit();
  }

  toggleUserMenu() {
    this.userMenuOpen = !this.userMenuOpen;
  }

  logout() {
    this.authStore.logout();
  }
}
