/**
 * Extended Employee Interfaces
 * 
 * This file contains additional interfaces for employee-related entities
 * based on the Swagger API documentation.
 */

// Employee Details Interface (for detailed employee information)
export interface EmployeeDetails {
  id: number;
  employee_id: number;
  
  // Personal Information
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  blood_group?: string;
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  nationality?: string;
  
  // Government Documents
  passport_number?: string;
  passport_expiry_date?: string;
  visa_status?: string;
  visa_expiry_date?: string;
  work_permit_number?: string;
  work_permit_expiry_date?: string;
  social_security_number?: string;
  tax_id?: string;
  
  // Banking Information
  bank_account_number?: string;
  bank_name?: string;
  bank_routing_number?: string;
  bank_branch?: string;
  
  // Address Information
  permanent_address?: string;
  permanent_city?: string;
  permanent_state?: string;
  permanent_zip?: string;
  permanent_country?: string;
  current_address?: string;
  current_city?: string;
  current_state?: string;
  current_zip?: string;
  current_country?: string;
  
  // Additional Information
  notes?: string;
  
  created_at: string;
  updated_at: string;
}

// Employee Documents Interface
export interface EmployeeDocument {
  id: number;
  employee_id: number;
  document_type: 'resume' | 'contract' | 'id_proof' | 'address_proof' | 'education' | 'certification' | 'other';
  document_name: string;
  document_url: string;
  file_size?: number;
  mime_type?: string;
  uploaded_by?: number;
  is_verified?: boolean;
  verification_date?: string;
  expiry_date?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Employee Skills Interface
export interface EmployeeSkill {
  id: number;
  employee_id: number;
  skill_name: string;
  skill_category?: 'technical' | 'soft' | 'language' | 'certification' | 'other';
  proficiency_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  years_of_experience?: number;
  certification_name?: string;
  certification_date?: string;
  certification_expiry?: string;
  is_verified?: boolean;
  created_at: string;
  updated_at: string;
}

// Employee Job History Interface
export interface EmployeeJobHistory {
  id: number;
  employee_id: number;
  company_name: string;
  job_title: string;
  department?: string;
  start_date: string;
  end_date?: string;
  is_current?: boolean;
  salary?: number;
  currency?: string;
  employment_type?: 'full_time' | 'part_time' | 'contract' | 'internship' | 'freelance';
  job_description?: string;
  achievements?: string;
  reason_for_leaving?: string;
  supervisor_name?: string;
  supervisor_contact?: string;
  created_at: string;
  updated_at: string;
}

// Designation Interface
export interface Designation {
  id: number;
  name: string;
  code?: string;
  description?: string;
  level?: number;
  department_id?: number;
  is_active?: boolean;
  created_at: string;
  updated_at: string;
}

// Employee Form Data Interface (for comprehensive forms)
export interface EmployeeFormData {
  // Basic Information
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other';
  
  // Work Information
  department_id?: number;
  designation_id?: number;
  status?: 'active' | 'inactive' | 'on_leave' | 'terminated';
  hire_date?: string;
  salary?: number;
  employment_type?: 'full_time' | 'part_time' | 'contract' | 'internship';
  
  // Contact Information
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  
  // Emergency Contact
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  
  // Personal Details
  blood_group?: string;
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  nationality?: string;
  
  // Documents
  profile_picture?: File | string;
  resume?: File;
  contract?: File;
  id_proof?: File;
  
  // Banking Information
  bank_account_number?: string;
  bank_name?: string;
  bank_routing_number?: string;
}

// Employee Search/Filter Interface
export interface EmployeeSearchFilters {
  search?: string;
  department_ids?: number[];
  designation_ids?: number[];
  status?: ('active' | 'inactive' | 'on_leave' | 'terminated')[];
  gender?: ('male' | 'female' | 'other')[];
  employment_type?: ('full_time' | 'part_time' | 'contract' | 'internship')[];
  hire_date_from?: string;
  hire_date_to?: string;
  salary_min?: number;
  salary_max?: number;
  skills?: string[];
  page?: number;
  page_size?: number;
  ordering?: string;
}

// Employee Statistics Interface
export interface EmployeeStatistics {
  total_employees: number;
  active_employees: number;
  inactive_employees: number;
  on_leave_employees: number;
  terminated_employees: number;
  new_hires_this_month: number;
  departures_this_month: number;
  average_tenure_months: number;
  department_distribution: {
    department_name: string;
    employee_count: number;
  }[];
  designation_distribution: {
    designation_name: string;
    employee_count: number;
  }[];
  gender_distribution: {
    gender: string;
    count: number;
  }[];
}

// API Response Interfaces
export interface EmployeeDetailsResponse {
  employee: Employee;
  details?: EmployeeDetails;
  documents?: EmployeeDocument[];
  skills?: EmployeeSkill[];
  job_history?: EmployeeJobHistory[];
}

export interface EmployeeListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Employee[];
}

// Employee Details Input Interface (for creating/updating details)
export interface EmployeeDetailsInput {
  employee_id: number;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  blood_group?: string;
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  nationality?: string;
  passport_number?: string;
  passport_expiry_date?: string;
  visa_status?: string;
  visa_expiry_date?: string;
  work_permit_number?: string;
  work_permit_expiry_date?: string;
  social_security_number?: string;
  tax_id?: string;
  bank_account_number?: string;
  bank_name?: string;
  bank_routing_number?: string;
  bank_branch?: string;
  permanent_address?: string;
  permanent_city?: string;
  permanent_state?: string;
  permanent_zip?: string;
  permanent_country?: string;
  current_address?: string;
  current_city?: string;
  current_state?: string;
  current_zip?: string;
  current_country?: string;
  notes?: string;
}

// Import Employee interface from the main state file
import { Employee } from '../state/employees/employees.state';
