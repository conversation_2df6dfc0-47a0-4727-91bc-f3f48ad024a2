@import '../../../../../styles/variables/_colors';

.okr-form {
  background: $white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .form-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, $primary, $primary-light);
    color: $white;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: $font-weight-semibold;
    }
  }

  .form-content {
    padding: 2rem;

    .form-section {
      margin-bottom: 2rem;
      padding-bottom: 1.5rem;
      border-bottom: 1px solid $border-light;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      h4 {
        margin: 0 0 1.5rem 0;
        font-size: 1.1rem;
        font-weight: $font-weight-semibold;
        color: $text-primary;
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        h4 {
          margin: 0;
        }
      }
    }

    .form-group {
      margin-bottom: 1.5rem;

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: $font-weight-medium;
        color: $text-primary;
        font-size: 0.9rem;

        .required {
          color: $error;
          margin-left: 0.25rem;
        }
      }

      .form-input,
      .form-select {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid $border-light;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: $white;

        &:focus {
          outline: none;
          border-color: $primary;
          box-shadow: 0 0 0 3px rgba($primary, 0.1);
        }

        &.error {
          border-color: $error;
          box-shadow: 0 0 0 3px rgba($error, 0.1);
        }

        &::placeholder {
          color: $text-secondary;
        }

        // Remove number input arrows
        &[type="number"] {
          -moz-appearance: textfield;

          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
          }
        }
      }

      .form-textarea {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid $border-light;
        border-radius: 8px;
        font-size: 1rem;
        font-family: inherit;
        resize: vertical;
        min-height: 80px;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: $primary;
          box-shadow: 0 0 0 3px rgba($primary, 0.1);
        }

        &.error {
          border-color: $error;
          box-shadow: 0 0 0 3px rgba($error, 0.1);
        }

        &::placeholder {
          color: $text-secondary;
        }
      }

      .progress-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .form-input {
          padding-right: 2.5rem;
        }

        .percentage-symbol {
          position: absolute;
          right: 1rem;
          color: $text-secondary;
          font-weight: $font-weight-medium;
          z-index: 1;
        }
      }

      .progress-bar {
        margin-top: 0.5rem;
        height: 8px;
        background-color: $background-light;
        border-radius: 4px;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, $primary, $primary-light);
          transition: width 0.3s ease;
        }
      }

      .character-count {
        text-align: right;
        margin-top: 0.25rem;
        font-size: 0.8rem;
        color: $text-secondary;
      }

      .error-message {
        margin-top: 0.5rem;
        color: $error;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;

        &::before {
          content: '⚠';
          font-size: 0.9rem;
        }
      }
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }

    .dynamic-list {
      .dynamic-item {
        margin-bottom: 1rem;
        padding: 1rem;
        background: $background-light;
        border-radius: 8px;
        border-left: 4px solid $primary;

        &:last-child {
          margin-bottom: 0;
        }

        .item-content {
          .input-with-action {
            position: relative;
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;

            .form-textarea {
              flex: 1;
            }

            .remove-button {
              flex-shrink: 0;
              width: 36px;
              height: 36px;
              background: $error;
              color: $white;
              border: none;
              border-radius: 6px;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.3s ease;

              .material-icons {
                font-size: 1.1rem;
              }

              &:hover {
                background: darken($error, 10%);
                transform: scale(1.05);
              }
            }
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 2rem;
      padding-top: 1.5rem;
      border-top: 1px solid $border-light;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .okr-form {
    .form-header {
      padding: 1rem 1.5rem;

      h3 {
        font-size: 1.1rem;
      }
    }

    .form-content {
      padding: 1.5rem;

      .form-section {
        .section-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 1rem;

          h4 {
            margin-bottom: 0;
          }
        }
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .dynamic-list {
        .dynamic-item {
          .item-content {
            .input-with-action {
              flex-direction: column;

              .remove-button {
                align-self: flex-end;
                width: auto;
                height: auto;
                padding: 0.5rem 1rem;
                border-radius: 4px;

                .material-icons {
                  margin-right: 0.25rem;
                }

                &::after {
                  content: 'Remove';
                  font-size: 0.8rem;
                }
              }
            }
          }
        }
      }

      .form-actions {
        flex-direction: column-reverse;
        gap: 0.75rem;

        app-button {
          width: 100%;
        }
      }
    }
  }
}

// Animation for dynamic items
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dynamic-item {
  animation: slideIn 0.3s ease-out;
}

// Focus states
.form-group:focus-within .form-label {
  color: $primary;
}

// Hover effects
.form-group .form-input:hover:not(:focus),
.form-group .form-select:hover:not(:focus),
.form-group .form-textarea:hover:not(:focus) {
  border-color: rgba($primary, 0.5);
}

// Progress bar animation
.progress-bar .progress-fill {
  animation: progressGrow 0.5s ease-out;
}

@keyframes progressGrow {
  from {
    width: 0;
  }
}

// Dynamic list item hover effect
.dynamic-item:hover {
  background: rgba($primary, 0.02);
  border-left-color: $primary-light;
}
