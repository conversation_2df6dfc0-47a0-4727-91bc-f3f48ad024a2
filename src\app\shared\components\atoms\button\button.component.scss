/* Font is imported in the add-employee-modal component */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn:focus, .btn:hover {
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-primary {
  color: #fff;
  background: linear-gradient(135deg, #ff7e45, #ff6b35);
  border-color: #ff6b35;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #ff8955, #ff7a45);
  border-color: #e85a2a;
}

.btn-primary:active {
  background: linear-gradient(135deg, #e85a2a, #ff6b35);
}

.btn-secondary {
  color: #495057;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-color: #dee2e6;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-color: #ced4da;
}

.btn-secondary:active {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 10px;
}

.btn-lg {
  padding: 0.875rem 1.75rem;
  font-size: 1rem;
  border-radius: 14px;
}

.btn-block {
  display: flex;
  width: 100%;
}

.btn-icon-only {
  padding: 0.625rem;
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.btn-sm.btn-icon-only {
  width: 32px;
  height: 32px;
  padding: 0.375rem;
}

.btn-lg.btn-icon-only {
  width: 48px;
  height: 48px;
  padding: 0.75rem;
}

app-icon + .btn-text {
  margin-left: 0.625rem;
}

.btn-text + app-icon {
  margin-left: 0.625rem;
}
