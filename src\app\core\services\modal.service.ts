import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ConfirmationDialogConfig {
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  private confirmationDialogSubject = new BehaviorSubject<{
    isOpen: boolean;
    config: ConfirmationDialogConfig;
  }>({
    isOpen: false,
    config: { message: '' }
  });

  confirmationDialog$ = this.confirmationDialogSubject.asObservable();

  constructor() {}

  /**
   * Open a confirmation dialog
   * @param config The dialog configuration
   * @returns Promise that resolves to true if confirmed, false if canceled
   */
  confirm(config: ConfirmationDialogConfig): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      // Set up the dialog
      this.confirmationDialogSubject.next({
        isOpen: true,
        config: {
          title: config.title || 'Confirm Action',
          message: config.message,
          confirmText: config.confirmText || 'Confirm',
          cancelText: config.cancelText || 'Cancel'
        }
      });

      // Set up the handlers
      const confirmHandler = () => {
        this.closeConfirmationDialog();
        resolve(true);
      };

      const cancelHandler = () => {
        this.closeConfirmationDialog();
        resolve(false);
      };

      // Store the handlers on the service instance
      (this as any).confirmHandler = confirmHandler;
      (this as any).cancelHandler = cancelHandler;
    });
  }

  /**
   * Confirm the dialog (called from the dialog component)
   */
  confirmDialog(): void {
    if ((this as any).confirmHandler) {
      (this as any).confirmHandler();
    }
  }

  /**
   * Cancel the dialog (called from the dialog component)
   */
  cancelDialog(): void {
    if ((this as any).cancelHandler) {
      (this as any).cancelHandler();
    }
  }

  /**
   * Close the confirmation dialog
   */
  private closeConfirmationDialog(): void {
    this.confirmationDialogSubject.next({
      isOpen: false,
      config: { message: '' }
    });

    // Clean up the handlers
    (this as any).confirmHandler = null;
    (this as any).cancelHandler = null;
  }
}
