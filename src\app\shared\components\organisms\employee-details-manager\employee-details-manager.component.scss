@import '../../../../../styles/variables/colors';

.employee-details-manager {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background: $body-bg;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;

  &__header {
    background: $white;
    border-radius: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    overflow: hidden;
  }

  &__header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid $gray-200;
  }

  &__title-section {
    flex: 1;
    min-width: 0;
  }

  &__title {
    font-size: 1.5rem;
    font-weight: 600;
    color: $gray-900;
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
  }

  &__subtitle {
    font-size: 0.875rem;
    color: $gray-600;
    margin: 0;
    line-height: 1.4;
  }

  &__header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
  }

  &__navigation {
    padding: 0;
  }

  &__nav-tabs {
    display: flex;
    flex-wrap: wrap;
  }

  &__nav-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: $gray-600;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 0;

    &:hover {
      background-color: $gray-50;
      color: $gray-800;
    }

    &--active {
      color: $primary;
      border-bottom-color: $primary;
      background-color: rgba($primary, 0.05);
    }
  }

  &__nav-icon {
    font-size: 1.125rem;
    line-height: 1;
  }

  &__nav-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__error {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba($danger, 0.05);
    border: 1px solid rgba($danger, 0.2);
    border-radius: 12px;
    color: $danger;
  }

  &__error-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
  }

  &__content {
    background: $white;
    border-radius: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }

  &__overview {
    padding: 2rem;
  }

  &__quick-actions {
    margin-bottom: 2rem;
    display: flex;
    justify-content: flex-end;
  }

  &__summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  &__card {
    background: $white;
    border: 1px solid $gray-200;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transform: translateY(-1px);
    }
  }

  &__card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: $gray-900;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid $gray-100;
  }

  &__card-icon {
    font-size: 1.25rem;
    line-height: 1;
  }

  &__card-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  &__info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    label {
      font-size: 0.75rem;
      font-weight: 500;
      color: $gray-500;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    span {
      font-size: 0.875rem;
      color: $gray-900;
      font-weight: 500;
    }
  }

  &__address-section {
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }

    h4 {
      font-size: 0.875rem;
      font-weight: 600;
      color: $gray-700;
      margin: 0 0 0.5rem 0;
    }

    p {
      font-size: 0.875rem;
      color: $gray-600;
      margin: 0 0 0.25rem 0;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &__additional {
    margin-top: 2rem;
  }

  &__notes {
    font-size: 0.875rem;
    color: $gray-700;
    line-height: 1.6;
    margin: 0;
    white-space: pre-wrap;
  }

  &__empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
  }

  &__empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  &__empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: $gray-900;
    margin: 0 0 0.5rem 0;
  }

  &__empty-description {
    font-size: 0.875rem;
    color: $gray-600;
    margin: 0 0 1.5rem 0;
    max-width: 400px;
    line-height: 1.5;
  }

  &__stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    padding: 1.5rem;
    background: $gray-50;
    border-radius: 12px;
    margin-top: 2rem;
  }

  &__stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
  }

  &__stat-icon {
    font-size: 1.5rem;
    line-height: 1;
  }

  &__stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: $primary;
    line-height: 1;
  }

  &__stat-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: $gray-600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  &__edit {
    padding: 2rem;
  }

  &__section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid $gray-200;

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: $gray-900;
      margin: 0;
    }
  }

  &__documents {
    padding: 2rem;
  }

  &__documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
  }

  &__document-card {
    background: $white;
    border: 1px solid $gray-200;
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transform: translateY(-1px);
    }
  }

  &__document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
  }

  &__document-type {
    font-size: 0.75rem;
    font-weight: 500;
    color: $primary;
    background: rgba($primary, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  &__document-date {
    font-size: 0.75rem;
    color: $gray-500;
  }

  &__document-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: $gray-900;
    margin: 0 0 0.25rem 0;
  }

  &__document-size {
    font-size: 0.75rem;
    color: $gray-600;
    margin: 0 0 0.75rem 0;
  }

  &__document-actions {
    display: flex;
    gap: 0.5rem;
  }

  &__skills {
    padding: 2rem;
  }

  &__skills-categories {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  &__skill-category {
    background: $gray-50;
    border-radius: 12px;
    padding: 1.5rem;
  }

  &__category-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: $gray-900;
    margin: 0 0 1rem 0;
  }

  &__skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }

  &__skill-card {
    background: $white;
    border: 1px solid $gray-200;
    border-radius: 8px;
    padding: 1rem;
  }

  &__skill-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  &__skill-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: $gray-900;
    margin: 0;
  }

  &__skill-level {
    font-size: 0.75rem;
    font-weight: 500;
    color: $white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  &__skill-details {
    font-size: 0.75rem;
    color: $gray-600;

    p {
      margin: 0 0 0.25rem 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &__history {
    padding: 2rem;
  }

  &__timeline {
    position: relative;
    padding-left: 2rem;

    &::before {
      content: '';
      position: absolute;
      left: 0.75rem;
      top: 0;
      bottom: 0;
      width: 2px;
      background: $gray-200;
    }
  }

  &__timeline-item {
    position: relative;
    margin-bottom: 2rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0.5rem;
    width: 12px;
    height: 12px;
    background: $primary;
    border-radius: 50%;
    border: 3px solid $white;
    box-shadow: 0 0 0 2px $primary;
  }

  &__timeline-content {
    background: $white;
    border: 1px solid $gray-200;
    border-radius: 12px;
    padding: 1.5rem;
  }

  &__job-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
  }

  &__job-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: $gray-900;
    margin: 0;
  }

  &__job-company {
    font-size: 0.875rem;
    color: $gray-600;
    font-weight: 500;
  }

  &__job-current {
    font-size: 0.75rem;
    font-weight: 500;
    color: $success;
    background: rgba($success, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  &__job-period {
    font-size: 0.875rem;
    color: $gray-500;
    margin-bottom: 1rem;
  }

  &__job-details,
  &__job-achievements {
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }

    h5 {
      font-size: 0.875rem;
      font-weight: 600;
      color: $gray-700;
      margin: 0 0 0.5rem 0;
    }

    p {
      font-size: 0.875rem;
      color: $gray-600;
      line-height: 1.5;
      margin: 0;
    }
  }

  &__loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba($white, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
    border-radius: 16px;
  }

  &__loading-spinner {
    text-align: center;
    color: $gray-600;
  }

  &__spinner {
    width: 40px;
    height: 40px;
    border: 3px solid $gray-200;
    border-top: 3px solid $primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // Responsive design
  @media (max-width: 768px) {
    &__header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    &__title-section {
      text-align: center;
    }

    &__header-actions {
      justify-content: center;
    }

    &__nav-tabs {
      flex-direction: column;
    }

    &__nav-tab {
      flex: none;
      justify-content: center;
      text-align: center;
    }

    &__summary-grid {
      grid-template-columns: 1fr;
    }

    &__stats {
      flex-direction: column;
      gap: 1rem;
    }

    &__documents-grid,
    &__skills-grid {
      grid-template-columns: 1fr;
    }

    &__job-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    &__timeline {
      padding-left: 1.5rem;

      &::before {
        left: 0.5rem;
      }
    }

    &__timeline-marker {
      left: -1.5rem;
    }
  }

  @media (max-width: 480px) {
    &__overview,
    &__edit,
    &__documents,
    &__skills,
    &__history {
      padding: 1rem;
    }

    &__section-header {
      padding: 1rem;
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    &__card {
      padding: 1rem;
    }

    &__timeline-content {
      padding: 1rem;
    }
  }
}
