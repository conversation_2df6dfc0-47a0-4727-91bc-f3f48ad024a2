<div class="employee-details-manager">
  <!-- Header -->
  <div class="employee-details-manager__header">
    <div class="employee-details-manager__header-content">
      <div class="employee-details-manager__title-section">
        <h2 class="employee-details-manager__title">{{ employeeFullName }} - Details</h2>
        <p class="employee-details-manager__subtitle">Comprehensive employee information management</p>
      </div>

      <div class="employee-details-manager__header-actions">
        <app-button
          variant="secondary"
          size="sm"
          (click)="refreshData()"
          [disabled]="isLoading()">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
            <path d="M21 3v5h-5"/>
            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
            <path d="M3 21v-5h5"/>
          </svg>
          Refresh
        </app-button>

        <app-button
          variant="secondary"
          size="sm"
          (click)="onClose()">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
          Close
        </app-button>
      </div>
    </div>

    <!-- View Navigation -->
    <div class="employee-details-manager__navigation">
      <div class="employee-details-manager__nav-tabs">
        <button
          type="button"
          *ngFor="let view of views"
          class="employee-details-manager__nav-tab"
          [class.employee-details-manager__nav-tab--active]="currentView === view.id"
          (click)="onViewChange(view.id)">
          <span class="employee-details-manager__nav-icon">{{ view.icon }}</span>
          <span class="employee-details-manager__nav-label">{{ view.label }}</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Error Display -->
  <div class="employee-details-manager__error" *ngIf="error() || detailsError() || documentsError() || skillsError() || jobHistoryError()" role="alert">
    <div class="employee-details-manager__error-content">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="10"/>
        <line x1="12" y1="8" x2="12" y2="12"/>
        <line x1="12" y1="16" x2="12.01" y2="16"/>
      </svg>
      <span>{{ error() || detailsError() || documentsError() || skillsError() || jobHistoryError() }}</span>
    </div>
  </div>

  <!-- Content -->
  <div class="employee-details-manager__content">

    <!-- Overview View -->
    <div class="employee-details-manager__overview" *ngIf="currentView === 'overview'">

      <!-- Quick Actions -->
      <div class="employee-details-manager__quick-actions">
        <app-button
          variant="primary"
          (click)="onViewChange('edit')"
          [disabled]="isLoading()">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
            <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
          </svg>
          {{ employeeDetails() ? 'Edit Details' : 'Add Details' }}
        </app-button>
      </div>

      <!-- Employee Details Summary -->
      <div class="employee-details-manager__summary" *ngIf="employeeDetails(); else noDetailsTemplate">
        <div class="employee-details-manager__summary-grid">

          <!-- Personal Information Card -->
          <div class="employee-details-manager__card">
            <h3 class="employee-details-manager__card-title">
              <span class="employee-details-manager__card-icon">👤</span>
              Personal Information
            </h3>
            <div class="employee-details-manager__card-content">
              <div class="employee-details-manager__info-item" *ngIf="employeeDetails()?.blood_group">
                <label>Blood Group</label>
                <span>{{ employeeDetails()?.blood_group }}</span>
              </div>
              <div class="employee-details-manager__info-item" *ngIf="employeeDetails()?.marital_status">
                <label>Marital Status</label>
                <span>{{ employeeDetails()?.marital_status | titlecase }}</span>
              </div>
              <div class="employee-details-manager__info-item" *ngIf="employeeDetails()?.nationality">
                <label>Nationality</label>
                <span>{{ employeeDetails()?.nationality }}</span>
              </div>
            </div>
          </div>

          <!-- Emergency Contact Card -->
          <div class="employee-details-manager__card" *ngIf="employeeDetails()?.emergency_contact_name">
            <h3 class="employee-details-manager__card-title">
              <span class="employee-details-manager__card-icon">🚨</span>
              Emergency Contact
            </h3>
            <div class="employee-details-manager__card-content">
              <div class="employee-details-manager__info-item">
                <label>Name</label>
                <span>{{ employeeDetails()?.emergency_contact_name }}</span>
              </div>
              <div class="employee-details-manager__info-item" *ngIf="employeeDetails()?.emergency_contact_phone">
                <label>Phone</label>
                <span>{{ employeeDetails()?.emergency_contact_phone }}</span>
              </div>
              <div class="employee-details-manager__info-item" *ngIf="employeeDetails()?.emergency_contact_relationship">
                <label>Relationship</label>
                <span>{{ employeeDetails()?.emergency_contact_relationship }}</span>
              </div>
            </div>
          </div>

          <!-- Banking Information Card -->
          <div class="employee-details-manager__card" *ngIf="employeeDetails()?.bank_name">
            <h3 class="employee-details-manager__card-title">
              <span class="employee-details-manager__card-icon">🏦</span>
              Banking Information
            </h3>
            <div class="employee-details-manager__card-content">
              <div class="employee-details-manager__info-item">
                <label>Bank Name</label>
                <span>{{ employeeDetails()?.bank_name }}</span>
              </div>
              <div class="employee-details-manager__info-item" *ngIf="employeeDetails()?.bank_branch">
                <label>Branch</label>
                <span>{{ employeeDetails()?.bank_branch }}</span>
              </div>
              <div class="employee-details-manager__info-item" *ngIf="employeeDetails()?.bank_account_number">
                <label>Account Number</label>
                <span>****{{ employeeDetails()?.bank_account_number?.slice(-4) }}</span>
              </div>
            </div>
          </div>

          <!-- Address Information Card -->
          <div class="employee-details-manager__card" *ngIf="employeeDetails()?.current_address || employeeDetails()?.permanent_address">
            <h3 class="employee-details-manager__card-title">
              <span class="employee-details-manager__card-icon">🏠</span>
              Address Information
            </h3>
            <div class="employee-details-manager__card-content">
              <div class="employee-details-manager__address-section" *ngIf="employeeDetails()?.current_address">
                <h4>Current Address</h4>
                <p>{{ employeeDetails()?.current_address }}</p>
                <p *ngIf="employeeDetails()?.current_city || employeeDetails()?.current_state">
                  {{ employeeDetails()?.current_city }}{{ employeeDetails()?.current_state ? ', ' + employeeDetails()?.current_state : '' }}
                  {{ employeeDetails()?.current_zip }}
                </p>
                <p *ngIf="employeeDetails()?.current_country">{{ employeeDetails()?.current_country }}</p>
              </div>

              <div class="employee-details-manager__address-section" *ngIf="employeeDetails()?.permanent_address">
                <h4>Permanent Address</h4>
                <p>{{ employeeDetails()?.permanent_address }}</p>
                <p *ngIf="employeeDetails()?.permanent_city || employeeDetails()?.permanent_state">
                  {{ employeeDetails()?.permanent_city }}{{ employeeDetails()?.permanent_state ? ', ' + employeeDetails()?.permanent_state : '' }}
                  {{ employeeDetails()?.permanent_zip }}
                </p>
                <p *ngIf="employeeDetails()?.permanent_country">{{ employeeDetails()?.permanent_country }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="employee-details-manager__additional" *ngIf="employeeDetails()?.notes">
          <div class="employee-details-manager__card">
            <h3 class="employee-details-manager__card-title">
              <span class="employee-details-manager__card-icon">📝</span>
              Additional Notes
            </h3>
            <div class="employee-details-manager__card-content">
              <p class="employee-details-manager__notes">{{ employeeDetails()?.notes }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- No Details Template -->
      <ng-template #noDetailsTemplate>
        <div class="employee-details-manager__empty-state">
          <div class="employee-details-manager__empty-icon">📋</div>
          <h3 class="employee-details-manager__empty-title">No Details Available</h3>
          <p class="employee-details-manager__empty-description">
            No additional details have been added for this employee yet.
          </p>
          <app-button
            variant="primary"
            (click)="onViewChange('edit')"
            [disabled]="isLoading()">
            Add Employee Details
          </app-button>
        </div>
      </ng-template>

      <!-- Quick Stats -->
      <div class="employee-details-manager__stats">
        <div class="employee-details-manager__stat-item">
          <span class="employee-details-manager__stat-icon">📄</span>
          <span class="employee-details-manager__stat-value">{{ documents().length }}</span>
          <span class="employee-details-manager__stat-label">Documents</span>
        </div>
        <div class="employee-details-manager__stat-item">
          <span class="employee-details-manager__stat-icon">🎯</span>
          <span class="employee-details-manager__stat-value">{{ skills().length }}</span>
          <span class="employee-details-manager__stat-label">Skills</span>
        </div>
        <div class="employee-details-manager__stat-item">
          <span class="employee-details-manager__stat-icon">📈</span>
          <span class="employee-details-manager__stat-value">{{ jobHistory().length }}</span>
          <span class="employee-details-manager__stat-label">Job History</span>
        </div>
      </div>
    </div>

    <!-- Edit View -->
    <div class="employee-details-manager__edit" *ngIf="currentView === 'edit'">
      <app-employee-details-form
        [employeeDetails]="employeeDetails()"
        [employeeId]="employee?.id || null"
        [isLoading]="isLoadingDetails()"
        [isEdit]="!!employeeDetails()"
        [showAdvancedFields]="true"
        (formSubmit)="onDetailsFormSubmit($event)"
        (formCancel)="onDetailsFormCancel()">
      </app-employee-details-form>
    </div>

    <!-- Documents View -->
    <div class="employee-details-manager__documents" *ngIf="currentView === 'documents'">
      <div class="employee-details-manager__section-header">
        <h3>Employee Documents</h3>
        <app-button variant="primary" size="sm">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          Upload Document
        </app-button>
      </div>

      <div class="employee-details-manager__documents-grid" *ngIf="hasDocuments(); else noDocumentsTemplate">
        <div class="employee-details-manager__document-card" *ngFor="let document of documents()">
          <div class="employee-details-manager__document-header">
            <span class="employee-details-manager__document-type">{{ getDocumentTypeLabel(document.document_type) }}</span>
            <span class="employee-details-manager__document-date">{{ formatDate(document.created_at) }}</span>
          </div>
          <div class="employee-details-manager__document-content">
            <h4 class="employee-details-manager__document-name">{{ document.document_name }}</h4>
            <p class="employee-details-manager__document-size">{{ formatFileSize(document.file_size) }}</p>
            <div class="employee-details-manager__document-actions">
              <app-button variant="secondary" size="sm">View</app-button>
              <app-button variant="secondary" size="sm">Download</app-button>
            </div>
          </div>
        </div>
      </div>

      <ng-template #noDocumentsTemplate>
        <div class="employee-details-manager__empty-state">
          <div class="employee-details-manager__empty-icon">📄</div>
          <h3 class="employee-details-manager__empty-title">No Documents</h3>
          <p class="employee-details-manager__empty-description">
            No documents have been uploaded for this employee yet.
          </p>
        </div>
      </ng-template>
    </div>

    <!-- Skills View -->
    <div class="employee-details-manager__skills" *ngIf="currentView === 'skills'">
      <div class="employee-details-manager__section-header">
        <h3>Employee Skills</h3>
        <app-button variant="primary" size="sm">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          Add Skill
        </app-button>
      </div>

      <div class="employee-details-manager__skills-categories" *ngIf="hasSkills(); else noSkillsTemplate">
        <div class="employee-details-manager__skill-category" *ngFor="let category of Object.entries(skillsByCategory())">
          <h4 class="employee-details-manager__category-title">{{ getSkillCategoryLabel(category[0]) }}</h4>
          <div class="employee-details-manager__skills-grid">
            <div class="employee-details-manager__skill-card" *ngFor="let skill of category[1]">
              <div class="employee-details-manager__skill-header">
                <h5 class="employee-details-manager__skill-name">{{ skill.skill_name }}</h5>
                <span class="employee-details-manager__skill-level"
                      [style.background-color]="getProficiencyColor(skill.proficiency_level)">
                  {{ skill.proficiency_level | titlecase }}
                </span>
              </div>
              <div class="employee-details-manager__skill-details" *ngIf="skill.years_of_experience || skill.certification_name">
                <p *ngIf="skill.years_of_experience">{{ skill.years_of_experience }} years experience</p>
                <p *ngIf="skill.certification_name">Certified: {{ skill.certification_name }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <ng-template #noSkillsTemplate>
        <div class="employee-details-manager__empty-state">
          <div class="employee-details-manager__empty-icon">🎯</div>
          <h3 class="employee-details-manager__empty-title">No Skills</h3>
          <p class="employee-details-manager__empty-description">
            No skills have been added for this employee yet.
          </p>
        </div>
      </ng-template>
    </div>

    <!-- Job History View -->
    <div class="employee-details-manager__history" *ngIf="currentView === 'history'">
      <div class="employee-details-manager__section-header">
        <h3>Job History</h3>
        <app-button variant="primary" size="sm">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          Add Job
        </app-button>
      </div>

      <div class="employee-details-manager__timeline" *ngIf="hasJobHistory(); else noHistoryTemplate">
        <div class="employee-details-manager__timeline-item" *ngFor="let job of jobHistory()">
          <div class="employee-details-manager__timeline-marker"></div>
          <div class="employee-details-manager__timeline-content">
            <div class="employee-details-manager__job-header">
              <h4 class="employee-details-manager__job-title">{{ job.job_title }}</h4>
              <span class="employee-details-manager__job-company">{{ job.company_name }}</span>
              <span class="employee-details-manager__job-current" *ngIf="job.is_current">Current</span>
            </div>
            <div class="employee-details-manager__job-period">
              {{ formatDate(job.start_date) }} - {{ job.end_date ? formatDate(job.end_date) : 'Present' }}
            </div>
            <div class="employee-details-manager__job-details" *ngIf="job.job_description">
              <p>{{ job.job_description }}</p>
            </div>
            <div class="employee-details-manager__job-achievements" *ngIf="job.achievements">
              <h5>Key Achievements:</h5>
              <p>{{ job.achievements }}</p>
            </div>
          </div>
        </div>
      </div>

      <ng-template #noHistoryTemplate>
        <div class="employee-details-manager__empty-state">
          <div class="employee-details-manager__empty-icon">📈</div>
          <h3 class="employee-details-manager__empty-title">No Job History</h3>
          <p class="employee-details-manager__empty-description">
            No job history has been added for this employee yet.
          </p>
        </div>
      </ng-template>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="employee-details-manager__loading" *ngIf="isLoading()">
    <div class="employee-details-manager__loading-spinner">
      <div class="employee-details-manager__spinner"></div>
      <p>Loading employee details...</p>
    </div>
  </div>
</div>
