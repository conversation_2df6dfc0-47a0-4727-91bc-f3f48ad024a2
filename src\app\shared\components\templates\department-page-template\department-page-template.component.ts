import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DepartmentTableComponent } from '../../organisms/department-table/department-table.component';
import { AddDepartmentModalComponent } from '../../../modals/add-department-modal/add-department-modal.component';
import { EditDepartmentModalComponent } from '../../../modals/edit-department-modal/edit-department-modal.component';

@Component({
  selector: 'app-department-page-template',
  standalone: true,
  imports: [
    CommonModule,
    DepartmentTableComponent,
    AddDepartmentModalComponent,
    EditDepartmentModalComponent
  ],
  templateUrl: './department-page-template.component.html',
  styleUrl: './department-page-template.component.scss'
})
export class DepartmentPageTemplateComponent {
  // This is a template component that composes the page layout
  // It doesn't contain business logic, just layout structure
}
