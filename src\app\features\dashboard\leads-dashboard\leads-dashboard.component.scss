@import '../../../../styles/variables/_colors';

.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100%;
  height: 100%;
}

// Dashboard Header
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .title-section {
    h1 {
      font-size: 24px;
      font-weight: 600;
      color: $dark;
      margin: 0 0 5px 0;
    }

    .breadcrumbs {
      font-size: 14px;
      color: $gray-600;

      a {
        color: $gray-600;
        text-decoration: none;

        &:hover {
          color: $primary;
        }
      }

      .separator {
        margin: 0 5px;
      }

      .current {
        color: $gray-800;
      }
    }
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 10px;

    .btn-export, .btn-filter {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 15px;
      background-color: white;
      border: 1px solid $border-color;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      color: $gray-700;

      i {
        margin-right: 5px;
      }

      &:hover {
        background-color: $gray-100;
      }
    }

    .date-range {
      input {
        padding: 8px 15px;
        border: 1px solid $border-color;
        border-radius: 4px;
        font-size: 14px;
        color: $gray-700;
        width: 200px;
      }
    }
  }
}

// Metric Cards
.metric-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;

  .metric-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .card-header {
      padding: 15px;
      display: flex;
      align-items: center;
      gap: 15px;

      .icon-container {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 18px;
        }
      }

      .card-title {
        font-size: 14px;
        font-weight: 500;
        color: $gray-700;
      }
    }

    .card-content {
      padding: 15px;

      .value {
        font-size: 24px;
        font-weight: 600;
        color: $dark;
        margin-bottom: 10px;
      }

      .progress-bar {
        height: 6px;
        background-color: $gray-200;
        border-radius: 3px;
        margin-bottom: 10px;

        .progress {
          height: 100%;
          border-radius: 3px;
        }
      }

      .change {
        font-size: 13px;
        display: flex;
        align-items: center;
        gap: 5px;

        &.positive {
          color: $success;
        }

        &.negative {
          color: $danger;
        }
      }
    }
  }
}

// Dashboard Row
.dashboard-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;

  &:last-child {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

// Dashboard Card
.dashboard-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .card-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid $gray-200;

    h2 {
      font-size: 16px;
      font-weight: 600;
      color: $dark;
      margin: 0;
    }

    .date-range {
      font-size: 13px;
      color: $gray-600;
      background-color: $gray-100;
      padding: 5px 10px;
      border-radius: 4px;
    }
  }
}

// Pipeline Stages
.pipeline-content {
  padding: 15px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;

  .pipeline-stage {
    text-align: center;

    .stage-header {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;
      margin-bottom: 10px;

      .color-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
      }

      .stage-name {
        font-size: 13px;
        color: $gray-600;
      }
    }

    .stage-value {
      font-size: 18px;
      font-weight: 600;
      color: $dark;
    }
  }
}

// New Leads
.new-leads-content {
  padding: 15px;
  height: 200px;

  .placeholder-chart {
    width: 100%;
    height: 100%;
    background-color: $gray-100;
    border-radius: 8px;
  }
}

// Lost Leads
.lost-leads-content {
  padding: 15px;

  .reason-list {
    .reason-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .reason-name {
        width: 120px;
        font-size: 13px;
        color: $gray-700;
      }

      .reason-bar {
        flex: 1;
        height: 8px;
        background-color: $gray-200;
        border-radius: 4px;
        margin: 0 15px;

        .reason-progress {
          height: 100%;
          background-color: $primary;
          border-radius: 4px;
        }
      }

      .reason-percentage {
        width: 40px;
        font-size: 13px;
        font-weight: 500;
        color: $gray-700;
        text-align: right;
      }
    }
  }
}

// Leads By Companies
.companies-content {
  padding: 15px;

  .company-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid $gray-200;

    &:last-child {
      border-bottom: none;
    }

    .company-logo {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: $gray-200;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .logo-placeholder {
        font-size: 18px;
        font-weight: 600;
        color: $gray-600;
      }
    }

    .company-info {
      flex: 1;

      .company-name {
        font-size: 14px;
        font-weight: 500;
        color: $dark;
        margin-bottom: 3px;
      }

      .company-value {
        font-size: 13px;
        color: $gray-600;
      }
    }

    .company-tag {
      padding: 5px 10px;
      background-color: #e1bee7;
      color: #7b1fa2;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// Leads By Source
.source-content {
  padding: 15px;

  .source-list {
    .source-header {
      display: flex;
      justify-content: space-between;
      padding-bottom: 10px;
      border-bottom: 1px solid $gray-200;
      margin-bottom: 10px;

      .source-title, .source-percentage {
        font-size: 13px;
        font-weight: 600;
        color: $gray-700;
      }
    }

    .source-item {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;

      .source-name {
        font-size: 14px;
        color: $gray-700;
      }

      .source-value {
        font-size: 14px;
        font-weight: 500;
        color: $dark;
      }
    }
  }
}



