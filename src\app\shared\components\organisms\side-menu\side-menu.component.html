<div class="side-menu" [ngClass]="{'collapsed': collapsed}">
  @if (!collapsed) {
    <div class="side-menu-header">
      <div class="logo-container">
        @if (logo) {
          <img [src]="logo" alt="Logo" class="logo">
        }
        <span class="title">Smart<span>HR</span></span>
      </div>
    </div>
  } @else {
    <div class="side-menu-header">
      @if (logo) {
        <img [src]="logo" alt="Logo" class="logo">
      }
    </div>
  }


  <div class="side-menu-content">
    @if (menuGroups && menuGroups.length > 0) {
      @for (group of menuGroups; track group.title) {
        <app-menu-group
          [title]="group.title"
          [items]="group.items"
          [expanded]="group.expanded || false"
          [collapsed]="collapsed">
        </app-menu-group>
      }
    } @else {
      @for (group of employeeMenuGroups; track group.title) {
        <app-menu-group
          [title]="group.title"
          [expanded]="group.expanded || false"
          [collapsed]="collapsed">
        </app-menu-group>

        @if (group.title === 'MAIN MENU') {
          @for (item of group.items; track item.label) {
            <div class="menu-item-container">
              <app-menu-item
                [icon]="item.icon"
                [label]="item.label"
                [route]="item.route"
                [active]="item.active || false"
                [badge]="item.badge || ''"
                [badgeType]="item.badgeType || 'primary'"
                [hasSubmenu]="item.hasSubmenu || false"
                [submenuExpanded]="expandedSubmenus[item.label]"
                (toggleSubmenu)="toggleSubmenu(item.label)">
              </app-menu-item>

              <div class="submenu" [ngClass]="{'expanded': expandedSubmenus[item.label]}">
                @for (subItem of getSubmenuItems(item.label); track subItem.label) {
                  <div class="submenu-item" [ngClass]="{'active': subItem.active}">
                    <a [routerLink]="subItem.route" [routerLinkActive]="'active'" class="submenu-link">
                      {{ subItem.label }}
                    </a>
                  </div>
                }
              </div>
            </div>
          }
        } @else {
          @for (item of group.items; track item.label) {
            @if (item.hasSubmenu) {
              <div class="menu-item-container">
                <app-menu-item
                  [icon]="item.icon"
                  [label]="item.label"
                  [route]="item.route"
                  [active]="item.active || false"
                  [badge]="item.badge || ''"
                  [badgeType]="item.badgeType || 'primary'"
                  [hasSubmenu]="item.hasSubmenu || false"
                  [submenuExpanded]="expandedSubmenus[item.label]"
                  (toggleSubmenu)="toggleSubmenu(item.label)">
                </app-menu-item>

                <div class="submenu" [ngClass]="{'expanded': expandedSubmenus[item.label]}">
                  @for (subItem of getSubmenuItems(item.label); track subItem.label) {
                    <div class="submenu-item" [ngClass]="{'active': subItem.active}">
                      <a [routerLink]="subItem.route" [routerLinkActive]="'active'" class="submenu-link">
                        {{ subItem.label }}
                      </a>
                    </div>
                  }
                </div>
              </div>
            } @else {
              <app-menu-item
                [icon]="item.icon"
                [label]="item.label"
                [route]="item.route"
                [active]="item.active || false"
                [badge]="item.badge || ''"
                [badgeType]="item.badgeType || 'primary'">
              </app-menu-item>
            }
          }
        }
      }
    }
  </div>
</div>
