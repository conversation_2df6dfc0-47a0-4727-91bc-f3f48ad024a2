import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MaintenanceService {
  private maintenanceModeSubject = new BehaviorSubject<boolean>(false);
  public maintenanceMode$ = this.maintenanceModeSubject.asObservable();

  // Configuration for maintenance mode
  private maintenanceConfig = {
    enabled: false,
    startTime: new Date(),
    endTime: new Date(new Date().getTime() + 2 * 60 * 60 * 1000), // 2 hours from now
    message: 'We\'re currently performing scheduled maintenance to improve your experience.',
    allowedRoles: ['admin', 'superadmin']
  };

  constructor() {
    // Check if maintenance mode is enabled in localStorage or from server config
    this.checkMaintenanceStatus();
  }

  /**
   * Check if the application is in maintenance mode
   */
  private checkMaintenanceStatus(): void {
    // This would typically come from an API or configuration
    // For demo purposes, we're using a local configuration
    this.maintenanceModeSubject.next(this.maintenanceConfig.enabled);
  }

  /**
   * Enable maintenance mode
   */
  enableMaintenanceMode(endTime?: Date, message?: string): void {
    this.maintenanceConfig.enabled = true;
    
    if (endTime) {
      this.maintenanceConfig.endTime = endTime;
    }
    
    if (message) {
      this.maintenanceConfig.message = message;
    }
    
    this.maintenanceConfig.startTime = new Date();
    this.maintenanceModeSubject.next(true);
    
    // This would typically update a configuration on the server
    console.log('Maintenance mode enabled:', this.maintenanceConfig);
  }

  /**
   * Disable maintenance mode
   */
  disableMaintenanceMode(): void {
    this.maintenanceConfig.enabled = false;
    this.maintenanceModeSubject.next(false);
    
    // This would typically update a configuration on the server
    console.log('Maintenance mode disabled');
  }

  /**
   * Get the current maintenance configuration
   */
  getMaintenanceConfig(): any {
    return { ...this.maintenanceConfig };
  }

  /**
   * Get the estimated completion time as a string
   */
  getEstimatedCompletionTime(): string {
    const now = new Date();
    const endTime = this.maintenanceConfig.endTime;
    const diffMs = endTime.getTime() - now.getTime();
    
    if (diffMs <= 0) {
      return 'Completed';
    }
    
    const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHrs > 0) {
      return `${diffHrs} hour${diffHrs > 1 ? 's' : ''} ${diffMins > 0 ? `and ${diffMins} minute${diffMins > 1 ? 's' : ''}` : ''}`;
    } else {
      return `${diffMins} minute${diffMins > 1 ? 's' : ''}`;
    }
  }

  /**
   * Check if a user with the given role is allowed during maintenance
   */
  isUserAllowedDuringMaintenance(userRole: string): boolean {
    return this.maintenanceConfig.allowedRoles.includes(userRole);
  }
}
