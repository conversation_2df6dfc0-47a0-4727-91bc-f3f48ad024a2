/**
 * User interface
 */
export interface User {
  id?: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  company: string;
  is_active?: boolean;
  is_staff?: boolean;
  date_joined?: string;
  last_login?: string;
}

/**
 * Registration request payload
 */
export interface RegistrationRequest {
  username: string;
  email: string;
  password: string;
  password2: string;
  first_name: string;
  last_name: string;
  company: string;
}

/**
 * Registration response
 */
export interface RegistrationResponse extends AuthTokens {
  user: User;
  message?: string;
}

/**
 * Login request payload
 */
export interface LoginRequest {
  username: string;
  password: string;
}

/**
 * Auth tokens
 */
export interface AuthTokens {
  access: string;
  refresh: string;
  token_type: string;
  expires_in?: number;
}

/**
 * Login response
 */
export interface LoginResponse extends AuthTokens {
  user?: User;
}

/**
 * Token refresh request
 */
export interface RefreshTokenRequest {
  refresh_token: string;
}

/**
 * Token refresh response
 */
export interface RefreshTokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in?: number;
}

/**
 * Token verification request
 */
export interface VerifyTokenRequest {
  access_token: string;
}

/**
 * Token verification response
 */
export interface VerifyTokenResponse {
  is_valid: boolean;
  detail?: string;
}
