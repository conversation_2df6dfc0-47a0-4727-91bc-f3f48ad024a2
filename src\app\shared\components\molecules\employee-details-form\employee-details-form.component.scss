@import '../../../../../styles/variables/colors';

.employee-details-form {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  &__navigation {
    margin-bottom: 2rem;
    background: $white;
    border-radius: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }

  &__nav-tabs {
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid $gray-200;
  }

  &__nav-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: $gray-600;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 0;

    &:hover {
      background-color: $gray-50;
      color: $gray-800;
    }

    &--active {
      color: $primary;
      border-bottom-color: $primary;
      background-color: rgba($primary, 0.05);
    }
  }

  &__nav-icon {
    font-size: 1.125rem;
    line-height: 1;
  }

  &__nav-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__content {
    background: $white;
    border-radius: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }

  &__section {
    padding: 2rem;
    animation: slideInUp 0.3s ease-out;
  }

  &__section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: $gray-900;
    margin: 0 0 1.5rem 0;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid $gray-100;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &::before {
      content: '';
      width: 4px;
      height: 1.5rem;
      background: $primary;
      border-radius: 2px;
    }
  }

  &__subsection {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid $gray-100;

    &:first-child {
      margin-top: 0;
      padding-top: 0;
      border-top: none;
    }
  }

  &__subsection-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: $gray-800;
    margin: 0 0 1rem 0;
  }

  &__grid {
    display: grid;
    gap: 1.5rem;

    &--2-col {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    &--3-col {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }

  &__field {
    display: flex;
    flex-direction: column;

    &--full-width {
      grid-column: 1 / -1;
    }
  }

  &__label {
    display: block;
    font-weight: 500;
    color: $gray-700;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  &__input,
  &__select {
    width: 100%;
    padding: 0.75rem 1rem;
    background: $white;
    border: 1px solid $gray-300;
    border-radius: 12px;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: $gray-900;
    transition: all 0.2s ease;
    min-height: 2.75rem;

    &::placeholder {
      color: $gray-500;
    }

    &:hover:not(:disabled) {
      border-color: $gray-400;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.1);
    }

    &:disabled {
      background-color: $gray-100;
      color: $gray-500;
      cursor: not-allowed;
    }

    &--error {
      border-color: $danger;
      box-shadow: 0 0 0 3px rgba($danger, 0.1);

      &:focus {
        border-color: $danger;
        box-shadow: 0 0 0 3px rgba($danger, 0.1);
      }
    }
  }

  &__textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    background: $white;
    border: 1px solid $gray-300;
    border-radius: 12px;
    font-size: 0.875rem;
    line-height: 1.5rem;
    color: $gray-900;
    transition: all 0.2s ease;
    resize: vertical;
    min-height: 120px;
    font-family: inherit;

    &::placeholder {
      color: $gray-500;
    }

    &:hover:not(:disabled) {
      border-color: $gray-400;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.1);
    }

    &:disabled {
      background-color: $gray-100;
      color: $gray-500;
      cursor: not-allowed;
    }
  }

  &__error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    color: $danger;
    font-size: 0.75rem;
    line-height: 1rem;

    &::before {
      content: '⚠';
      font-size: 0.875rem;
    }
  }

  &__address-actions {
    display: flex;
    gap: 1rem;
    margin: 1.5rem 0;
    padding: 1rem;
    background: $gray-50;
    border-radius: 12px;
    border: 1px solid $gray-200;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem 2rem;
    background: $gray-50;
    border-top: 1px solid $gray-200;
  }

  // Responsive design
  @media (max-width: 768px) {
    &__nav-tabs {
      flex-direction: column;
    }

    &__nav-tab {
      flex: none;
      justify-content: center;
      text-align: center;
    }

    &__grid {
      &--2-col,
      &--3-col {
        grid-template-columns: 1fr;
      }
    }

    &__actions {
      flex-direction: column-reverse;
      gap: 0.75rem;

      app-button {
        width: 100%;
      }
    }

    &__section {
      padding: 1.5rem;
    }

    &__address-actions {
      flex-direction: column;
      gap: 0.75rem;

      app-button {
        width: 100%;
      }
    }
  }

  @media (max-width: 480px) {
    &__field {
      &--full-width {
        grid-column: 1;
      }
    }

    &__section {
      padding: 1rem;
    }

    &__section-title {
      font-size: 1.25rem;
      margin-bottom: 1rem;
    }

    &__nav-label {
      font-size: 0.75rem;
    }

    &__actions {
      padding: 1rem;
    }
  }

  // Animation for form sections
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // Focus within styling for better UX
  &__section:focus-within {
    .employee-details-form__section-title {
      color: $primary;
    }
  }

  // Loading state
  &--loading {
    pointer-events: none;
    opacity: 0.7;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba($white, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
  }

  // Success state
  &--success {
    .employee-details-form__section {
      border-color: $success;
      background: rgba($success, 0.02);
    }
  }

  // Error state
  &--error {
    .employee-details-form__section {
      border-color: $danger;
      background: rgba($danger, 0.02);
    }
  }
}
