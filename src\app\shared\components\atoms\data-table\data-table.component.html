<div class="data-table">
  <table class="data-table__table">
    <thead class="data-table__head">
      <tr class="data-table__row">
        <th *ngIf="selectable" class="data-table__cell data-table__cell--select">
          <div class="data-table__checkbox">
            <input type="checkbox" id="select-all" class="data-table__checkbox-input" [checked]="allSelected" (change)="toggleSelectAll()">
            <label for="select-all" class="data-table__checkbox-label"></label>
          </div>
        </th>
        <th *ngFor="let column of dynamicColumns ? generatedColumns : columns"
            class="data-table__cell data-table__cell--header"
            [ngClass]="{'data-table__cell--sortable': column.sortable}"
            [ngStyle]="{'width': column.width, 'text-align': column.align || 'left'}"
            (click)="column.sortable ? sort(column.key) : null">
          <div class="data-table__header">
            <span *ngIf="column.icon" class="data-table__header-icon">{{ column.icon }}</span>
            <span class="data-table__header-text">{{ column.label }}</span>
            <span *ngIf="column.sortable" class="data-table__sort-icon">
              <span *ngIf="sortColumn !== column.key">⇅</span>
              <span *ngIf="sortColumn === column.key && sortDirection === 'asc'">↑</span>
              <span *ngIf="sortColumn === column.key && sortDirection === 'desc'">↓</span>
            </span>
          </div>
        </th>
      </tr>
    </thead>
    <tbody class="data-table__body">
      <tr *ngFor="let row of data; let i = index"
          class="data-table__row"
          [ngClass]="{'data-table__row--selected': isSelected(row), 'data-table__row--highlighted': i % 2 === 1}"
          (click)="rowClick.emit(row)">
        <td *ngIf="selectable" class="data-table__cell data-table__cell--select">
          <div class="data-table__checkbox">
            <input type="checkbox" [id]="'select-row-' + i" class="data-table__checkbox-input" [checked]="isSelected(row)" (change)="toggleSelect(row, $event)">
            <label [for]="'select-row-' + i" class="data-table__checkbox-label"></label>
          </div>
        </td>
        <td *ngFor="let column of dynamicColumns ? generatedColumns : columns"
            class="data-table__cell"
            [ngStyle]="{'text-align': column.align || 'left'}">
          <ng-container [ngSwitch]="column.type">
            <!-- Date type -->
            <span *ngSwitchCase="'date'" class="data-table__date">
              {{ row[column.key] | date:'MMM d, yyyy' }}
            </span>

            <!-- Badge type -->
            <span *ngSwitchCase="'badge'"
                  class="data-table__badge"
                  [ngClass]="'data-table__badge--' + getBadgeType(row[column.key])">
              <span class="data-table__badge-dot"></span>
              <span class="data-table__badge-text">{{ row[column.key] }}</span>
            </span>

            <!-- Image type -->
            <div *ngSwitchCase="'image'" class="data-table__avatar">
              <img [src]="column.imageKey ? row[column.imageKey] : (row.avatar || row.profile_picture_url || row.image_url || row[column.key])"
                   alt="Avatar"
                   class="data-table__avatar-image">
            </div>

            <!-- Location type -->
            <div *ngSwitchCase="'location'" class="data-table__location">
              <span class="data-table__location-flag"
                    [ngClass]="'data-table__location-flag--' + getCountryCode(row[column.key])">
                {{ getCountryFlag(row[column.key]) }}
              </span>
              <span class="data-table__location-text">{{ row[column.key] }}</span>
            </div>

            <!-- Custom type -->
            <ng-container *ngSwitchCase="'custom'">
              <ng-container *ngTemplateOutlet="customTemplate; context: {$implicit: row, column: column}"></ng-container>
            </ng-container>

            <!-- Actions type -->
            <div *ngSwitchCase="'actions'" class="data-table__actions">
              <button *ngIf="column.actions?.view"
                      class="data-table__action-btn data-table__action-btn--view"
                      (click)="onViewClick($event, row)"
                      title="View">
                <i class="fa fa-info-circle data-table__action-icon"></i>
              </button>
              <button *ngIf="column.actions?.edit"
                      class="data-table__action-btn data-table__action-btn--edit"
                      (click)="onEditClick($event, row)"
                      title="Edit">
                <i class="fa fa-pencil data-table__action-icon"></i>
              </button>
              <button *ngIf="column.actions?.delete"
                      class="data-table__action-btn data-table__action-btn--delete"
                      (click)="onDeleteClick($event, row)"
                      title="Delete">
                <i class="fa fa-times-circle data-table__action-icon"></i>
              </button>
              <ng-container *ngIf="column.actions?.custom">
                <button *ngFor="let action of column.actions?.custom"
                        class="data-table__action-btn data-table__action-btn--custom"
                        (click)="onCustomClick($event, action, row)"
                        [title]="action">
                  {{ action }}
                </button>
              </ng-container>
            </div>

            <!-- Default type -->
            <span *ngSwitchDefault class="data-table__text">{{ row[column.key] }}</span>
          </ng-container>
        </td>
      </tr>

      <!-- Empty state -->
      <tr *ngIf="data.length === 0" class="data-table__row data-table__row--empty">
        <td [attr.colspan]="selectable ? (dynamicColumns ? generatedColumns.length : columns.length) + 1 : (dynamicColumns ? generatedColumns.length : columns.length)" class="data-table__cell data-table__cell--empty">
          No data available
        </td>
      </tr>
    </tbody>
  </table>
</div>
