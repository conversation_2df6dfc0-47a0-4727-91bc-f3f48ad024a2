import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Import interfaces
import {
  EmployeeDetails,
  EmployeeDetailsInput,
  EmployeeDocument,
  EmployeeSkill,
  EmployeeJobHistory,
  EmployeeDetailsResponse
} from '../models/employee-extended.interface';

// Paginated response interface
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

@Injectable({
  providedIn: 'root'
})
export class EmployeeDetailsService {
  private http = inject(HttpClient);
  private apiUrl = environment.apiUrl;
  private employeeDetailsEndpoint = '/api/v1/employee-details/';
  private employeeDocumentsEndpoint = '/api/v1/employee-documents/';
  private employeeSkillsEndpoint = '/api/v1/employee-skills/';
  private employeeJobHistoryEndpoint = '/api/v1/employee-job-history/';

  /**
   * Get all employee details with pagination and filtering
   * @param page Page number (1-based)
   * @param pageSize Number of items per page
   * @param searchTerm Optional search term
   * @param ordering Optional ordering field
   * @returns Observable of paginated employee details response
   */
  getEmployeeDetails(
    page: number = 1,
    pageSize: number = 10,
    searchTerm?: string,
    ordering?: string
  ): Observable<PaginatedResponse<EmployeeDetails>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    if (searchTerm) {
      params = params.set('search', searchTerm);
    }

    if (ordering) {
      params = params.set('ordering', ordering);
    }

    return this.http.get<PaginatedResponse<EmployeeDetails>>(
      `${this.apiUrl}${this.employeeDetailsEndpoint}`,
      { params }
    ).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get employee details by ID
   * @param id The employee details ID
   * @returns Observable of EmployeeDetails
   */
  getEmployeeDetailsById(id: number): Observable<EmployeeDetails> {
    return this.http.get<EmployeeDetails>(`${this.apiUrl}${this.employeeDetailsEndpoint}${id}/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get employee details by employee ID
   * @param employeeId The employee ID
   * @returns Observable of EmployeeDetails
   */
  getEmployeeDetailsByEmployeeId(employeeId: number): Observable<EmployeeDetails> {
    const params = new HttpParams().set('employee_id', employeeId.toString());

    return this.http.get<PaginatedResponse<EmployeeDetails>>(
      `${this.apiUrl}${this.employeeDetailsEndpoint}`,
      { params }
    ).pipe(
      map(response => response.results[0] || null),
      catchError(this.handleError)
    );
  }

  /**
   * Create new employee details
   * @param employeeDetails The employee details data to create
   * @returns Observable of the created EmployeeDetails
   */
  createEmployeeDetails(employeeDetails: EmployeeDetailsInput): Observable<EmployeeDetails> {
    return this.http.post<EmployeeDetails>(
      `${this.apiUrl}${this.employeeDetailsEndpoint}`,
      employeeDetails
    ).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Update employee details (full update)
   * @param id The employee details ID
   * @param employeeDetails The employee details data to update
   * @returns Observable of the updated EmployeeDetails
   */
  updateEmployeeDetails(id: number, employeeDetails: EmployeeDetailsInput): Observable<EmployeeDetails> {
    return this.http.put<EmployeeDetails>(
      `${this.apiUrl}${this.employeeDetailsEndpoint}${id}/`,
      employeeDetails
    ).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Partially update employee details
   * @param id The employee details ID
   * @param employeeDetails The partial employee details data to update
   * @returns Observable of the updated EmployeeDetails
   */
  patchEmployeeDetails(id: number, employeeDetails: Partial<EmployeeDetailsInput>): Observable<EmployeeDetails> {
    return this.http.patch<EmployeeDetails>(
      `${this.apiUrl}${this.employeeDetailsEndpoint}${id}/`,
      employeeDetails
    ).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Delete employee details
   * @param id The employee details ID
   * @returns Observable of void
   */
  deleteEmployeeDetails(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}${this.employeeDetailsEndpoint}${id}/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get comprehensive employee details including all related data
   * @param employeeId The employee ID
   * @returns Observable of EmployeeDetailsResponse
   */
  getComprehensiveEmployeeDetails(employeeId: number): Observable<EmployeeDetailsResponse> {
    // This would typically be a single API call, but we'll simulate it by combining multiple calls
    return this.http.get<EmployeeDetailsResponse>(
      `${this.apiUrl}/api/v1/employees/${employeeId}/comprehensive/`
    ).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get employee documents
   * @param employeeId The employee ID
   * @returns Observable of EmployeeDocument array
   */
  getEmployeeDocuments(employeeId: number): Observable<EmployeeDocument[]> {
    const params = new HttpParams().set('employee_id', employeeId.toString());

    return this.http.get<PaginatedResponse<EmployeeDocument>>(
      `${this.apiUrl}${this.employeeDocumentsEndpoint}`,
      { params }
    ).pipe(
      map(response => response.results),
      catchError(this.handleError)
    );
  }

  /**
   * Upload employee document
   * @param employeeId The employee ID
   * @param documentType The type of document
   * @param file The file to upload
   * @param documentName Optional document name
   * @returns Observable of EmployeeDocument
   */
  uploadEmployeeDocument(
    employeeId: number,
    documentType: string,
    file: File,
    documentName?: string
  ): Observable<EmployeeDocument> {
    const formData = new FormData();
    formData.append('employee_id', employeeId.toString());
    formData.append('document_type', documentType);
    formData.append('document_file', file);

    if (documentName) {
      formData.append('document_name', documentName);
    }

    return this.http.post<EmployeeDocument>(
      `${this.apiUrl}${this.employeeDocumentsEndpoint}`,
      formData
    ).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get employee skills
   * @param employeeId The employee ID
   * @returns Observable of EmployeeSkill array
   */
  getEmployeeSkills(employeeId: number): Observable<EmployeeSkill[]> {
    const params = new HttpParams().set('employee_id', employeeId.toString());

    return this.http.get<PaginatedResponse<EmployeeSkill>>(
      `${this.apiUrl}${this.employeeSkillsEndpoint}`,
      { params }
    ).pipe(
      map(response => response.results),
      catchError(this.handleError)
    );
  }

  /**
   * Add employee skill
   * @param skill The skill data to add
   * @returns Observable of EmployeeSkill
   */
  addEmployeeSkill(skill: Omit<EmployeeSkill, 'id' | 'created_at' | 'updated_at'>): Observable<EmployeeSkill> {
    return this.http.post<EmployeeSkill>(
      `${this.apiUrl}${this.employeeSkillsEndpoint}`,
      skill
    ).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get employee job history
   * @param employeeId The employee ID
   * @returns Observable of EmployeeJobHistory array
   */
  getEmployeeJobHistory(employeeId: number): Observable<EmployeeJobHistory[]> {
    const params = new HttpParams().set('employee_id', employeeId.toString());

    return this.http.get<PaginatedResponse<EmployeeJobHistory>>(
      `${this.apiUrl}${this.employeeJobHistoryEndpoint}`,
      { params }
    ).pipe(
      map(response => response.results),
      catchError(this.handleError)
    );
  }

  /**
   * Add employee job history
   * @param jobHistory The job history data to add
   * @returns Observable of EmployeeJobHistory
   */
  addEmployeeJobHistory(
    jobHistory: Omit<EmployeeJobHistory, 'id' | 'created_at' | 'updated_at'>
  ): Observable<EmployeeJobHistory> {
    return this.http.post<EmployeeJobHistory>(
      `${this.apiUrl}${this.employeeJobHistoryEndpoint}`,
      jobHistory
    ).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Handle HTTP errors
   * @param error The HTTP error response
   * @returns Observable that throws an error
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;

      if (error.error && typeof error.error === 'object') {
        // Try to extract more specific error messages
        if (error.error.detail) {
          errorMessage = error.error.detail;
        } else if (error.error.message) {
          errorMessage = error.error.message;
        }
      }
    }

    console.error('EmployeeDetailsService Error:', errorMessage);
    return throwError(() => errorMessage);
  }
}
