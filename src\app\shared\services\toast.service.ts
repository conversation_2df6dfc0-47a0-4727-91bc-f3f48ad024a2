import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Toast {
  id: number;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  duration?: number;
}

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  private toasts: BehaviorSubject<Toast[]> = new BehaviorSubject<Toast[]>([]);
  public toasts$: Observable<Toast[]> = this.toasts.asObservable();
  private nextId = 0;

  /**
   * Show a success toast notification
   * @param message The message to display
   * @param duration Duration in milliseconds (default: 3000)
   * @returns The ID of the toast
   */
  success(message: string, duration: number = 3000): number {
    return this.show({
      id: this.getNextId(),
      message,
      type: 'success',
      duration
    });
  }

  /**
   * Show an error toast notification
   * @param message The message to display
   * @param duration Duration in milliseconds (default: 5000)
   * @returns The ID of the toast
   */
  error(message: string, duration: number = 5000): number {
    return this.show({
      id: this.getNextId(),
      message,
      type: 'error',
      duration
    });
  }

  /**
   * Show an info toast notification
   * @param message The message to display
   * @param duration Duration in milliseconds (default: 3000)
   * @returns The ID of the toast
   */
  info(message: string, duration: number = 3000): number {
    return this.show({
      id: this.getNextId(),
      message,
      type: 'info',
      duration
    });
  }

  /**
   * Show a warning toast notification
   * @param message The message to display
   * @param duration Duration in milliseconds (default: 4000)
   * @returns The ID of the toast
   */
  warning(message: string, duration: number = 4000): number {
    return this.show({
      id: this.getNextId(),
      message,
      type: 'warning',
      duration
    });
  }

  /**
   * Show a toast notification
   * @param toast The toast to show
   * @returns The ID of the toast
   */
  private show(toast: Toast): number {
    const currentToasts = this.toasts.getValue();
    this.toasts.next([...currentToasts, toast]);

    if (toast.duration) {
      setTimeout(() => this.remove(toast.id), toast.duration);
    }

    return toast.id;
  }

  /**
   * Remove a toast notification by ID
   * @param id The ID of the toast to remove
   */
  remove(id: number): void {
    const currentToasts = this.toasts.getValue();
    this.toasts.next(currentToasts.filter(toast => toast.id !== id));
  }

  /**
   * Clear all toast notifications
   */
  clear(): void {
    this.toasts.next([]);
  }

  /**
   * Get the next available toast ID
   * @returns The next available toast ID
   */
  private getNextId(): number {
    return this.nextId++;
  }
}
