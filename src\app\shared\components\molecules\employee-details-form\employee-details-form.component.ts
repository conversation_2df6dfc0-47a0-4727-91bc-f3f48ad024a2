import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

// Import atomic components
import { ButtonComponent } from '../../atoms/button/button.component';

// Import interfaces
import { EmployeeDetails, EmployeeDetailsInput } from '../../../../core/models/employee-extended.interface';

@Component({
  selector: 'app-employee-details-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonComponent
  ],
  templateUrl: './employee-details-form.component.html',
  styleUrls: ['./employee-details-form.component.scss']
})
export class EmployeeDetailsFormComponent implements OnInit, OnDestroy {
  @Input() employeeDetails: EmployeeDetails | null = null;
  @Input() employeeId: number | null = null;
  @Input() isLoading: boolean = false;
  @Input() isEdit: boolean = false;
  @Input() showAdvancedFields: boolean = true;

  @Output() formSubmit = new EventEmitter<EmployeeDetailsInput>();
  @Output() formCancel = new EventEmitter<void>();
  @Output() formReset = new EventEmitter<void>();

  private fb = inject(FormBuilder);
  private destroy$ = new Subject<void>();

  employeeDetailsForm!: FormGroup;
  activeSection: string = 'personal';

  // Form sections
  sections = [
    { id: 'personal', label: 'Personal Information', icon: '👤' },
    { id: 'emergency', label: 'Emergency Contact', icon: '🚨' },
    { id: 'documents', label: 'Government Documents', icon: '📄' },
    { id: 'banking', label: 'Banking Information', icon: '🏦' },
    { id: 'address', label: 'Address Information', icon: '🏠' },
    { id: 'additional', label: 'Additional Information', icon: '📝' }
  ];

  // Blood group options
  bloodGroups = [
    'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
  ];

  // Relationship options
  relationships = [
    'Spouse', 'Parent', 'Child', 'Sibling', 'Friend', 'Colleague', 'Other'
  ];

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormSubscriptions();

    if (this.employeeDetails && this.isEdit) {
      this.populateForm(this.employeeDetails);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.employeeDetailsForm = this.fb.group({
      // Employee ID (required)
      employee_id: [this.employeeId, Validators.required],

      // Personal Information
      emergency_contact_name: [''],
      emergency_contact_phone: ['', [Validators.pattern(/^[\+]?[1-9][\d]{0,15}$/)]],
      emergency_contact_relationship: [''],
      blood_group: [''],
      marital_status: [''],
      nationality: [''],

      // Government Documents
      passport_number: [''],
      passport_expiry_date: [''],
      visa_status: [''],
      visa_expiry_date: [''],
      work_permit_number: [''],
      work_permit_expiry_date: [''],
      social_security_number: [''],
      tax_id: [''],

      // Banking Information
      bank_account_number: [''],
      bank_name: [''],
      bank_routing_number: [''],
      bank_branch: [''],

      // Address Information
      permanent_address: [''],
      permanent_city: [''],
      permanent_state: [''],
      permanent_zip: ['', [Validators.pattern(/^\d{5}(-\d{4})?$/)]],
      permanent_country: [''],
      current_address: [''],
      current_city: [''],
      current_state: [''],
      current_zip: ['', [Validators.pattern(/^\d{5}(-\d{4})?$/)]],
      current_country: [''],

      // Additional Information
      notes: ['']
    });
  }

  private setupFormSubscriptions(): void {
    // Watch for form value changes
    this.employeeDetailsForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        // You can add real-time validation or other logic here
      });
  }

  private populateForm(employeeDetails: EmployeeDetails): void {
    this.employeeDetailsForm.patchValue({
      employee_id: employeeDetails.employee_id,
      emergency_contact_name: employeeDetails.emergency_contact_name || '',
      emergency_contact_phone: employeeDetails.emergency_contact_phone || '',
      emergency_contact_relationship: employeeDetails.emergency_contact_relationship || '',
      blood_group: employeeDetails.blood_group || '',
      marital_status: employeeDetails.marital_status || '',
      nationality: employeeDetails.nationality || '',
      passport_number: employeeDetails.passport_number || '',
      passport_expiry_date: employeeDetails.passport_expiry_date || '',
      visa_status: employeeDetails.visa_status || '',
      visa_expiry_date: employeeDetails.visa_expiry_date || '',
      work_permit_number: employeeDetails.work_permit_number || '',
      work_permit_expiry_date: employeeDetails.work_permit_expiry_date || '',
      social_security_number: employeeDetails.social_security_number || '',
      tax_id: employeeDetails.tax_id || '',
      bank_account_number: employeeDetails.bank_account_number || '',
      bank_name: employeeDetails.bank_name || '',
      bank_routing_number: employeeDetails.bank_routing_number || '',
      bank_branch: employeeDetails.bank_branch || '',
      permanent_address: employeeDetails.permanent_address || '',
      permanent_city: employeeDetails.permanent_city || '',
      permanent_state: employeeDetails.permanent_state || '',
      permanent_zip: employeeDetails.permanent_zip || '',
      permanent_country: employeeDetails.permanent_country || '',
      current_address: employeeDetails.current_address || '',
      current_city: employeeDetails.current_city || '',
      current_state: employeeDetails.current_state || '',
      current_zip: employeeDetails.current_zip || '',
      current_country: employeeDetails.current_country || '',
      notes: employeeDetails.notes || ''
    });
  }

  onSubmit(): void {
    if (this.employeeDetailsForm.valid) {
      const formData: EmployeeDetailsInput = this.employeeDetailsForm.value;
      this.formSubmit.emit(formData);
    } else {
      // Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.employeeDetailsForm);
    }
  }

  onCancel(): void {
    this.formCancel.emit();
  }

  onReset(): void {
    this.employeeDetailsForm.reset();
    this.employeeDetailsForm.patchValue({ employee_id: this.employeeId });
    this.formReset.emit();
  }

  setActiveSection(sectionId: string): void {
    this.activeSection = sectionId;
  }

  copyCurrentToPermanent(): void {
    const currentAddress = this.employeeDetailsForm.get('current_address')?.value;
    const currentCity = this.employeeDetailsForm.get('current_city')?.value;
    const currentState = this.employeeDetailsForm.get('current_state')?.value;
    const currentZip = this.employeeDetailsForm.get('current_zip')?.value;
    const currentCountry = this.employeeDetailsForm.get('current_country')?.value;

    this.employeeDetailsForm.patchValue({
      permanent_address: currentAddress,
      permanent_city: currentCity,
      permanent_state: currentState,
      permanent_zip: currentZip,
      permanent_country: currentCountry
    });
  }

  copyPermanentToCurrent(): void {
    const permanentAddress = this.employeeDetailsForm.get('permanent_address')?.value;
    const permanentCity = this.employeeDetailsForm.get('permanent_city')?.value;
    const permanentState = this.employeeDetailsForm.get('permanent_state')?.value;
    const permanentZip = this.employeeDetailsForm.get('permanent_zip')?.value;
    const permanentCountry = this.employeeDetailsForm.get('permanent_country')?.value;

    this.employeeDetailsForm.patchValue({
      current_address: permanentAddress,
      current_city: permanentCity,
      current_state: permanentState,
      current_zip: permanentZip,
      current_country: permanentCountry
    });
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  // Helper methods for form controls
  getFieldError(fieldName: string): string | null {
    const field = this.employeeDetailsForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${this.getFieldLabel(fieldName)} is required`;
      if (field.errors['pattern']) return `Please enter a valid ${this.getFieldLabel(fieldName).toLowerCase()}`;
    }
    return null;
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      'employee_id': 'Employee ID',
      'emergency_contact_name': 'Emergency Contact Name',
      'emergency_contact_phone': 'Emergency Contact Phone',
      'emergency_contact_relationship': 'Relationship',
      'blood_group': 'Blood Group',
      'marital_status': 'Marital Status',
      'nationality': 'Nationality',
      'passport_number': 'Passport Number',
      'passport_expiry_date': 'Passport Expiry Date',
      'visa_status': 'Visa Status',
      'visa_expiry_date': 'Visa Expiry Date',
      'work_permit_number': 'Work Permit Number',
      'work_permit_expiry_date': 'Work Permit Expiry Date',
      'social_security_number': 'Social Security Number',
      'tax_id': 'Tax ID',
      'bank_account_number': 'Bank Account Number',
      'bank_name': 'Bank Name',
      'bank_routing_number': 'Bank Routing Number',
      'bank_branch': 'Bank Branch',
      'permanent_zip': 'Permanent ZIP Code',
      'current_zip': 'Current ZIP Code'
    };
    return labels[fieldName] || fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get isFormValid(): boolean {
    return this.employeeDetailsForm.valid;
  }

  get hasUnsavedChanges(): boolean {
    return this.employeeDetailsForm.dirty;
  }

  get maxDate(): string {
    return new Date().toISOString().split('T')[0];
  }
}
