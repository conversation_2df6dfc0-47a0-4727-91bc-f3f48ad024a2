<div class="salary-form">
  <div class="form-header">
    <h3>{{ isEdit ? 'Edit Salary' : 'Add New Salary' }}</h3>
  </div>

  <form [formGroup]="salaryForm" (ngSubmit)="onSubmit()" class="form-content">
    <!-- Basic Salary -->
    <div class="form-group">
      <label for="basic_salary" class="form-label">
        Basic Salary <span class="required">*</span>
      </label>
      <div class="input-wrapper">
        <span class="currency-symbol">$</span>
        <input
          id="basic_salary"
          type="number"
          formControlName="basic_salary"
          class="form-input"
          [class.error]="isFieldInvalid('basic_salary')"
          placeholder="Enter basic salary"
          min="0"
          step="0.01">
      </div>
      @if (isFieldInvalid('basic_salary')) {
        <div class="error-message">
          {{ getFieldError('basic_salary') }}
        </div>
      }
    </div>

    <!-- Allowances -->
    <div class="form-group">
      <label for="allowances" class="form-label">Allowances</label>
      <div class="input-wrapper">
        <span class="currency-symbol">$</span>
        <input
          id="allowances"
          type="number"
          formControlName="allowances"
          class="form-input"
          [class.error]="isFieldInvalid('allowances')"
          placeholder="Enter allowances"
          min="0"
          step="0.01">
      </div>
      @if (isFieldInvalid('allowances')) {
        <div class="error-message">
          {{ getFieldError('allowances') }}
        </div>
      }
    </div>

    <!-- Deductions -->
    <div class="form-group">
      <label for="deductions" class="form-label">Deductions</label>
      <div class="input-wrapper">
        <span class="currency-symbol">$</span>
        <input
          id="deductions"
          type="number"
          formControlName="deductions"
          class="form-input"
          [class.error]="isFieldInvalid('deductions')"
          placeholder="Enter deductions"
          min="0"
          step="0.01">
      </div>
      @if (isFieldInvalid('deductions')) {
        <div class="error-message">
          {{ getFieldError('deductions') }}
        </div>
      }
    </div>

    <!-- Calculated Fields -->
    <div class="calculated-fields">
      <!-- Gross Salary -->
      <div class="form-group">
        <label for="gross_salary" class="form-label">Gross Salary</label>
        <div class="input-wrapper">
          <span class="currency-symbol">$</span>
          <input
            id="gross_salary"
            type="number"
            formControlName="gross_salary"
            class="form-input calculated"
            readonly
            placeholder="Calculated automatically">
        </div>
        <div class="field-note">
          Calculated as: Basic Salary + Allowances
        </div>
      </div>

      <!-- Net Salary -->
      <div class="form-group">
        <label for="net_salary" class="form-label">Net Salary</label>
        <div class="input-wrapper">
          <span class="currency-symbol">$</span>
          <input
            id="net_salary"
            type="number"
            formControlName="net_salary"
            class="form-input calculated"
            readonly
            placeholder="Calculated automatically">
        </div>
        <div class="field-note">
          Calculated as: Gross Salary - Deductions
        </div>
      </div>
    </div>

    <!-- Effective Date -->
    <div class="form-group">
      <label for="effective_date" class="form-label">
        Effective Date <span class="required">*</span>
      </label>
      <input
        id="effective_date"
        type="date"
        formControlName="effective_date"
        class="form-input"
        [class.error]="isFieldInvalid('effective_date')">
      @if (isFieldInvalid('effective_date')) {
        <div class="error-message">
          {{ getFieldError('effective_date') }}
        </div>
      }
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <app-button
        [variant]="'secondary'"
        [size]="'md'"
        [disabled]="isSubmitting"
        (click)="onCancel()">
        Cancel
      </app-button>

      <app-button
        [variant]="'primary'"
        [size]="'md'"
        [disabled]="salaryForm.invalid || isSubmitting"
        type="submit">
        {{ isEdit ? 'Update Salary' : 'Add Salary' }}
      </app-button>
    </div>
  </form>
</div>
