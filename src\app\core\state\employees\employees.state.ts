/**
 * NgRx Signals Employees State
 *
 * This file implements employees state management using NgRx Signals.
 */

import { patchState, signalStore, withComputed, withMethods, withState } from '@ngrx/signals';
import { computed, inject } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { catchError, finalize, map, of, pipe, switchMap, tap } from 'rxjs';
import { EmployeeService } from '../../services/employee.service';

// Employee interface
export interface Employee {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  position?: string;
  department?: string | { id: number; name: string };
  department_id?: number;
  department_name?: string; // Added for dynamic columns
  hire_date?: string;
  start_date?: string; // Added for dynamic columns
  salary?: number;
  status?: 'active' | 'inactive' | 'on_leave';
  manager_id?: number;
  company_id: number;
  avatar?: string;
  profile_picture_url?: string; // Added for dynamic columns
  full_name?: string; // Added for dynamic columns
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  date_of_birth?: string; // Added for employee form
  gender?: string; // Added for employee form
  designation?: { id: number; name: string }; // Added for employee form
  created_at: string;
  updated_at: string;
}

// Employee creation/update interface
export interface EmployeeInput {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string; // Made optional to match Employee interface
  position?: string; // Made optional
  department_id?: number; // Made optional
  hire_date?: string; // Made optional
  // Optional fields
  salary?: number;
  status?: 'active' | 'inactive' | 'on_leave';
  manager_id?: number;
  company_id?: number;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  avatar?: File; // Added avatar file for proper file upload
  profile_picture?: string; // Added for profile picture URL
  date_of_birth?: string; // Added for date of birth
  gender?: string; // Added for gender
  designation_id?: number; // Added for designation
}

// Define the paginated response interface
export interface PaginatedEmployeesResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Employee[];
}

// Define the employees state interface
export interface EmployeesState {
  employees: Employee[];
  selectedEmployee: Employee | null;
  isLoading: boolean;
  error: string | null;
  // Pagination state
  page: number;
  pageSize: number;
  totalCount: number;
  // Search state
  searchTerm: string;
  // Department filter state
  departmentIds: (string | number)[];
}

// Define the initial state
export const initialEmployeesState: EmployeesState = {
  employees: [],
  selectedEmployee: null,
  isLoading: false,
  error: null,
  // Pagination state
  page: 1,
  pageSize: 10,
  totalCount: 0,
  // Search state
  searchTerm: '',
  // Department filter state
  departmentIds: [''] // Default to "All Departments"
};

// Create the employees store
export const EmployeesStore = signalStore(
  { providedIn: 'root' },
  withState(initialEmployeesState),
  withComputed((state) => ({
    // Computed properties
    employeesCount: computed(() => state.totalCount()),
    hasEmployees: computed(() => state.employees().length > 0),
    fullNames: computed(() => {
      return state.employees().map(employee =>
        `${employee.first_name} ${employee.last_name}`
      );
    }),
    activeEmployees: computed(() => {
      return state.employees().filter(employee =>
        employee.status === 'active'
      );
    }),
    activeEmployeesCount: computed(() => {
      return state.employees().filter(employee =>
        employee.status === 'active'
      ).length;
    }),
    onLeaveEmployeesCount: computed(() => {
      return state.employees().filter(employee =>
        employee.status === 'on_leave'
      ).length;
    }),
    departmentCounts: computed(() => {
      const counts: Record<string, number> = {};
      state.employees().forEach(employee => {
        if (employee.department) {
          const deptName = typeof employee.department === 'string'
            ? employee.department
            : employee.department.name;
          counts[deptName] = (counts[deptName] || 0) + 1;
        }
      });
      return counts;
    }),
    // Pagination computed properties
    totalPages: computed(() => Math.ceil(state.totalCount() / state.pageSize())),
    hasNextPage: computed(() => state.page() < Math.ceil(state.totalCount() / state.pageSize())),
    hasPreviousPage: computed(() => state.page() > 1)
  })),
  withMethods((store, employeeService = inject(EmployeeService)) => ({
    // Load employees with pagination, search, and department filtering
    loadEmployees: rxMethod<{ page?: number; pageSize?: number; searchTerm?: string; departmentIds?: (string | number)[] }>(
      pipe(
        tap(({ page, pageSize, searchTerm, departmentIds }) => {
          // Update pagination, search, and department filter state
          if (page !== undefined) {
            patchState(store, { page });
          }
          if (pageSize !== undefined) {
            patchState(store, { pageSize });
          }
          if (searchTerm !== undefined) {
            patchState(store, { searchTerm });
          }
          if (departmentIds !== undefined) {
            patchState(store, { departmentIds });
          }

          patchState(store, { isLoading: true, error: null });
        }),
        switchMap(() => {
          const currentPage = store.page();
          const currentPageSize = store.pageSize();
          const currentSearchTerm = store.searchTerm();
          const currentDepartmentIds = store.departmentIds();

          return employeeService.getEmployees(currentPage, currentPageSize, currentSearchTerm, currentDepartmentIds).pipe(
            map((response) => {
              patchState(store, {
                employees: response.results,
                totalCount: response.count,
                isLoading: false,
                error: null
              });
              return response;
            }),
            catchError((error: HttpErrorResponse) => {
              console.error('Error loading employees:', error);
              const errorMessage = error.error?.detail || error.error?.message || 'Failed to load employees';
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          );
        })
      )
    ),

    // Load employees by company ID
    loadEmployeesByCompany: rxMethod<number>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((companyId) =>
          employeeService.getEmployeesByCompany(companyId).pipe(
            map((employees) => {
              patchState(store, {
                employees,
                isLoading: false,
                error: null
              });
              return employees;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to load employees for company ${companyId}`;
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of([]);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Load a single employee by ID
    loadEmployee: rxMethod<number>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((id) =>
          employeeService.getEmployeeById(id).pipe(
            map((employee) => {
              patchState(store, {
                selectedEmployee: employee,
                isLoading: false,
                error: null
              });
              return employee;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to load employee with ID ${id}`;
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Create a new employee
    createEmployee: rxMethod<EmployeeInput>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((employeeData) =>
          employeeService.createEmployee(employeeData).pipe(
            map((newEmployee) => {
              const currentEmployees = store.employees();
              patchState(store, {
                employees: [...currentEmployees, newEmployee],
                isLoading: false,
                error: null
              });
              return newEmployee;
            }),
            catchError((error: HttpErrorResponse) => {
              console.error('Error creating employee:', error);
              const errorMessage = error.error?.detail || error.error?.message || 'Failed to create employee';
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Update an existing employee
    updateEmployee: rxMethod<{ id: number; data: EmployeeInput }>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap(({ id, data }) =>
          employeeService.updateEmployee(id, data).pipe(
            map((updatedEmployee) => {
              const currentEmployees = store.employees();
              const updatedEmployees = currentEmployees.map(employee =>
                employee.id === id ? updatedEmployee : employee
              );

              patchState(store, {
                employees: updatedEmployees,
                selectedEmployee: updatedEmployee,
                isLoading: false,
                error: null
              });
              return updatedEmployee;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to update employee with ID ${id}`;
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Delete an employee
    deleteEmployee: rxMethod<number>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((id) =>
          employeeService.deleteEmployee(id).pipe(
            map(() => {
              const currentEmployees = store.employees();
              const filteredEmployees = currentEmployees.filter(employee => employee.id !== id);

              patchState(store, {
                employees: filteredEmployees,
                selectedEmployee: null,
                isLoading: false,
                error: null
              });
              return true;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to delete employee with ID ${id}`;
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(false);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Select an employee
    selectEmployee(employee: Employee | null): void {
      patchState(store, { selectedEmployee: employee });
    },

    // Clear error
    clearError(): void {
      patchState(store, { error: null });
    },

    // Set page
    setPage(page: number): void {
      patchState(store, { page });
      this.loadEmployees({});
    },

    // Set page size
    setPageSize(pageSize: number): void {
      patchState(store, { pageSize, page: 1 }); // Reset to first page when changing page size
      this.loadEmployees({});
    },

    // Set search term
    setSearchTerm(searchTerm: string): void {
      patchState(store, { searchTerm, page: 1 }); // Reset to first page when searching
      this.loadEmployees({});
    },

    // Set department filter
    setDepartmentFilter(departmentIds: (string | number)[]): void {
      patchState(store, { departmentIds, page: 1 }); // Reset to first page when filtering
      this.loadEmployees({});
    },

    // Reset search
    resetSearch(): void {
      patchState(store, { searchTerm: '', page: 1 });
      this.loadEmployees({});
    },

    // Reset filters
    resetFilters(): void {
      patchState(store, { searchTerm: '', departmentIds: [''], page: 1 });
      this.loadEmployees({});
    }
  }))
);
