@use 'sass:color';
@import '../../../styles/variables/_colors';

.profile-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

// Profile Header
.profile-header {
  position: relative;
  margin-bottom: 2rem;
  border-radius: 8px;
  overflow: hidden;
  background-color: $white;
  box-shadow: 0 2px 10px rgba($black, 0.05);
}

.profile-cover {
  height: 200px;
  background: linear-gradient(135deg, $primary, color.adjust($primary, $lightness: 20%));
}

.profile-info {
  display: flex;
  align-items: flex-end;
  padding: 0 2rem 1.5rem;
  position: relative;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

.profile-avatar {
  position: relative;
  margin-top: -50px;

  img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid $white;
    background-color: $white;
    object-fit: cover;
  }
}

.profile-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: $primary;
  color: $white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: $font-weight-semibold;
}

.profile-details {
  margin-left: 1.5rem;
  flex: 1;

  @media (max-width: 768px) {
    margin-left: 0;
    margin-top: 1rem;
  }
}

.profile-name {
  font-size: 1.5rem;
  font-weight: $font-weight-bold;
  color: $gray-900;
  margin: 0 0 0.25rem;
}

.profile-email {
  color: $gray-600;
  margin: 0;
}

.profile-actions {
  display: flex;
  gap: 0.75rem;

  @media (max-width: 768px) {
    margin-top: 1rem;
  }
}

// Buttons
.btn-edit, .btn-save, .btn-cancel {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: $font-weight-medium;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  i {
    margin-right: 0.5rem;
  }
}

.btn-edit {
  background-color: $primary;
  color: $white;

  &:hover {
    background-color: color.adjust($primary, $lightness: -10%);
  }
}

.btn-save {
  background-color: $success;
  color: $white;

  &:hover:not(:disabled) {
    background-color: color.adjust($success, $lightness: -10%);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.btn-cancel {
  background-color: $gray-200;
  color: $gray-700;

  &:hover {
    background-color: $gray-300;
  }
}

// Tabs
.profile-tabs {
  margin-bottom: 1.5rem;
}

.tabs-list {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  border-bottom: 1px solid $gray-200;

  @media (max-width: 576px) {
    flex-wrap: wrap;
  }
}

.tab-item {
  margin-right: 1rem;

  &.active {
    .tab-link {
      color: $primary;
      border-bottom-color: $primary;
    }
  }

  @media (max-width: 576px) {
    margin-bottom: 0.5rem;
  }
}

.tab-link {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 0;
  color: $gray-600;
  border-bottom: 2px solid transparent;
  background: none;
  border-top: none;
  border-left: none;
  border-right: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: $font-weight-medium;

  i {
    margin-right: 0.5rem;
  }

  &:hover {
    color: $primary;
  }
}

// Profile Content
.profile-content {
  background-color: $white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba($black, 0.05);
  padding: 2rem;
}

// Form
.profile-form {
  max-width: 800px;
}

.form-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
}

.form-group {
  flex: 1;
  margin-bottom: 1.5rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }

  input, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid $gray-300;
    border-radius: 4px;
    font-size: 1rem;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba($primary, 0.1);
    }

    &.readonly {
      background-color: $gray-100;
      cursor: default;
    }
  }

  textarea {
    resize: vertical;
  }
}

.form-error {
  color: $danger;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

// Tab Placeholder
.tab-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;

  .placeholder-icon {
    font-size: 3rem;
    color: $gray-300;
    margin-bottom: 1rem;
  }

  h3 {
    font-size: 1.25rem;
    font-weight: $font-weight-semibold;
    color: $gray-800;
    margin: 0 0 0.5rem;
  }

  p {
    color: $gray-600;
    margin: 0;
    max-width: 400px;
  }
}


