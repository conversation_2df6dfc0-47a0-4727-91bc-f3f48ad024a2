/**
 * Theme configuration interfaces for the application
 */

export type ThemeMode = 'light' | 'dark' | 'auto';

export type HeaderPosition = 'fixed' | 'static' | 'sticky';

export interface ThemeConfig {
  mode: ThemeMode;
  primaryColor?: string;
  accentColor?: string;
  customColors?: Record<string, string>;
}

export interface LayoutConfig {
  headerPosition: HeaderPosition;
  sidebarCollapsed: boolean;
  sidebarPosition: 'left' | 'right';
  layoutType: 'vertical' | 'horizontal';
}

export interface UserPreferences {
  theme: ThemeConfig;
  layout: LayoutConfig;
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    desktop: boolean;
  };
}

export interface ThemeColors {
  // Primary colors
  primary: string;
  primaryLight: string;
  primaryDark: string;
  
  // Secondary colors
  secondary: string;
  secondaryLight: string;
  secondaryDark: string;
  
  // Neutral colors
  background: string;
  surface: string;
  surfaceVariant: string;
  
  // Text colors
  onBackground: string;
  onSurface: string;
  onPrimary: string;
  onSecondary: string;
  
  // Status colors
  success: string;
  warning: string;
  error: string;
  info: string;
  
  // Border and divider colors
  border: string;
  divider: string;
  
  // Input colors
  inputBackground: string;
  inputBorder: string;
  inputFocus: string;
  
  // Card colors
  cardBackground: string;
  cardBorder: string;
  cardShadow: string;
}

export const LIGHT_THEME_COLORS: ThemeColors = {
  // Primary colors
  primary: '#3b82f6',
  primaryLight: '#60a5fa',
  primaryDark: '#2563eb',
  
  // Secondary colors
  secondary: '#64748b',
  secondaryLight: '#94a3b8',
  secondaryDark: '#475569',
  
  // Neutral colors
  background: '#ffffff',
  surface: '#f8fafc',
  surfaceVariant: '#f1f5f9',
  
  // Text colors
  onBackground: '#1e293b',
  onSurface: '#334155',
  onPrimary: '#ffffff',
  onSecondary: '#ffffff',
  
  // Status colors
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#06b6d4',
  
  // Border and divider colors
  border: '#e2e8f0',
  divider: '#f1f5f9',
  
  // Input colors
  inputBackground: '#ffffff',
  inputBorder: '#d1d5db',
  inputFocus: '#3b82f6',
  
  // Card colors
  cardBackground: '#ffffff',
  cardBorder: '#e5e7eb',
  cardShadow: 'rgba(0, 0, 0, 0.1)'
};

export const DARK_THEME_COLORS: ThemeColors = {
  // Primary colors
  primary: '#60a5fa',
  primaryLight: '#93c5fd',
  primaryDark: '#3b82f6',
  
  // Secondary colors
  secondary: '#94a3b8',
  secondaryLight: '#cbd5e1',
  secondaryDark: '#64748b',
  
  // Neutral colors
  background: '#0f172a',
  surface: '#1e293b',
  surfaceVariant: '#334155',
  
  // Text colors
  onBackground: '#f8fafc',
  onSurface: '#e2e8f0',
  onPrimary: '#1e293b',
  onSecondary: '#1e293b',
  
  // Status colors
  success: '#34d399',
  warning: '#fbbf24',
  error: '#f87171',
  info: '#22d3ee',
  
  // Border and divider colors
  border: '#475569',
  divider: '#334155',
  
  // Input colors
  inputBackground: '#1e293b',
  inputBorder: '#475569',
  inputFocus: '#60a5fa',
  
  // Card colors
  cardBackground: '#1e293b',
  cardBorder: '#475569',
  cardShadow: 'rgba(0, 0, 0, 0.3)'
};

export const DEFAULT_THEME_CONFIG: ThemeConfig = {
  mode: 'light'
};

export const DEFAULT_LAYOUT_CONFIG: LayoutConfig = {
  headerPosition: 'fixed',
  sidebarCollapsed: false,
  sidebarPosition: 'left',
  layoutType: 'vertical'
};

export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: DEFAULT_THEME_CONFIG,
  layout: DEFAULT_LAYOUT_CONFIG,
  language: 'en',
  timezone: 'UTC',
  notifications: {
    email: true,
    push: true,
    desktop: false
  }
};
