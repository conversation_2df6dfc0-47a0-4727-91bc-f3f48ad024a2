<form [formGroup]="employeeForm" (ngSubmit)="onSubmit()" class="employee-form">
  <div class="employee-form__content">
    
    <!-- Basic Information Section -->
    <div class="employee-form__section">
      <h3 class="employee-form__section-title">Basic Information</h3>
      
      <div class="employee-form__grid employee-form__grid--2-col">
        <!-- First Name -->
        <div class="employee-form__field">
          <label for="first_name" class="employee-form__label">
            First Name <span class="employee-form__required">*</span>
          </label>
          <input
            id="first_name"
            type="text"
            class="employee-form__input"
            [class.employee-form__input--error]="getFieldError('first_name')"
            formControlName="first_name"
            placeholder="Enter first name"
            [disabled]="isLoading">
          <div class="employee-form__error" *ngIf="getFieldError('first_name')">
            {{ getFieldError('first_name') }}
          </div>
        </div>

        <!-- Last Name -->
        <div class="employee-form__field">
          <label for="last_name" class="employee-form__label">
            Last Name <span class="employee-form__required">*</span>
          </label>
          <input
            id="last_name"
            type="text"
            class="employee-form__input"
            [class.employee-form__input--error]="getFieldError('last_name')"
            formControlName="last_name"
            placeholder="Enter last name"
            [disabled]="isLoading">
          <div class="employee-form__error" *ngIf="getFieldError('last_name')">
            {{ getFieldError('last_name') }}
          </div>
        </div>

        <!-- Email -->
        <div class="employee-form__field">
          <label for="email" class="employee-form__label">
            Email <span class="employee-form__required">*</span>
          </label>
          <input
            id="email"
            type="email"
            class="employee-form__input"
            [class.employee-form__input--error]="getFieldError('email')"
            formControlName="email"
            placeholder="Enter email address"
            [disabled]="isLoading">
          <div class="employee-form__error" *ngIf="getFieldError('email')">
            {{ getFieldError('email') }}
          </div>
        </div>

        <!-- Phone -->
        <div class="employee-form__field">
          <label for="phone" class="employee-form__label">Phone Number</label>
          <input
            id="phone"
            type="tel"
            class="employee-form__input"
            [class.employee-form__input--error]="getFieldError('phone')"
            formControlName="phone"
            placeholder="Enter phone number"
            [disabled]="isLoading">
          <div class="employee-form__error" *ngIf="getFieldError('phone')">
            {{ getFieldError('phone') }}
          </div>
        </div>
      </div>
    </div>

    <!-- Personal Information Section -->
    <div class="employee-form__section" *ngIf="showAdvancedFields">
      <h3 class="employee-form__section-title">Personal Information</h3>
      
      <div class="employee-form__grid employee-form__grid--2-col">
        <!-- Date of Birth -->
        <div class="employee-form__field">
          <app-date-input
            formControlName="date_of_birth"
            label="Date of Birth"
            placeholder="Select date of birth"
            [disabled]="isLoading"
            [error]="getFieldError('date_of_birth')"
            [min]="minBirthDate"
            [max]="maxBirthDate"
            [showAge]="true">
          </app-date-input>
        </div>

        <!-- Gender -->
        <div class="employee-form__field">
          <label for="gender" class="employee-form__label">Gender</label>
          <select
            id="gender"
            class="employee-form__select"
            formControlName="gender"
            [disabled]="isLoading">
            <option value="">Select gender</option>
            <option *ngFor="let option of genderOptions" [value]="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Work Information Section -->
    <div class="employee-form__section">
      <h3 class="employee-form__section-title">Work Information</h3>
      
      <div class="employee-form__grid employee-form__grid--2-col">
        <!-- Department -->
        <div class="employee-form__field">
          <label for="department_id" class="employee-form__label">
            Department <span class="employee-form__required">*</span>
          </label>
          <select
            id="department_id"
            class="employee-form__select"
            [class.employee-form__select--error]="getFieldError('department_id')"
            formControlName="department_id"
            [disabled]="isLoading">
            <option value="">Select department</option>
            <option *ngFor="let department of departments" [value]="department.id">
              {{ department.name }}
            </option>
          </select>
          <div class="employee-form__error" *ngIf="getFieldError('department_id')">
            {{ getFieldError('department_id') }}
          </div>
        </div>

        <!-- Designation -->
        <div class="employee-form__field">
          <label for="designation_id" class="employee-form__label">Designation</label>
          <select
            id="designation_id"
            class="employee-form__select"
            formControlName="designation_id"
            [disabled]="isLoading">
            <option value="">Select designation</option>
            <option *ngFor="let designation of designations" [value]="designation.id">
              {{ designation.name }}
            </option>
          </select>
        </div>

        <!-- Status -->
        <div class="employee-form__field">
          <label for="status" class="employee-form__label">
            Status <span class="employee-form__required">*</span>
          </label>
          <select
            id="status"
            class="employee-form__select"
            [class.employee-form__select--error]="getFieldError('status')"
            formControlName="status"
            [disabled]="isLoading">
            <option *ngFor="let option of statusOptions" [value]="option.value">
              {{ option.label }}
            </option>
          </select>
          <div class="employee-form__error" *ngIf="getFieldError('status')">
            {{ getFieldError('status') }}
          </div>
        </div>
      </div>
    </div>

    <!-- Profile Picture Section -->
    <div class="employee-form__section" *ngIf="showAdvancedFields">
      <h3 class="employee-form__section-title">Profile Picture</h3>
      
      <div class="employee-form__field">
        <label for="profile_picture" class="employee-form__label">Profile Picture URL</label>
        <input
          id="profile_picture"
          type="url"
          class="employee-form__input"
          formControlName="profile_picture"
          placeholder="Enter profile picture URL"
          [disabled]="isLoading">
      </div>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="employee-form__actions">
    <app-button
      type="button"
      variant="secondary"
      [disabled]="isLoading"
      (click)="onCancel()">
      Cancel
    </app-button>

    <app-button
      type="submit"
      variant="primary"
      [disabled]="!isFormValid || isLoading">
      {{ isEdit ? 'Update Employee' : 'Create Employee' }}
    </app-button>
  </div>
</form>
