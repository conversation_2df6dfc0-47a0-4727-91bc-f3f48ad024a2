@import '../../../../../styles/variables/colors';

.employee-form {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  &__content {
    background: $white;
    border-radius: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }

  &__section {
    padding: 2rem;
    border-bottom: 1px solid $gray-200;

    &:last-child {
      border-bottom: none;
    }
  }

  &__section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: $gray-900;
    margin: 0 0 1.5rem 0;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid $gray-100;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &::before {
      content: '';
      width: 4px;
      height: 1.25rem;
      background: $primary;
      border-radius: 2px;
    }
  }

  &__grid {
    display: grid;
    gap: 1.5rem;

    &--2-col {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
  }

  &__field {
    display: flex;
    flex-direction: column;
  }

  &__label {
    display: block;
    font-weight: 500;
    color: $gray-700;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  &__required {
    color: $danger;
    margin-left: 0.25rem;
  }

  &__input,
  &__select {
    width: 100%;
    padding: 0.75rem 1rem;
    background: $white;
    border: 1px solid $gray-300;
    border-radius: 12px;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: $gray-900;
    transition: all 0.2s ease;
    min-height: 2.75rem;

    &::placeholder {
      color: $gray-500;
    }

    &:hover:not(:disabled) {
      border-color: $gray-400;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.1);
    }

    &:disabled {
      background-color: $gray-100;
      color: $gray-500;
      cursor: not-allowed;
    }

    &--error {
      border-color: $danger;
      box-shadow: 0 0 0 3px rgba($danger, 0.1);

      &:focus {
        border-color: $danger;
        box-shadow: 0 0 0 3px rgba($danger, 0.1);
      }
    }
  }

  &__error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    color: $danger;
    font-size: 0.75rem;
    line-height: 1rem;

    &::before {
      content: '⚠';
      font-size: 0.875rem;
    }
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem 2rem;
    background: $gray-50;
    border-top: 1px solid $gray-200;
  }

  // Responsive design
  @media (max-width: 768px) {
    &__grid {
      &--2-col {
        grid-template-columns: 1fr;
      }
    }

    &__actions {
      flex-direction: column-reverse;
      gap: 0.75rem;

      app-button {
        width: 100%;
      }
    }

    &__section {
      padding: 1.5rem;
    }
  }

  @media (max-width: 480px) {
    &__section {
      padding: 1rem;
    }

    &__section-title {
      font-size: 1.125rem;
      margin-bottom: 1rem;
    }

    &__actions {
      padding: 1rem;
    }
  }
}
