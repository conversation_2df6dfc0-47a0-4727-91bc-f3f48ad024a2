import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';

// Import organisms
import { EmployeesListComponent } from '../../shared/components/organisms/employees-list/employees-list.component';

// Import stores and services
import { EmployeesStore } from '../../core/state/employees/employees.state';
import { DepartmentsStore } from '../../core/state/departments/departments.state';

@Component({
  selector: 'app-employees',
  standalone: true,
  imports: [
    CommonModule,
    EmployeesListComponent
  ],
  template: `
    <div class="employees-page">
      <div class="employees-page__header">
        <h1 class="employees-page__title">Employee Management</h1>
        <p class="employees-page__subtitle">Manage your organization's employees</p>
      </div>

      <div class="employees-page__content">
        <app-employees-list></app-employees-list>
      </div>
    </div>
  `,
  styleUrl: './employees.component.scss'
})
export class EmployeesComponent implements OnInit {
  private employeesStore = inject(EmployeesStore);
  private departmentsStore = inject(DepartmentsStore);

  ngOnInit(): void {
    // Load initial data
    this.employeesStore.loadEmployees({});
    this.departmentsStore.loadAllDepartments();
  }
}
