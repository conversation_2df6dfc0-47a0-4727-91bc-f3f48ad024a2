@use 'sass:color';
@import '../../../../../styles/variables/_colors';

.employee-documents-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid rgba(102, 126, 234, 0.1);

  // Section Header
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px;
    border-bottom: 1px solid #f1f3f4;
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(240, 147, 251, 0.05));

    .header-content {
      flex: 1;

      .section-title {
        margin: 0 0 8px 0;
        font-size: 20px;
        font-weight: 600;
        color: #202124;
        display: flex;
        align-items: center;
        gap: 12px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        i {
          font-size: 24px;
          background: linear-gradient(135deg, #667eea, #764ba2);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .section-description {
        margin: 0;
        color: #5f6368;
        font-size: 14px;
      }
    }

    .add-document-btn {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }

  // Loading State
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 80px 20px;

    .loading-spinner {
      text-align: center;
      color: #5f6368;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
        color: #667eea;
      }

      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }

  // Empty State
  .empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #5f6368;

    .empty-icon {
      margin-bottom: 24px;

      i {
        font-size: 64px;
        opacity: 0.3;
        color: #667eea;
      }
    }

    h4 {
      margin: 0 0 12px 0;
      font-size: 20px;
      font-weight: 600;
      color: #202124;
    }

    p {
      margin: 0 0 24px 0;
      font-size: 16px;
      color: #5f6368;
    }

    .btn {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }
  }

  // Documents Table
  .documents-table-container {
    padding: 0;

    .table-wrapper {
      overflow-x: auto;
    }

    .documents-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;

      thead {
        background: #f8f9fa;
        border-bottom: 2px solid #e8eaed;

        th {
          padding: 16px 20px;
          text-align: left;
          font-weight: 600;
          color: #202124;
          font-size: 13px;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.actions-column {
            text-align: center;
            width: 160px;
          }
        }
      }

      tbody {
        .document-row {
          border-bottom: 1px solid #f1f3f4;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(102, 126, 234, 0.05);
          }

          td {
            padding: 16px 20px;
            vertical-align: middle;

            &.document-name {
              .document-info {
                display: flex;
                align-items: center;
                gap: 12px;

                .document-icon {
                  color: #667eea;
                  font-size: 16px;
                }

                .name {
                  font-weight: 500;
                  color: #202124;
                }
              }
            }

            &.document-type {
              .type-badge {
                display: inline-block;
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 600;
                text-transform: capitalize;

                &.type-resume {
                  background: linear-gradient(135deg, #667eea, #764ba2);
                  color: white;
                }

                &.type-id_proof {
                  background: linear-gradient(135deg, #43e97b, #38f9d7);
                  color: white;
                }

                &.type-address_proof {
                  background: linear-gradient(135deg, #fa709a, #fee140);
                  color: white;
                }

                &.type-education_certificate {
                  background: linear-gradient(135deg, #a8edea, #fed6e3);
                  color: #333;
                }

                &.type-experience_letter {
                  background: linear-gradient(135deg, #d299c2, #fef9d3);
                  color: #333;
                }

                &.type-offer_letter {
                  background: linear-gradient(135deg, #89f7fe, #66a6ff);
                  color: white;
                }

                &.type-contract {
                  background: linear-gradient(135deg, #fdbb2d, #22c1c3);
                  color: white;
                }

                &.type-other {
                  background: linear-gradient(135deg, #e0e0e0, #f5f5f5);
                  color: #333;
                }
              }
            }

            &.file-size {
              color: #5f6368;
              font-size: 13px;
            }

            &.upload-date {
              color: #5f6368;
              font-size: 13px;
            }

            &.actions {
              text-align: center;

              .action-buttons {
                display: flex;
                justify-content: center;
                gap: 8px;
              }
            }
          }
        }
      }
    }
  }

  // Action Buttons
  .btn-action {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 14px;

    &.view {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }

    &.download {
      background: linear-gradient(135deg, #43e97b, #38f9d7);
      color: white;
      box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(67, 233, 123, 0.4);
      }
    }

    &.edit {
      background: linear-gradient(135deg, #a8edea, #fed6e3);
      color: #333;
      box-shadow: 0 2px 8px rgba(168, 237, 234, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(168, 237, 234, 0.4);
      }
    }

    // Disabled state for action buttons
    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      pointer-events: none;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }

    &.delete {
      background: linear-gradient(135deg, #fa709a, #fee140);
      color: white;
      box-shadow: 0 2px 8px rgba(250, 112, 154, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(250, 112, 154, 0.4);
      }
    }
  }

  // Modal Styles
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;

    .modal-container {
      background: white;
      border-radius: 16px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      width: 100%;
      max-width: 600px;
      max-height: 90vh;
      overflow: hidden;
      animation: modalSlideIn 0.3s ease-out;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px;
        border-bottom: 1px solid #f1f3f4;
        background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(240, 147, 251, 0.05));

        .modal-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #202124;
          display: flex;
          align-items: center;
          gap: 12px;

          i {
            color: #667eea;
          }
        }

        .modal-close {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          border: none;
          background: #f8f9fa;
          color: #5f6368;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &:hover {
            background: #e8eaed;
            color: #202124;
          }
        }
      }

      .modal-body {
        padding: 24px;
        max-height: calc(90vh - 140px);
        overflow-y: auto;

        .form-group {
          margin-bottom: 24px;

          .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #202124;
            font-size: 14px;

            .required {
              color: #ea4335;
              margin-left: 4px;
            }

            .optional {
              color: #5f6368;
              font-weight: 400;
              font-size: 12px;
              margin-left: 4px;
            }
          }

          .form-input,
          .form-select,
          .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e8eaed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: white;

            &:focus {
              outline: none;
              border-color: #667eea;
              box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }

            &.error {
              border-color: #ea4335;
              box-shadow: 0 0 0 3px rgba(234, 67, 53, 0.1);
            }

            &::placeholder {
              color: #9aa0a6;
            }

            &.form-control[rows] {
              resize: vertical;
              min-height: 80px;
              max-height: 200px;
              font-family: inherit;
            }
          }

          .error-message {
            display: block;
            margin-top: 6px;
            color: #ea4335;
            font-size: 12px;
          }

          // File Upload Styles
          .file-upload-container {
            .file-input {
              display: none;
            }

            .file-upload-area {
              border: 2px dashed #e8eaed;
              border-radius: 8px;
              padding: 24px;
              text-align: center;
              cursor: pointer;
              transition: all 0.2s ease;
              background: #fafbfc;

              &:hover {
                border-color: #667eea;
                background: rgba(102, 126, 234, 0.05);
              }

              &.has-file {
                border-color: #34a853;
                background: rgba(52, 168, 83, 0.05);
              }

              &.drag-over {
                border-color: #667eea;
                background: rgba(102, 126, 234, 0.1);
                transform: scale(1.02);
              }

              .selected-file {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
                color: #34a853;
                position: relative;

                i {
                  font-size: 24px;
                }

                .file-name {
                  font-weight: 500;
                  color: #202124;
                }

                .file-size {
                  color: #5f6368;
                  font-size: 12px;
                }

                .remove-file-btn {
                  position: absolute;
                  top: -8px;
                  right: -8px;
                  width: 24px;
                  height: 24px;
                  border-radius: 50%;
                  border: none;
                  background: #ea4335;
                  color: white;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 12px;
                  transition: all 0.2s ease;
                  box-shadow: 0 2px 4px rgba(234, 67, 53, 0.3);

                  &:hover {
                    background: color.adjust(#ea4335, $lightness: -10%);
                    transform: scale(1.1);
                  }
                }
              }

              .upload-placeholder {
                color: #5f6368;

                i {
                  font-size: 48px;
                  margin-bottom: 16px;
                  display: block;
                  color: #667eea;
                  opacity: 0.7;
                }

                .upload-text {
                  display: block;
                  font-weight: 500;
                  margin-bottom: 8px;
                  color: #202124;
                }

                .upload-hint {
                  font-size: 12px;
                  color: #5f6368;
                }
              }
            }
          }
        }

        .modal-actions {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          margin-top: 32px;
          padding-top: 24px;
          border-top: 1px solid #f1f3f4;

          .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;

            &.btn-secondary {
              background: #f8f9fa;
              color: #5f6368;
              border: 1px solid #e8eaed;

              &:hover {
                background: #e8eaed;
                color: #202124;
              }
            }

            &.btn-primary {
              background: linear-gradient(135deg, #667eea, #764ba2);
              color: white;
              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

              &:hover:not(:disabled) {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
              }

              &:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
              }
            }
          }
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .section-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .add-document-btn {
        justify-content: center;
      }
    }

    .documents-table-container {
      .documents-table {
        font-size: 12px;

        thead th,
        tbody td {
          padding: 12px 16px;
        }

        .action-buttons {
          flex-wrap: wrap;
          gap: 4px;
        }

        .btn-action {
          width: 28px;
          height: 28px;
          font-size: 12px;
        }
      }
    }

    .modal-overlay {
      padding: 10px;

      .modal-container {
        max-height: 95vh;

        .modal-body {
          padding: 16px;
          max-height: calc(95vh - 120px);
        }
      }
    }
  }
}

// Animation
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
