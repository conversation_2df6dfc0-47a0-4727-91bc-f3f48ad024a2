import { Component, ElementRef, EventEmitter, Input, Output, ViewChild, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'app-file-upload',
  standalone: true,
  imports: [CommonModule, FormsModule, IconComponent],
  templateUrl: './file-upload.component.html',
  styleUrl: './file-upload.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FileUploadComponent),
      multi: true
    }
  ]
})
export class FileUploadComponent implements ControlValueAccessor {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  @Input() accept: string = 'image/*';
  @Input() placeholder: string = 'Upload Avatar';
  @Input() acceptedFileTypes: string = 'JPG, PNG, GIF (max 5MB)';

  @Output() fileSelected = new EventEmitter<File>();
  @Output() fileRemoved = new EventEmitter<void>();

  previewUrl: string | null = null;
  isDragging: boolean = false;

  // Value accessor properties and methods
  private _value: File | null = null;
  private _onChange: (value: File | null) => void = () => {};
  private _onTouched: () => void = () => {};
  private _disabled: boolean = false;

  // ControlValueAccessor interface methods
  writeValue(value: File | null): void {
    this._value = value;

    if (value) {
      // Create preview for the file
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        this.previewUrl = e.target?.result as string;
      };
      reader.readAsDataURL(value);
    } else {
      this.previewUrl = null;
    }
  }

  registerOnChange(fn: (value: File | null) => void): void {
    this._onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this._onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this._disabled = isDisabled;
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;

    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];
      this.handleFile(file);
    }
  }

  triggerFileInput(event: MouseEvent): void {
    event.preventDefault();
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      this.handleFile(file);
    }
  }

  handleFile(file: File): void {
    // Check file type
    if (!file.type.startsWith('image/')) {
      console.error('File must be an image');
      return;
    }

    // Check file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      console.error('File size exceeds 5MB');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>) => {
      this.previewUrl = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    // Update value and notify form
    this._value = file;
    this._onChange(file);
    this._onTouched();

    // Emit file for external listeners
    this.fileSelected.emit(file);
  }

  removeFile(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();

    this.previewUrl = null;
    this.fileInput.nativeElement.value = '';

    // Update value and notify form
    this._value = null;
    this._onChange(null);
    this._onTouched();

    // Emit event for external listeners
    this.fileRemoved.emit();
  }
}
