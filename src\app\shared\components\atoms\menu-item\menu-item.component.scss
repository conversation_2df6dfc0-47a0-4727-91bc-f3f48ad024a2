@import '../../../../../styles/variables/colors';

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: $gray-800;
  text-decoration: none;
  transition: all 0.2s ease;
  margin: 0;
  position: relative;
  border-radius: 4px;
  font-size: 14px;
  font-weight: $font-weight-medium;
  height: 44px; // Fixed height for consistent alignment

  &.active {
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: $primary;
    }
  }

  &:hover {
    background-color: rgba($sidebar-hover-bg, 0.05);
  }

  &.active {
    color: $gray-800;
    font-weight: $font-weight-medium;
    background-color: $gray-200;
  }

  &.expanded {
    background-color: rgba($sidebar-hover-bg, 0.05);
  }

  .menu-chevron {
    margin-left: auto;
    font-size: 10px;
    color: $gray-500;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 16px;
    transition: transform 0.5s ease;

    &.rotate-down {
      transform: rotate(90deg);
    }
  }

  .menu-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    margin-right: 12px;
    transition: all 0.2s ease;

    app-icon {
      color: $gray-600;
      transition: color 0.2s ease;
      font-size: 16px;
    }
  }

  .menu-label {
    flex: 1;
    font-size: 14px;
    line-height: 1.5;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    white-space: nowrap;
    cursor: pointer;
  }

  .menu-badge {
    margin: 8px 30px;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: $font-weight-bold;
    text-transform: none;
    background-color: $danger;
    color: $white;
    display: inline-block;
    line-height: 1.4;
  }
}

// These styles will be applied when the parent side-menu is collapsed
:host-context(.side-menu.collapsed) {
  .menu-item {
    justify-content: center;
    padding: 12px 0;
    margin: 0;
    position: relative;
    border-radius: 0;

    &:hover {
      background-color: rgba($sidebar-hover-bg, 0.05);
    }

    &.active {
      background-color: transparent;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: $primary;
      }

      .menu-icon-container {
        app-icon {
          color: $primary;
        }
      }
    }

    .menu-icon-container {
      margin-right: 0;
      width: 20px;
      height: 20px;
    }

    .menu-label, .menu-badge, .menu-chevron {
      display: none;
    }
  }
}



