import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'app-menu-item',
  standalone: true,
  imports: [CommonModule, RouterModule, IconComponent],
  templateUrl: './menu-item.component.html',
  styleUrl: './menu-item.component.scss'
})
export class MenuItemComponent {
  @Input() icon?: string;
  @Input() label: string = '';
  @Input() route: string = '';
  @Input() active: boolean = false;
  @Input() badge: string = '';
  @Input() badgeType: 'primary' | 'success' | 'warning' | 'danger' | 'info' = 'primary';
  @Input() hasSubmenu: boolean = false;
  @Input() submenuExpanded: boolean = false;
  @Output() toggleSubmenu = new EventEmitter<void>();

  onItemClick(event: MouseEvent): void {
    if (this.hasSubmenu) {
      event.preventDefault();
      this.toggleSubmenu.emit();
    }
  }
}
