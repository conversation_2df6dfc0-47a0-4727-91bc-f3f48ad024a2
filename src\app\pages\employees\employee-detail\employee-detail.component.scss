@use 'sass:color';
@import '../../../../styles/variables/_colors';

.employee-detail {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 0;

  // Header
  &__header {
    background: white;
    padding: 16px 24px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .back-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      background: none;
      border: none;
      color: #6c757d;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      padding: 8px 0;

      &:hover {
        color: $primary;
      }

      i {
        font-size: 18px;
      }
    }

    .header-actions {
      .btn {
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;

        &.btn-primary {
          background: $primary;
          color: white;

          &:hover {
            background: color.adjust($primary, $lightness: -10%);
          }
        }

        i {
          font-size: 16px;
        }
      }
    }
  }

  // Loading and Error States
  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: white;
    margin: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .loading-spinner,
    .error-message {
      text-align: center;
      color: #6c757d;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }

      h3 {
        margin: 16px 0 8px 0;
        color: #495057;
      }

      p {
        margin: 0 0 16px 0;
      }

      .btn {
        padding: 8px 16px;
        background: $primary;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 6px;

        &:hover {
          background: color.adjust($primary, $lightness: -10%);
        }
      }
    }
  }

  // Employee Profile
  .employee-profile {
    padding: 24px;

    // Profile Banner
    .profile-banner {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      border-radius: 16px;
      padding: 32px;
      display: flex;
      align-items: center;
      gap: 24px;
      margin-bottom: 24px;
      color: white;
      position: relative;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border-radius: 50%;
        transform: translate(50%, -50%);
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -50px;
        left: -50px;
        width: 150px;
        height: 150px;
        background: linear-gradient(45deg, rgba(240, 147, 251, 0.2), rgba(118, 75, 162, 0.1));
        border-radius: 50%;
      }

      .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        border: 4px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 1;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .avatar-placeholder {
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 48px;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }

      .profile-info {
        flex: 1;
        position: relative;
        z-index: 1;

        .employee-name {
          font-size: 28px;
          font-weight: 700;
          margin: 0 0 8px 0;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .employee-position {
          font-size: 16px;
          margin: 0 0 4px 0;
          opacity: 0.9;
        }

        .employee-experience {
          font-size: 14px;
          margin: 0;
          opacity: 0.8;
        }
      }

      .profile-actions {
        display: flex;
        gap: 12px;
        position: relative;
        z-index: 1;

        .btn {
          padding: 10px 20px;
          border-radius: 8px;
          font-weight: 500;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.3s ease;

          &.btn-outline {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;

            &:hover {
              background: rgba(255, 255, 255, 0.3);
              transform: translateY(-2px);
            }
          }

          &.btn-primary {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            color: #ff6b35;

            &:hover {
              background: white;
              transform: translateY(-2px);
            }
          }

          i {
            font-size: 18px;
          }
        }
      }
    }

    // Employee Details Grid
    .employee-details-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      margin-bottom: 32px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }

      .info-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        transition: all 0.3s ease;
        border-left: 4px solid transparent;

        &:nth-child(1) {
          border-left-color: #4facfe;
          &:hover {
            box-shadow: 0 8px 30px rgba(79, 172, 254, 0.2);
          }
        }

        &:nth-child(2) {
          border-left-color: #43e97b;
          &:hover {
            box-shadow: 0 8px 30px rgba(67, 233, 123, 0.2);
          }
        }

        &:nth-child(3) {
          border-left-color: #fa709a;
          &:hover {
            box-shadow: 0 8px 30px rgba(250, 112, 154, 0.2);
          }
        }

        &:nth-child(4) {
          border-left-color: #fee140;
          &:hover {
            box-shadow: 0 8px 30px rgba(254, 225, 64, 0.2);
          }
        }

        &:nth-child(5) {
          border-left-color: #a8edea;
          &:hover {
            box-shadow: 0 8px 30px rgba(168, 237, 234, 0.2);
          }
        }

        &:nth-child(6) {
          border-left-color: #d299c2;
          &:hover {
            box-shadow: 0 8px 30px rgba(210, 153, 194, 0.2);
          }
        }

        &:hover {
          transform: translateY(-4px);
        }

        .card-header {
          padding: 20px 24px 16px 24px;
          border-bottom: 1px solid #f1f3f4;
          display: flex;
          justify-content: space-between;
          align-items: center;

          h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #202124;
          }

          .card-actions {
            display: flex;
            gap: 8px;
          }

          .edit-btn,
          .expand-btn {
            background: none;
            border: none;
            color: #5f6368;
            cursor: pointer;
            padding: 6px;
            border-radius: 50%;
            transition: all 0.2s ease;

            &:hover {
              background: #f1f3f4;
              color: $primary;
            }

            i {
              font-size: 18px;
            }
          }
        }

        .card-content {
          padding: 20px 24px;

          &.expanded {
            // Add any expanded state styles if needed
          }

          .info-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 8px 0;
            border-bottom: 1px solid #f8f9fa;

            &:last-child {
              border-bottom: none;
            }

            .label {
              font-size: 14px;
              color: #5f6368;
              font-weight: 500;
              min-width: 120px;
            }

            .value {
              font-size: 14px;
              color: #202124;
              text-align: right;
              flex: 1;
              word-break: break-word;
            }
          }

          .education-item,
          .experience-item {
            padding: 16px 0;
            border-bottom: 1px solid #f8f9fa;

            &:last-child {
              border-bottom: none;
              padding-bottom: 0;
            }

            h4 {
              margin: 0 0 4px 0;
              font-size: 16px;
              font-weight: 600;
              color: #202124;
            }

            p {
              margin: 0;
              font-size: 14px;
              color: #5f6368;
            }
          }
        }
      }
    }

    // Management Sections
    .management-sections {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      border: 1px solid rgba(102, 126, 234, 0.1);

      .section-tabs {
        display: flex;
        border-bottom: 1px solid #f1f3f4;
        background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(240, 147, 251, 0.05));

        .tab-btn {
          flex: 1;
          padding: 18px 24px;
          background: none;
          border: none;
          font-size: 15px;
          font-weight: 600;
          color: #5f6368;
          cursor: pointer;
          transition: all 0.3s ease;
          border-bottom: 3px solid transparent;
          position: relative;

          &:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(240, 147, 251, 0.1));
            color: #667eea;
            transform: translateY(-1px);
          }

          &.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(240, 147, 251, 0.15));

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 2px;
              background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            }
          }
        }
      }

      .tab-content {
        padding: 24px;

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;
          padding-bottom: 16px;
          border-bottom: 2px solid #f1f3f4;

          h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #202124;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .btn {
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;

            &.btn-primary {
              background: linear-gradient(135deg, #667eea, #764ba2);
              color: white;
              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
              }
            }
          }
        }

        .loading-state,
        .empty-state {
          text-align: center;
          padding: 60px 20px;
          color: #5f6368;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
            opacity: 0.5;
          }

          p {
            font-size: 16px;
            margin: 0;
          }
        }

        .projects-section {
          .project-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
          }
        }

        .assets-section {
          .assets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
          }
        }

        .documents-section {
          // Remove padding since the component has its own
          padding: 0;

          app-employee-documents-section {
            display: block;
            border-radius: 12px;
            overflow: hidden;
          }

          .loading-state {
            text-align: center;
            padding: 60px 20px;
            color: #5f6368;

            i {
              font-size: 48px;
              margin-bottom: 16px;
              display: block;
              color: #667eea;
            }

            p {
              font-size: 16px;
              margin: 0;
            }
          }
        }



        // Action buttons
        .btn-action {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          border: none;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          font-size: 14px;

          &.edit {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            }
          }

          &.delete {
            background: linear-gradient(135deg, #fa709a, #fee140);
            color: white;
            box-shadow: 0 2px 8px rgba(250, 112, 154, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(250, 112, 154, 0.4);
            }
          }

          &.return {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
            color: white;
            box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(67, 233, 123, 0.4);
            }
          }
        }

        // Project card actions
        .project-card {
          display: flex;
          gap: 16px;
          padding: 20px;
          border: 1px solid #e8eaed;
          border-radius: 12px;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
          }

          .project-icon {
            width: 56px;
            height: 56px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

            &.world-health {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #4facfe 100%);
              box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
            }

            &.hospital-admin {
              background: linear-gradient(135deg, #fa709a 0%, #fee140 50%, #43e97b 100%);
              box-shadow: 0 4px 20px rgba(250, 112, 154, 0.3);
            }

            i {
              font-size: 26px;
              color: white;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
          }

          .project-info {
            flex: 1;

            h4 {
              margin: 0 0 8px 0;
              font-size: 16px;
              font-weight: 600;
              color: #202124;
            }

            .project-stats {
              display: flex;
              gap: 12px;
              margin-bottom: 12px;

              span {
                font-size: 11px;
                font-weight: 600;
                padding: 6px 12px;
                border-radius: 20px;

                &.tasks {
                  background: linear-gradient(135deg, #667eea, #764ba2);
                  color: white;
                  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
                }

                &.completed {
                  background: linear-gradient(135deg, #43e97b, #38f9d7);
                  color: white;
                  box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3);
                }
              }
            }

            .project-meta {
              .deadline {
                font-size: 12px;
                color: #5f6368;
                display: block;
                margin-bottom: 8px;
              }

              .project-lead {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;
                color: #5f6368;

                .lead-avatar {
                  width: 20px;
                  height: 20px;
                  border-radius: 50%;
                  overflow: hidden;

                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                  }
                }
              }
            }
          }

          .project-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            justify-content: flex-end;
          }
        }
      }
    }
  }
}


