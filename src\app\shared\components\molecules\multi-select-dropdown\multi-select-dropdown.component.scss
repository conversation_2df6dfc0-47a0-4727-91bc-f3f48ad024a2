// Variables
$primary: #ff6b35;
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;
$white: #ffffff;
$border-radius: 12px;

.multi-select-dropdown {
  position: relative;
  width: 220px; /* Fixed width */

  &__toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 10px 16px;
    background-color: $white;
    border: 1px solid $gray-300;
    border-radius: $border-radius;
    font-size: 14px;
    color: $gray-700;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;

    &:hover {
      border-color: $gray-400;
    }

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba($primary, 0.1);
    }
  }

  &.is-disabled &__toggle {
    background-color: $gray-100;
    color: $gray-500;
    cursor: not-allowed;
  }

  &.is-invalid &__toggle {
    border-color: #dc3545;
  }

  &__selected-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__toggle-icon {
    margin-left: 8px;
    transition: transform 0.2s ease;
  }

  &.is-open &__toggle-icon {
    transform: rotate(180deg);
  }

  &__menu {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    width: 100%;
    background-color: $white;
    border: 1px solid $gray-300;
    border-radius: $border-radius;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;
    max-height: 300px;
    display: flex;
    flex-direction: column;
  }

  &__actions {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    border-bottom: 1px solid $gray-200;
  }

  &__action-btn {
    background: none;
    border: none;
    color: $primary;
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;

    &:hover {
      background-color: rgba($primary, 0.1);
    }
  }

  &__search {
    position: relative;
    padding: 8px;
    border-bottom: 1px solid $gray-200;
  }

  &__search-input {
    width: 100%;
    padding: 8px 32px 8px 12px;
    border: 1px solid $gray-300;
    border-radius: 8px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba($primary, 0.1);
    }
  }

  &__search-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: $gray-500;
  }

  &__options {
    overflow-y: auto;
    max-height: 240px;
  }

  &__option {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: $gray-100;
    }

    &.is-active {
      background-color: rgba($primary, 0.1);
    }
  }

  &__checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    margin-right: 12px;

    input {
      position: absolute;
      opacity: 0;
      cursor: pointer;
      height: 0;
      width: 0;
    }

    label {
      position: absolute;
      top: 0;
      left: 0;
      height: 18px;
      width: 18px;
      background-color: $white;
      border: 1px solid $gray-400;
      border-radius: 4px;
      cursor: pointer;

      &:after {
        content: "";
        position: absolute;
        display: none;
        left: 6px;
        top: 2px;
        width: 5px;
        height: 10px;
        border: solid $white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
      }
    }

    input:checked ~ label {
      background-color: $primary;
      border-color: $primary;

      &:after {
        display: block;
      }
    }
  }

  &__option-icon {
    margin-right: 8px;
  }

  &__option-label {
    flex: 1;
  }

  &__no-results {
    padding: 16px;
    text-align: center;
    color: $gray-600;
    font-style: italic;
  }
}
