import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthStore } from '../../core/state';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="dashboard-container">
      <div class="welcome-section">
        <div class="welcome-content">
          <h1 class="welcome-title">Welcome back, {{ fullName() }}!</h1>
          <p class="welcome-subtitle">Here's what's happening with your HR management today.</p>
        </div>
        <div class="admin-badge" *ngIf="isAdmin()">
          <span>Admin</span>
        </div>
      </div>

      <div class="dashboard-stats">
        <div class="stat-card stat-card--primary">
          <div class="stat-card__icon">
            <i class="fa fa-users"></i>
          </div>
          <div class="stat-card__content">
            <h3 class="stat-card__value">248</h3>
            <p class="stat-card__title">Total Employees</p>
          </div>
        </div>

        <div class="stat-card stat-card--success">
          <div class="stat-card__icon">
            <i class="fa fa-building"></i>
          </div>
          <div class="stat-card__content">
            <h3 class="stat-card__value">12</h3>
            <p class="stat-card__title">Departments</p>
          </div>
        </div>

        <div class="stat-card stat-card--warning">
          <div class="stat-card__icon">
            <i class="fa fa-project-diagram"></i>
          </div>
          <div class="stat-card__content">
            <h3 class="stat-card__value">36</h3>
            <p class="stat-card__title">Projects</p>
          </div>
        </div>

        <div class="stat-card stat-card--danger">
          <div class="stat-card__icon">
            <i class="fa fa-tasks"></i>
          </div>
          <div class="stat-card__content">
            <h3 class="stat-card__value">124</h3>
            <p class="stat-card__title">Tasks</p>
          </div>
        </div>
      </div>

      <div class="dashboard-content">
        <div class="dashboard-card">
          <div class="dashboard-card__header">
            <h2 class="dashboard-card__title">Recent Activities</h2>
            <a href="#" class="dashboard-card__link">View All</a>
          </div>
          <div class="dashboard-card__body">
            <ul class="activity-list">
              <li class="activity-item">
                <div class="activity-icon activity-icon--success">
                  <i class="fa fa-user-plus"></i>
                </div>
                <div class="activity-content">
                  <p class="activity-text">
                    <strong>John Doe</strong> added a new employee
                    <span class="activity-target">Sarah Johnson</span>
                  </p>
                  <span class="activity-time">2 hours ago</span>
                </div>
              </li>
              <li class="activity-item">
                <div class="activity-icon activity-icon--primary">
                  <i class="fa fa-sync"></i>
                </div>
                <div class="activity-content">
                  <p class="activity-text">
                    <strong>Emily Clark</strong> updated project status
                    <span class="activity-target">Website Redesign</span>
                  </p>
                  <span class="activity-time">4 hours ago</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    @import '../../../styles/variables/_colors';

    .dashboard-container {
      padding: 1.5rem;
    }

    // Welcome Section
    .welcome-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;

      .welcome-title {
        font-size: 1.75rem;
        font-weight: $font-weight-bold;
        color: $gray-900;
        margin-bottom: 0.5rem;
      }

      .welcome-subtitle {
        color: $gray-600;
        font-size: 1rem;
      }

      .admin-badge {
        background-color: $primary;
        color: $white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: $font-weight-semibold;
      }
    }

    // Stats Grid
    .dashboard-stats {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 1.5rem;
      margin-bottom: 2rem;

      @media (max-width: 1200px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 576px) {
        grid-template-columns: 1fr;
      }
    }

    // Stat Card
    .stat-card {
      display: flex;
      align-items: center;
      padding: 1.5rem;
      border-radius: 8px;
      background-color: $white;
      box-shadow: 0 2px 10px rgba($black, 0.05);

      &__icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.5rem;
      }

      &__content {
        flex: 1;
      }

      &__value {
        font-size: 1.75rem;
        font-weight: $font-weight-bold;
        margin-bottom: 0.25rem;
        line-height: 1;
      }

      &__title {
        color: $gray-600;
        font-size: 0.875rem;
        margin: 0;
      }

      // Color variants
      &--primary {
        .stat-card__icon {
          background-color: rgba($primary, 0.1);
          color: $primary;
        }

        .stat-card__value {
          color: $primary;
        }
      }

      &--success {
        .stat-card__icon {
          background-color: rgba($success, 0.1);
          color: $success;
        }

        .stat-card__value {
          color: $success;
        }
      }

      &--warning {
        .stat-card__icon {
          background-color: rgba($warning, 0.1);
          color: $warning;
        }

        .stat-card__value {
          color: $warning;
        }
      }

      &--danger {
        .stat-card__icon {
          background-color: rgba($danger, 0.1);
          color: $danger;
        }

        .stat-card__value {
          color: $danger;
        }
      }
    }

    // Dashboard Content
    .dashboard-content {
      display: grid;
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    // Dashboard Card
    .dashboard-card {
      background-color: $white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba($black, 0.05);
      overflow: hidden;

      &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid $gray-200;
      }

      &__title {
        font-size: 1.125rem;
        font-weight: $font-weight-semibold;
        color: $gray-800;
        margin: 0;
      }

      &__link {
        color: $primary;
        font-size: 0.875rem;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      &__body {
        padding: 1.5rem;
      }
    }

    // Activity List
    .activity-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .activity-item {
      display: flex;
      align-items: flex-start;
      padding: 0.75rem 0;
      border-bottom: 1px solid $gray-100;

      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }

      &:first-child {
        padding-top: 0;
      }
    }

    .activity-icon {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1rem;
      font-size: 1rem;

      &--primary {
        background-color: rgba($primary, 0.1);
        color: $primary;
      }

      &--success {
        background-color: rgba($success, 0.1);
        color: $success;
      }
    }

    .activity-content {
      flex: 1;
    }

    .activity-text {
      margin: 0 0 0.25rem;
      font-size: 0.875rem;
      color: $gray-700;

      strong {
        font-weight: $font-weight-semibold;
        color: $gray-900;
      }
    }

    .activity-target {
      font-weight: $font-weight-medium;
      color: $gray-800;
    }

    .activity-time {
      font-size: 0.75rem;
      color: $gray-500;
    }
  `]
})
export class DashboardComponent {
  private authStore = inject(AuthStore);

  // Access state from the store using signals
  user = this.authStore.user;
  isAuthenticated = this.authStore.isAuthenticated;
  fullName = this.authStore.fullName;
  isAdmin = this.authStore.isAdmin;
}
