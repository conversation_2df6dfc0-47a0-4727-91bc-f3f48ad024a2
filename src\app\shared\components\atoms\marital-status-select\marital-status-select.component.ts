import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

export interface MaritalStatusOption {
  value: 'single' | 'married' | 'divorced' | 'widowed';
  label: string;
  icon?: string;
}

@Component({
  selector: 'app-marital-status-select',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './marital-status-select.component.html',
  styleUrls: ['./marital-status-select.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => MaritalStatusSelectComponent),
      multi: true
    }
  ]
})
export class MaritalStatusSelectComponent implements ControlValueAccessor {
  @Input() label: string = 'Marital Status';
  @Input() placeholder: string = 'Select marital status';
  @Input() required: boolean = false;
  @Input() disabled: boolean = false;
  @Input() error: string | null = null;
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() variant: 'default' | 'outline' | 'filled' = 'default';
  
  @Output() selectionChange = new EventEmitter<'single' | 'married' | 'divorced' | 'widowed' | null>();

  value: 'single' | 'married' | 'divorced' | 'widowed' | null = null;
  isOpen = false;

  maritalStatusOptions: MaritalStatusOption[] = [
    { value: 'single', label: 'Single', icon: '👤' },
    { value: 'married', label: 'Married', icon: '💑' },
    { value: 'divorced', label: 'Divorced', icon: '💔' },
    { value: 'widowed', label: 'Widowed', icon: '🖤' }
  ];

  // ControlValueAccessor implementation
  private onChange = (value: 'single' | 'married' | 'divorced' | 'widowed' | null) => {};
  private onTouched = () => {};

  writeValue(value: 'single' | 'married' | 'divorced' | 'widowed' | null): void {
    this.value = value;
  }

  registerOnChange(fn: (value: 'single' | 'married' | 'divorced' | 'widowed' | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  toggleDropdown(): void {
    if (!this.disabled) {
      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        this.onTouched();
      }
    }
  }

  selectOption(option: MaritalStatusOption): void {
    if (!this.disabled) {
      this.value = option.value;
      this.isOpen = false;
      this.onChange(this.value);
      this.selectionChange.emit(this.value);
    }
  }

  clearSelection(): void {
    if (!this.disabled) {
      this.value = null;
      this.onChange(this.value);
      this.selectionChange.emit(this.value);
    }
  }

  onBlur(): void {
    setTimeout(() => {
      this.isOpen = false;
      this.onTouched();
    }, 150);
  }

  get selectedOption(): MaritalStatusOption | null {
    return this.maritalStatusOptions.find(option => option.value === this.value) || null;
  }

  get hasError(): boolean {
    return !!this.error;
  }

  get sizeClass(): string {
    return `marital-status-select--${this.size}`;
  }

  get variantClass(): string {
    return `marital-status-select--${this.variant}`;
  }
}
