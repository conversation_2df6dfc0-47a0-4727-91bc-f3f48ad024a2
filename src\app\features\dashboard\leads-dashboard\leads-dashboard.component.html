<div class="dashboard-container">
  <!-- Dashboard Header -->
  <div class="dashboard-header">
    <div class="title-section">
      <h1>Leads Dashboard</h1>
      <div class="breadcrumbs">
        <a [routerLink]="['/dashboard']">Dashboard</a>
        <span class="separator">/</span>
        <span class="current">Leads Dashboard</span>
      </div>
    </div>
    <div class="actions">
      <button class="btn-export">
        <i class="far fa-file-export"></i>
        Export
      </button>
      <div class="date-range">
        <input type="text" placeholder="dd/mm/yyyy - dd/mm/yyyy">
      </div>
      <button class="btn-filter">
        <i class="far fa-filter"></i>
      </button>
    </div>
  </div>

  <!-- Metric Cards -->
  <div class="metric-cards">
    @for (card of metricCards; track card.title) {
      <div class="metric-card">
        <div class="card-header" [style.background-color]="card.iconColor + '20'">
          <div class="icon-container" [style.background-color]="card.iconColor">
            <i class="far fa-{{card.icon}}" [style.color]="'white'"></i>
          </div>
          <div class="card-title">{{card.title}}</div>
        </div>
        <div class="card-content">
          <div class="value">{{card.value}}</div>
          <div class="progress-bar">
            <div class="progress" [style.width]="'60%'" [style.background-color]="card.iconColor"></div>
          </div>
          <div class="change" [ngClass]="{'positive': card.changeType === 'positive', 'negative': card.changeType === 'negative'}">
            <i class="far" [ngClass]="card.changeType === 'positive' ? 'fa-arrow-up' : 'fa-arrow-down'"></i>
            <span>{{card.change}}% from last week</span>
          </div>
        </div>
      </div>
    }
  </div>

  <!-- Pipeline Stages and New Leads -->
  <div class="dashboard-row">
    <div class="dashboard-card pipeline-stages">
      <div class="card-header">
        <h2>Pipeline Stages</h2>
        <div class="date-range">
          <span>2023 - 2024</span>
        </div>
      </div>
      <div class="pipeline-content">
        @for (stage of pipelineStages; track stage.name) {
          <div class="pipeline-stage">
            <div class="stage-header">
              <div class="color-indicator" [style.background-color]="stage.color"></div>
              <div class="stage-name">{{stage.name}}</div>
            </div>
            <div class="stage-value">{{stage.value}}</div>
          </div>
        }
      </div>
    </div>

    <div class="dashboard-card new-leads">
      <div class="card-header">
        <h2>New Leads</h2>
        <div class="date-range">
          <span>This Week</span>
        </div>
      </div>
      <div class="new-leads-content">
        <!-- Placeholder for new leads content -->
        <div class="placeholder-chart"></div>
      </div>
    </div>
  </div>

  <!-- Bottom Row: Lost Leads, Leads By Companies, Leads By Source -->
  <div class="dashboard-row">
    <div class="dashboard-card lost-leads">
      <div class="card-header">
        <h2>Lost Leads By Reason</h2>
      </div>
      <div class="lost-leads-content">
        <div class="reason-list">
          @for (reason of lostLeadsReasons; track reason.reason) {
            <div class="reason-item">
              <div class="reason-name">{{reason.reason}}</div>
              <div class="reason-bar">
                <div class="reason-progress" [style.width]="reason.percentage + '%'"></div>
              </div>
              <div class="reason-percentage">{{reason.percentage}}%</div>
            </div>
          }
        </div>
      </div>
    </div>

    <div class="dashboard-card leads-by-companies">
      <div class="card-header">
        <h2>Leads By Companies</h2>
        <div class="date-range">
          <span>This Week</span>
        </div>
      </div>
      <div class="companies-content">
        @for (company of leadsByCompany; track company.name) {
          <div class="company-item">
            <div class="company-logo">
              @if (company.logo) {
                <img [src]="company.logo" [alt]="company.name">
              } @else {
                <div class="logo-placeholder">
                  {{company.name.charAt(0)}}
                </div>
              }
            </div>
            <div class="company-info">
              <div class="company-name">{{company.name}}</div>
              <div class="company-value">Value - ${{company.value}}</div>
            </div>
            @if (company.name === 'Pitch') {
              <div class="company-tag">Not Contacted</div>
            }
          </div>
        }
      </div>
    </div>

    <div class="dashboard-card leads-by-source">
      <div class="card-header">
        <h2>Leads by Source</h2>
        <div class="date-range">
          <span>This Week</span>
        </div>
      </div>
      <div class="source-content">
        <div class="source-list">
          <div class="source-header">
            <div class="source-title">Status</div>
            <div class="source-percentage">%</div>
          </div>
          @for (source of leadsBySource; track source.source) {
            <div class="source-item">
              <div class="source-name">{{source.source}}</div>
              <div class="source-value">{{source.percentage}}%</div>
            </div>
          }
        </div>
      </div>
    </div>
  </div>
</div>
