/**
 * NgRx Signals Departments State
 *
 * This file implements departments state management using NgRx Signals.
 */

import { patchState, signalStore, withComputed, withMethods, withState } from '@ngrx/signals';
import { computed, inject } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { catchError, finalize, map, of, pipe, switchMap, tap } from 'rxjs';
import { DepartmentService } from '../../services/department.service';
import { Department, DepartmentInput, DepartmentListParams, DepartmentFilters, DepartmentDetail } from '../../models/department.interface';

// Define the departments state interface
export interface DepartmentsState {
  departments: Department[];
  selectedDepartment: Department | null;
  selectedDepartmentDetail: DepartmentDetail | null;
  isLoading: boolean;
  isLoadingDetail: boolean;
  error: string | null;
  // Pagination state
  page: number;
  pageSize: number;
  totalCount: number;
  // Search state
  searchTerm: string;
  // Filter state
  filters: DepartmentFilters;
}

// Define the initial state
export const initialDepartmentsState: DepartmentsState = {
  departments: [],
  selectedDepartment: null,
  selectedDepartmentDetail: null,
  isLoading: false,
  isLoadingDetail: false,
  error: null,
  page: 1,
  pageSize: 10,
  totalCount: 0,
  searchTerm: '',
  filters: {
    status: [],
    manager_name: [],
    manager: [],
    search: '',
    ordering: ''
  }
};

// Create the departments store
export const DepartmentsStore = signalStore(
  { providedIn: 'root' },
  withState(initialDepartmentsState),
  withComputed((state) => ({
    // Computed properties
    departmentsCount: computed(() => state.departments().length),
    hasDepartments: computed(() => state.departments().length > 0),
    totalPages: computed(() => Math.ceil(state.totalCount() / state.pageSize())),
    hasNextPage: computed(() => state.page() < Math.ceil(state.totalCount() / state.pageSize())),
    hasPreviousPage: computed(() => state.page() > 1),
    departmentNames: computed(() => {
      return state.departments().map(department => department.name);
    })
  })),
  withMethods((store, departmentService = inject(DepartmentService)) => ({
    // Load departments with pagination and filters
    loadDepartments: rxMethod<DepartmentListParams>(
      pipe(
        tap((params) => {
          // Update pagination and search state
          if (params.page !== undefined) {
            patchState(store, { page: params.page });
          }
          if (params.page_size !== undefined) {
            patchState(store, { pageSize: params.page_size });
          }
          if (params.search !== undefined) {
            patchState(store, { searchTerm: params.search });
          }

          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((params) => {
          // Build the complete parameters object
          const currentFilters = store.filters();
          const requestParams: DepartmentListParams = {
            page: params.page ?? store.page(),
            page_size: params.page_size ?? store.pageSize(),
            search: params.search ?? store.searchTerm() ?? currentFilters.search,
            ordering: params.ordering ?? currentFilters.ordering,
            manager: params.manager ?? (currentFilters.manager.length > 0 ? currentFilters.manager[0] : undefined),
            manager_name: currentFilters.manager_name.length > 0 ? currentFilters.manager_name.join(',') : undefined,
            status: currentFilters.status.length > 0 ? currentFilters.status.join(',') : undefined,
            name: params.name
          };

          return departmentService.getDepartments(requestParams).pipe(
            map((response) => {
              patchState(store, {
                departments: response.results,
                totalCount: response.count,
                isLoading: false,
                error: null
              });
              return response;
            }),
            catchError((error: HttpErrorResponse) => {
              console.error('Error loading departments:', error);
              const errorMessage = error.error?.detail || error.error?.message || 'Failed to load departments';
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          );
        })
      )
    ),

    // Load all departments (for dropdowns, etc.)
    loadAllDepartments: rxMethod<void>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap(() =>
          departmentService.getAllDepartments().pipe(
            map((departments) => {
              patchState(store, {
                departments,
                isLoading: false,
                error: null
              });
              return departments;
            }),
            catchError((error: HttpErrorResponse) => {
              console.error('Error loading all departments:', error);
              const errorMessage = error.error?.detail || error.error?.message || 'Failed to load departments';
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of([]);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Load a single department by ID
    loadDepartment: rxMethod<number>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((id) =>
          departmentService.getDepartmentById(id).pipe(
            map((department) => {
              patchState(store, {
                selectedDepartment: department,
                isLoading: false,
                error: null
              });
              return department;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to load department with ID ${id}`;
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Load department details by ID (includes employees)
    loadDepartmentDetail: rxMethod<number>(
      pipe(
        tap(() => {
          patchState(store, { isLoadingDetail: true, error: null });
        }),
        switchMap((id) =>
          departmentService.getDepartmentDetail(id).pipe(
            map((departmentDetail) => {
              patchState(store, {
                selectedDepartmentDetail: departmentDetail,
                isLoadingDetail: false,
                error: null
              });
              return departmentDetail;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to load department details with ID ${id}`;
              patchState(store, {
                isLoadingDetail: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoadingDetail: false });
            })
          )
        )
      )
    ),

    // Create a new department
    createDepartment: rxMethod<DepartmentInput>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((departmentData) =>
          departmentService.createDepartment(departmentData).pipe(
            map((newDepartment) => {
              // Return the new department
              return newDepartment;
            }),
            // After the operation is complete, reload the departments
            tap(() => {
              const requestParams: DepartmentListParams = {
                page: store.page(),
                page_size: store.pageSize(),
                search: store.searchTerm()
              };
              departmentService.getDepartments(requestParams).subscribe(
                response => {
                  patchState(store, {
                    departments: response.results,
                    totalCount: response.count
                  });
                }
              );
            }),
            catchError((error: HttpErrorResponse) => {
              console.error('Error creating department:', error);
              const errorMessage = error.error?.detail || error.error?.message || 'Failed to create department';
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Update an existing department
    updateDepartment: rxMethod<{ id: number; data: DepartmentInput }>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap(({ id, data }) =>
          departmentService.updateDepartment(id, data).pipe(
            map((updatedDepartment) => {
              // Return the updated department
              return updatedDepartment;
            }),
            // After the operation is complete, reload the departments
            tap(() => {
              const requestParams: DepartmentListParams = {
                page: store.page(),
                page_size: store.pageSize(),
                search: store.searchTerm()
              };
              departmentService.getDepartments(requestParams).subscribe(
                response => {
                  patchState(store, {
                    departments: response.results,
                    totalCount: response.count
                  });
                }
              );
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to update department with ID ${id}`;
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Delete a department
    deleteDepartment: rxMethod<number>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((id) =>
          departmentService.deleteDepartment(id).pipe(
            map(() => {
              // Return success
              return true;
            }),
            // After the operation is complete, reload the departments
            tap(() => {
              const requestParams: DepartmentListParams = {
                page: store.page(),
                page_size: store.pageSize(),
                search: store.searchTerm()
              };
              departmentService.getDepartments(requestParams).subscribe(
                response => {
                  patchState(store, {
                    departments: response.results,
                    totalCount: response.count
                  });
                }
              );
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to delete department with ID ${id}`;
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(false);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Set page
    setPage(page: number): void {
      patchState(store, { page });
      this.loadDepartments({});
    },

    // Set page size
    setPageSize(pageSize: number): void {
      patchState(store, { pageSize, page: 1 }); // Reset to first page when changing page size
      this.loadDepartments({});
    },

    // Set search term
    setSearchTerm(searchTerm: string): void {
      patchState(store, { searchTerm, page: 1 }); // Reset to first page when searching
      this.loadDepartments({});
    },

    // Reset search
    resetSearch(): void {
      patchState(store, { searchTerm: '', page: 1 });
      this.loadDepartments({});
    },

    // Filter management methods
    setStatusFilter(status: string[]): void {
      patchState(store, {
        filters: { ...store.filters(), status },
        page: 1
      });
      this.loadDepartments({});
    },

    setManagerFilter(manager_name: string[]): void {
      patchState(store, {
        filters: { ...store.filters(), manager_name },
        page: 1
      });
      this.loadDepartments({});
    },

    setManagerIdFilter(manager: number[]): void {
      patchState(store, {
        filters: { ...store.filters(), manager },
        page: 1
      });
      this.loadDepartments({});
    },

    setOrdering(ordering: string): void {
      patchState(store, {
        filters: { ...store.filters(), ordering },
        page: 1
      });
      this.loadDepartments({});
    },

    setSearchFilter(search: string): void {
      patchState(store, {
        filters: { ...store.filters(), search },
        searchTerm: search,
        page: 1
      });
      this.loadDepartments({});
    },

    clearAllFilters(): void {
      patchState(store, {
        filters: {
          status: [],
          manager_name: [],
          manager: [],
          search: '',
          ordering: ''
        },
        searchTerm: '',
        page: 1
      });
      this.loadDepartments({});
    },

    // Clear selected department detail
    clearDepartmentDetail(): void {
      patchState(store, {
        selectedDepartmentDetail: null,
        isLoadingDetail: false,
        error: null
      });
    }
  }))
);
