<div class="hike-form">
  <div class="form-header">
    <h3>{{ isEdit ? 'Edit Salary Hike' : 'Add New Salary Hike' }}</h3>
  </div>

  <form [formGroup]="hikeForm" (ngSubmit)="onSubmit()" class="form-content">
    <!-- Previous Salary -->
    <div class="form-group">
      <label for="previous_salary" class="form-label">
        Previous Salary <span class="required">*</span>
      </label>
      <div class="input-wrapper">
        <span class="currency-symbol">$</span>
        <input
          id="previous_salary"
          type="number"
          formControlName="previous_salary"
          class="form-input"
          [class.error]="isFieldInvalid('previous_salary')"
          placeholder="Enter previous salary"
          min="0"
          step="0.01">
      </div>
      @if (isFieldInvalid('previous_salary')) {
        <div class="error-message">
          {{ getFieldError('previous_salary') }}
        </div>
      }
    </div>

    <!-- Calculation Method Toggle -->
    <div class="calculation-method">
      <label class="form-label">Calculation Method</label>
      <div class="method-toggle">
        <button
          type="button"
          class="method-button"
          [class.active]="calculationMethod === 'percentage'"
          (click)="setCalculationMethod('percentage')">
          <span class="material-icons">percent</span>
          Percentage
        </button>
        <button
          type="button"
          class="method-button"
          [class.active]="calculationMethod === 'amount'"
          (click)="setCalculationMethod('amount')">
          <span class="material-icons">attach_money</span>
          Amount
        </button>
      </div>
    </div>

    <!-- Hike Input Fields -->
    <div class="hike-inputs">
      <!-- Hike Percentage -->
      <div class="form-group" [class.disabled]="calculationMethod !== 'percentage'">
        <label for="hike_percentage" class="form-label">
          Hike Percentage <span class="required">*</span>
        </label>
        <div class="input-wrapper">
          <input
            id="hike_percentage"
            type="number"
            formControlName="hike_percentage"
            class="form-input"
            [class.error]="isFieldInvalid('hike_percentage')"
            placeholder="Enter hike percentage"
            min="0"
            max="100"
            step="0.01">
          <span class="percentage-symbol">%</span>
        </div>
        @if (isFieldInvalid('hike_percentage')) {
          <div class="error-message">
            {{ getFieldError('hike_percentage') }}
          </div>
        }
      </div>

      <!-- Hike Amount -->
      <div class="form-group" [class.disabled]="calculationMethod !== 'amount'">
        <label for="hike_amount" class="form-label">
          Hike Amount <span class="required">*</span>
        </label>
        <div class="input-wrapper">
          <span class="currency-symbol">$</span>
          <input
            id="hike_amount"
            type="number"
            formControlName="hike_amount"
            class="form-input"
            [class.error]="isFieldInvalid('hike_amount')"
            placeholder="Enter hike amount"
            min="0"
            step="0.01">
        </div>
        @if (isFieldInvalid('hike_amount')) {
          <div class="error-message">
            {{ getFieldError('hike_amount') }}
          </div>
        }
      </div>
    </div>

    <!-- Calculated New Salary -->
    <div class="calculated-field">
      <div class="form-group">
        <label for="new_salary" class="form-label">New Salary</label>
        <div class="input-wrapper">
          <span class="currency-symbol">$</span>
          <input
            id="new_salary"
            type="number"
            formControlName="new_salary"
            class="form-input calculated"
            readonly
            placeholder="Calculated automatically">
        </div>
        <div class="field-note">
          Calculated as: Previous Salary + Hike Amount
        </div>
      </div>
    </div>

    <!-- Effective Date -->
    <div class="form-group">
      <label for="effective_date" class="form-label">
        Effective Date <span class="required">*</span>
      </label>
      <input
        id="effective_date"
        type="date"
        formControlName="effective_date"
        class="form-input"
        [class.error]="isFieldInvalid('effective_date')">
      @if (isFieldInvalid('effective_date')) {
        <div class="error-message">
          {{ getFieldError('effective_date') }}
        </div>
      }
    </div>

    <!-- Reason -->
    <div class="form-group">
      <label for="reason" class="form-label">
        Reason <span class="required">*</span>
      </label>
      <textarea
        id="reason"
        formControlName="reason"
        class="form-textarea"
        [class.error]="isFieldInvalid('reason')"
        placeholder="Enter reason for salary hike"
        rows="4"
        maxlength="500"></textarea>
      <div class="character-count">
        {{ reasonControl?.value?.length || 0 }}/500
      </div>
      @if (isFieldInvalid('reason')) {
        <div class="error-message">
          {{ getFieldError('reason') }}
        </div>
      }
    </div>

    <!-- Hike Summary -->
    <div class="hike-summary">
      <h4>Hike Summary</h4>
      <div class="summary-grid">
        <div class="summary-item">
          <span class="label">Previous Salary:</span>
          <span class="value">${{ previousSalaryControl?.value | number:'1.2-2' }}</span>
        </div>
        <div class="summary-item">
          <span class="label">Hike Percentage:</span>
          <span class="value">{{ hikePercentageControl?.value | number:'1.2-2' }}%</span>
        </div>
        <div class="summary-item">
          <span class="label">Hike Amount:</span>
          <span class="value">${{ hikeAmountControl?.value | number:'1.2-2' }}</span>
        </div>
        <div class="summary-item highlight">
          <span class="label">New Salary:</span>
          <span class="value">${{ newSalaryControl?.value | number:'1.2-2' }}</span>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <app-button
        [variant]="'secondary'"
        [size]="'md'"
        [disabled]="isSubmitting"
        (click)="onCancel()">
        Cancel
      </app-button>

      <app-button
        [variant]="'primary'"
        [size]="'md'"
        [disabled]="hikeForm.invalid || isSubmitting"
        type="submit">
        {{ isEdit ? 'Update Hike' : 'Submit Hike' }}
      </app-button>
    </div>
  </form>
</div>
