import { Injectable, signal, computed, effect, inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { 
  HeaderPosition, 
  LayoutConfig, 
  DEFAULT_LAYOUT_CONFIG 
} from '../models/theme.interface';

@Injectable({
  providedIn: 'root'
})
export class LayoutService {
  private readonly document = inject(DOCUMENT);
  private readonly STORAGE_KEY = 'app-layout-config';

  // Layout state signals
  private readonly _layoutConfig = signal<LayoutConfig>(this.loadLayoutConfig());

  // Public readonly signals
  public readonly layoutConfig = this._layoutConfig.asReadonly();
  public readonly headerPosition = computed(() => this._layoutConfig().headerPosition);
  public readonly sidebarCollapsed = computed(() => this._layoutConfig().sidebarCollapsed);
  public readonly sidebarPosition = computed(() => this._layoutConfig().sidebarPosition);
  public readonly layoutType = computed(() => this._layoutConfig().layoutType);

  // Computed CSS classes for layout
  public readonly layoutClasses = computed<string[]>(() => {
    const config = this._layoutConfig();
    const classes: string[] = [];
    
    // Header position classes
    classes.push(`header-${config.headerPosition}`);
    
    // Sidebar classes
    if (config.sidebarCollapsed) {
      classes.push('sidebar-collapsed');
    }
    classes.push(`sidebar-${config.sidebarPosition}`);
    
    // Layout type classes
    classes.push(`layout-${config.layoutType}`);
    
    return classes;
  });

  constructor() {
    // Apply layout classes on initialization and changes
    effect(() => {
      this.applyLayoutClasses();
    });
  }

  /**
   * Set header position
   */
  setHeaderPosition(position: HeaderPosition): void {
    const newConfig = { ...this._layoutConfig(), headerPosition: position };
    this._layoutConfig.set(newConfig);
    this.saveLayoutConfig(newConfig);
  }

  /**
   * Toggle sidebar collapsed state
   */
  toggleSidebar(): void {
    const newConfig = { 
      ...this._layoutConfig(), 
      sidebarCollapsed: !this._layoutConfig().sidebarCollapsed 
    };
    this._layoutConfig.set(newConfig);
    this.saveLayoutConfig(newConfig);
  }

  /**
   * Set sidebar position
   */
  setSidebarPosition(position: 'left' | 'right'): void {
    const newConfig = { ...this._layoutConfig(), sidebarPosition: position };
    this._layoutConfig.set(newConfig);
    this.saveLayoutConfig(newConfig);
  }

  /**
   * Set layout type
   */
  setLayoutType(type: 'vertical' | 'horizontal'): void {
    const newConfig = { ...this._layoutConfig(), layoutType: type };
    this._layoutConfig.set(newConfig);
    this.saveLayoutConfig(newConfig);
  }

  /**
   * Reset layout to default
   */
  resetLayout(): void {
    this._layoutConfig.set(DEFAULT_LAYOUT_CONFIG);
    this.saveLayoutConfig(DEFAULT_LAYOUT_CONFIG);
  }

  /**
   * Get header position display name
   */
  getHeaderPositionDisplayName(position: HeaderPosition): string {
    switch (position) {
      case 'fixed':
        return 'Fixed';
      case 'static':
        return 'Static';
      case 'sticky':
        return 'Sticky';
      default:
        return 'Unknown';
    }
  }

  /**
   * Check if header is fixed
   */
  isHeaderFixed(): boolean {
    return this.headerPosition() === 'fixed';
  }

  /**
   * Check if header is static
   */
  isHeaderStatic(): boolean {
    return this.headerPosition() === 'static';
  }

  /**
   * Check if header is sticky
   */
  isHeaderSticky(): boolean {
    return this.headerPosition() === 'sticky';
  }

  /**
   * Check if sidebar is collapsed
   */
  isSidebarCollapsed(): boolean {
    return this.sidebarCollapsed();
  }

  /**
   * Get available header positions
   */
  getAvailableHeaderPositions(): { value: HeaderPosition; label: string; description: string }[] {
    return [
      {
        value: 'fixed',
        label: 'Fixed',
        description: 'Header stays at top during scroll'
      },
      {
        value: 'static',
        label: 'Static',
        description: 'Header scrolls with content'
      },
      {
        value: 'sticky',
        label: 'Sticky',
        description: 'Header becomes fixed after scrolling'
      }
    ];
  }

  /**
   * Apply layout classes to document
   */
  private applyLayoutClasses(): void {
    const classes = this.layoutClasses();
    const config = this._layoutConfig();
    
    // Remove existing layout classes
    this.document.documentElement.classList.remove(
      'header-fixed', 'header-static', 'header-sticky',
      'sidebar-collapsed', 'sidebar-left', 'sidebar-right',
      'layout-vertical', 'layout-horizontal'
    );
    
    // Add current layout classes
    classes.forEach(className => {
      this.document.documentElement.classList.add(className);
    });
    
    // Set data attributes for layout
    this.document.documentElement.setAttribute('data-header-position', config.headerPosition);
    this.document.documentElement.setAttribute('data-sidebar-position', config.sidebarPosition);
    this.document.documentElement.setAttribute('data-layout-type', config.layoutType);
    this.document.documentElement.setAttribute('data-sidebar-collapsed', config.sidebarCollapsed.toString());
  }

  /**
   * Load layout configuration from localStorage
   */
  private loadLayoutConfig(): LayoutConfig {
    if (typeof window === 'undefined') {
      return DEFAULT_LAYOUT_CONFIG;
    }
    
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...DEFAULT_LAYOUT_CONFIG, ...parsed };
      }
    } catch (error) {
      console.warn('Failed to load layout config from localStorage:', error);
    }
    
    return DEFAULT_LAYOUT_CONFIG;
  }

  /**
   * Save layout configuration to localStorage
   */
  private saveLayoutConfig(config: LayoutConfig): void {
    if (typeof window === 'undefined') {
      return;
    }
    
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(config));
    } catch (error) {
      console.warn('Failed to save layout config to localStorage:', error);
    }
  }
}
