/**
 * NgRx Signals Companies State
 * 
 * This file implements companies state management using NgRx Signals.
 */

import { patchState, signalStore, withComputed, withMethods, withState } from '@ngrx/signals';
import { computed, inject } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { catchError, finalize, map, of, pipe, switchMap, tap } from 'rxjs';
import { environment } from '../../../../environments/environment';

// Company interface
export interface Company {
  id: number;
  name: string;
  email: string;
  phone?: string;
  website?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  logo?: string;
  industry?: string;
  description?: string;
  employees_count?: number;
  created_at: string;
  updated_at: string;
}

// Company creation/update interface
export interface CompanyInput {
  name: string;
  email: string;
  phone?: string;
  website?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  industry?: string;
  description?: string;
}

// Define the companies state interface
export interface CompaniesState {
  companies: Company[];
  selectedCompany: Company | null;
  isLoading: boolean;
  error: string | null;
}

// Define the initial state
export const initialCompaniesState: CompaniesState = {
  companies: [],
  selectedCompany: null,
  isLoading: false,
  error: null
};

// Create the companies store
export const CompaniesStore = signalStore(
  { providedIn: 'root' },
  withState(initialCompaniesState),
  withComputed((state) => ({
    // Computed properties
    companiesCount: computed(() => state.companies().length),
    hasCompanies: computed(() => state.companies().length > 0)
  })),
  withMethods((store, http = inject(HttpClient)) => ({
    // Load all companies
    loadCompanies: rxMethod<void>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap(() =>
          http.get<Company[]>(`${environment.apiUrl}/companies/`).pipe(
            map((companies) => {
              patchState(store, {
                companies,
                isLoading: false,
                error: null
              });
              return companies;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || 'Failed to load companies';
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of([]);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Load a single company by ID
    loadCompany: rxMethod<number>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((id) =>
          http.get<Company>(`${environment.apiUrl}/companies/${id}/`).pipe(
            map((company) => {
              patchState(store, {
                selectedCompany: company,
                isLoading: false,
                error: null
              });
              return company;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to load company with ID ${id}`;
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Create a new company
    createCompany: rxMethod<CompanyInput>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((companyData) =>
          http.post<Company>(`${environment.apiUrl}/companies/`, companyData).pipe(
            map((newCompany) => {
              const currentCompanies = store.companies();
              patchState(store, {
                companies: [...currentCompanies, newCompany],
                isLoading: false,
                error: null
              });
              return newCompany;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || 'Failed to create company';
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Update an existing company
    updateCompany: rxMethod<{ id: number; data: CompanyInput }>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap(({ id, data }) =>
          http.put<Company>(`${environment.apiUrl}/companies/${id}/`, data).pipe(
            map((updatedCompany) => {
              const currentCompanies = store.companies();
              const updatedCompanies = currentCompanies.map(company => 
                company.id === id ? updatedCompany : company
              );
              
              patchState(store, {
                companies: updatedCompanies,
                selectedCompany: updatedCompany,
                isLoading: false,
                error: null
              });
              return updatedCompany;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to update company with ID ${id}`;
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Delete a company
    deleteCompany: rxMethod<number>(
      pipe(
        tap(() => {
          patchState(store, { isLoading: true, error: null });
        }),
        switchMap((id) =>
          http.delete<void>(`${environment.apiUrl}/companies/${id}/`).pipe(
            map(() => {
              const currentCompanies = store.companies();
              const filteredCompanies = currentCompanies.filter(company => company.id !== id);
              
              patchState(store, {
                companies: filteredCompanies,
                selectedCompany: null,
                isLoading: false,
                error: null
              });
              return true;
            }),
            catchError((error: HttpErrorResponse) => {
              const errorMessage = error.error?.message || `Failed to delete company with ID ${id}`;
              patchState(store, {
                isLoading: false,
                error: errorMessage
              });
              return of(false);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    // Select a company
    selectCompany(company: Company | null): void {
      patchState(store, { selectedCompany: company });
    },

    // Clear error
    clearError(): void {
      patchState(store, { error: null });
    }
  }))
);
