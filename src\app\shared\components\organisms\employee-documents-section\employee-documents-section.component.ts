import { Component, Input, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';

import { ModalService } from '../../../../core/services/modal.service';
import { ToastService } from '../../../services/toast.service';
import { EmployeeDocumentsService } from '../../../../core/services/employee-documents.service';

export interface EmployeeDocument {
  id: number;
  employee_id: number;
  name: string; // Updated to match API
  document_name: string; // Keep for backward compatibility
  document_type: string;
  description?: string; // Added description field
  file_url: string;
  document?: string; // API file field
  uploaded_on: string;
  file_size?: number;
  created_at: string;
  updated_at: string;
}

export interface DocumentType {
  value: string;
  label: string;
}

@Component({
  selector: 'app-employee-documents-section',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './employee-documents-section.component.html',
  styleUrls: ['./employee-documents-section.component.scss']
})
export class EmployeeDocumentsSectionComponent implements OnInit {
  @Input() employeeId!: number;

  private fb = inject(FormBuilder);
  private documentsService = inject(EmployeeDocumentsService);
  private modalService = inject(ModalService);
  private toastService = inject(ToastService);

  // Data
  documents: EmployeeDocument[] = [];
  documentTypes: DocumentType[] = [
    { value: 'resume', label: 'Resume/CV' },
    { value: 'id_proof', label: 'ID Proof' },
    { value: 'address_proof', label: 'Address Proof' },
    { value: 'qualification_certificate', label: 'Education Certificate' },
    { value: 'experience_certificate', label: 'Experience Letter' },
    { value: 'offer_letter', label: 'Offer Letter' },
    { value: 'contract', label: 'Contract' },
    { value: 'bank_details', label: 'Bank Details' },
    { value: 'other', label: 'Other' }
  ];

  // State
  isLoading = false;
  isModalOpen = false;
  isEditing = false;
  selectedDocument: EmployeeDocument | null = null;
  selectedFile: File | null = null;

  // Form
  documentForm: FormGroup;

  constructor() {
    this.documentForm = this.fb.group({
      document_name: ['', [Validators.required, Validators.minLength(2)]],
      document_type: ['', Validators.required],
      description: [''], // Optional description field
      file: [null, Validators.required]
    });
  }

  ngOnInit(): void {
    if (this.employeeId) {
      this.loadDocuments();
    }
  }

  loadDocuments(): void {
    this.isLoading = true;
    this.documentsService.getEmployeeDocuments(this.employeeId).subscribe({
      next: (response: any) => {
        this.documents = response.results || response;
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading documents:', error);
        this.toastService.error('Failed to load documents');
        this.isLoading = false;
      }
    });
  }

  openAddModal(): void {
    this.isEditing = false;
    this.selectedDocument = null;
    this.documentForm.reset();
    this.documentForm.get('file')?.setValidators([Validators.required]);
    this.documentForm.get('file')?.updateValueAndValidity();
    this.selectedFile = null;
    this.isModalOpen = true;
  }

  openEditModal(document: EmployeeDocument): void {
    this.isEditing = true;
    this.selectedDocument = document;
    this.documentForm.patchValue({
      document_name: document.document_name,
      document_type: document.document_type,
      description: document.description || ''
    });
    // File is optional for edit
    this.documentForm.get('file')?.clearValidators();
    this.documentForm.get('file')?.updateValueAndValidity();
    this.selectedFile = null;
    this.isModalOpen = true;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.isEditing = false;
    this.selectedDocument = null;
    this.documentForm.reset();
    this.selectedFile = null;
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.processSelectedFile(file);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    const target = event.currentTarget as HTMLElement;
    target.classList.add('drag-over');
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    const target = event.currentTarget as HTMLElement;
    target.classList.remove('drag-over');
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    const target = event.currentTarget as HTMLElement;
    target.classList.remove('drag-over');

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.processSelectedFile(files[0]);
    }
  }

  removeSelectedFile(event: Event): void {
    event.stopPropagation();
    this.selectedFile = null;
    this.documentForm.patchValue({ file: null });

    // Reset the file input
    const fileInput = document.getElementById('file') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  private processSelectedFile(file: File): void {
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      this.toastService.error('File size must be less than 10MB');
      return;
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/jpg',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!allowedTypes.includes(file.type)) {
      this.toastService.error('Only PDF, DOC, DOCX, and image files are allowed');
      return;
    }

    this.selectedFile = file;
    this.documentForm.patchValue({ file: file });
    this.documentForm.get('file')?.markAsTouched();
  }

  submitForm(): void {
    if (this.documentForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    // For new documents, file is required
    if (!this.isEditing && !this.selectedFile) {
      this.toastService.error('Please select a file to upload');
      return;
    }

    const formData = new FormData();

    // Based on Document Management API specification
    formData.append('employee', this.employeeId.toString());
    formData.append('document_name', this.documentForm.get('document_name')?.value || '');
    formData.append('document_type', this.documentForm.get('document_type')?.value || '');

    // Add optional description if provided
    const description = this.documentForm.get('description')?.value;
    if (description) {
      formData.append('description', description);
    }

    if (this.selectedFile) {
      formData.append('file', this.selectedFile, this.selectedFile.name);
    }

    // Log payload for debugging
    console.log('Document upload payload:');
    console.log('- employee_id:', this.employeeId);
    console.log('- name:', this.documentForm.get('document_name')?.value);
    console.log('- document_type:', this.documentForm.get('document_type')?.value);
    console.log('- file:', this.selectedFile?.name);

    this.isLoading = true;

    if (this.isEditing && this.selectedDocument) {
      this.documentsService.updateDocument(this.selectedDocument.id, formData).subscribe({
        next: () => {
          this.toastService.success('Document updated successfully');
          this.loadDocuments();
          this.closeModal();
        },
        error: (error: any) => {
          console.error('Error updating document:', error);
          this.toastService.error('Failed to update document');
          this.isLoading = false;
        }
      });
    } else {
      this.documentsService.uploadDocument(formData).subscribe({
        next: () => {
          this.toastService.success('Document uploaded successfully');
          this.loadDocuments();
          this.closeModal();
        },
        error: (error: any) => {
          console.error('Error uploading document:', error);
          this.toastService.error('Failed to upload document');
          this.isLoading = false;
        }
      });
    }
  }

  async deleteDocument(document: EmployeeDocument): Promise<void> {
    const confirmed = await this.modalService.confirm({
      title: 'Delete Document',
      message: `Are you sure you want to delete "${document.document_name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel'
    });

    if (confirmed) {
      this.isLoading = true;
      this.documentsService.deleteDocument(document.id).subscribe({
        next: () => {
          this.toastService.success('Document deleted successfully');
          this.loadDocuments();
        },
        error: (error: any) => {
          console.error('Error deleting document:', error);
          this.toastService.error('Failed to delete document');
          this.isLoading = false;
        }
      });
    }
  }

  viewDocument(doc: EmployeeDocument): void {
    if (doc.file_url) {
      // Open in new tab for viewing
      const newWindow = window.open(doc.file_url, '_blank');
      if (!newWindow) {
        // If popup was blocked, show message and try alternative
        this.toastService.warning('Popup blocked. Trying alternative method...');
        // Fallback: navigate in same tab
        window.location.href = doc.file_url;
      }
    } else {
      this.toastService.error('Document file not available for viewing. The file may not have been uploaded properly.');
      console.error('Document file_url is null or empty:', doc);
    }
  }

  downloadDocument(doc: EmployeeDocument): void {
    if (doc.file_url) {
      const fileName = doc.document_name || doc.name || 'document';
      const fileExtension = this.getFileExtension(doc.file_url);
      const downloadName = fileExtension ? fileName : `${fileName}.pdf`;

      // Show loading message
      this.toastService.info(`Preparing download for ${downloadName}...`);

      // Try fetch method first for better control
      fetch(doc.file_url)
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.blob();
        })
        .then(blob => {
          // Create blob URL and download
          const blobUrl = window.URL.createObjectURL(blob);
          const link = window.document.createElement('a');
          link.href = blobUrl;
          link.download = downloadName;
          link.style.display = 'none';

          window.document.body.appendChild(link);
          link.click();
          window.document.body.removeChild(link);

          // Clean up blob URL
          window.URL.revokeObjectURL(blobUrl);

          this.toastService.success(`Downloaded ${downloadName} successfully`);
        })
        .catch(error => {
          console.error('Fetch download failed, trying direct download:', error);
          // Fallback to direct download method
          this.directDownload(doc.file_url, downloadName);
        });
    } else {
      this.toastService.error('Document file not available for download');
    }
  }

  private directDownload(url: string, fileName: string): void {
    try {
      const link = window.document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';

      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);

      this.toastService.success(`Download initiated for ${fileName}`);
    } catch (error) {
      console.error('Direct download failed:', error);
      this.toastService.error('Download failed. Please try again or contact support.');
    }
  }

  private getFileExtension(url: string): boolean {
    if (!url) return false;
    const fileName = url.split('/').pop() || '';
    return fileName.includes('.');
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return 'Invalid Date';
    }
  }

  formatFileSize(bytes?: number): string {
    if (!bytes) return 'Unknown';

    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  getDocumentTypeLabel(type: string): string {
    const docType = this.documentTypes.find(dt => dt.value === type);
    return docType ? docType.label : type;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.documentForm.controls).forEach(key => {
      const control = this.documentForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.documentForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return `${fieldName.replace('_', ' ')} is required`;
      }
      if (field.errors['minlength']) {
        return `${fieldName.replace('_', ' ')} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }
}
