import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

// Import atomic components
import { ButtonComponent } from '../../atoms/button/button.component';

// Import interfaces
import { EmployeeOKR } from '../../../../core/services/employee-management.service';

@Component({
  selector: 'app-okr-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonComponent
  ],
  templateUrl: './okr-form.component.html',
  styleUrls: ['./okr-form.component.scss']
})
export class OKRFormComponent implements OnInit, OnDestroy {
  @Input() employeeId: number | null = null;
  @Input() okrData: EmployeeOKR | null = null;
  @Input() isEdit: boolean = false;
  @Output() formSubmit = new EventEmitter<Partial<EmployeeOKR>>();
  @Output() formCancel = new EventEmitter<void>();

  private destroy$ = new Subject<void>();
  private fb = inject(FormBuilder);

  okrForm: FormGroup;
  isSubmitting = false;

  // Quarter options
  quarters = [
    { value: 'Q1', label: 'Q1 (Jan-Mar)' },
    { value: 'Q2', label: 'Q2 (Apr-Jun)' },
    { value: 'Q3', label: 'Q3 (Jul-Sep)' },
    { value: 'Q4', label: 'Q4 (Oct-Dec)' }
  ];

  // Status options
  statusOptions = [
    { value: 'draft', label: 'Draft' },
    { value: 'active', label: 'Active' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  // Current year and year options
  currentYear = new Date().getFullYear();
  yearOptions = Array.from({ length: 5 }, (_, i) => this.currentYear - 2 + i);

  constructor() {
    this.okrForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.okrData && this.isEdit) {
      this.populateForm(this.okrData);
    } else {
      // Set default values for new OKR
      const currentQuarter = this.getCurrentQuarter();
      this.okrForm.patchValue({
        quarter: currentQuarter,
        year: this.currentYear,
        status: 'draft',
        progress: 0,
        score: 0
      });
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      employee_id: [this.employeeId, [Validators.required]],
      title: ['', [Validators.required, Validators.maxLength(200)]],
      description: ['', [Validators.maxLength(1000)]],
      objectives: this.fb.array([this.createObjectiveControl()]),
      key_results: this.fb.array([this.createKeyResultControl()]),
      quarter: ['', [Validators.required]],
      year: [this.currentYear, [Validators.required]],
      status: ['draft', [Validators.required]],
      progress: [0, [Validators.min(0), Validators.max(100)]],
      score: [0, [Validators.min(0), Validators.max(10)]]
    });
  }

  private createObjectiveControl(): FormGroup {
    return this.fb.group({
      text: ['', [Validators.required, Validators.maxLength(500)]]
    });
  }

  private createKeyResultControl(): FormGroup {
    return this.fb.group({
      text: ['', [Validators.required, Validators.maxLength(500)]]
    });
  }

  private populateForm(okr: EmployeeOKR): void {
    // Clear existing arrays
    this.clearFormArray('objectives');
    this.clearFormArray('key_results');

    // Populate basic fields
    this.okrForm.patchValue({
      employee_id: okr.employee_id,
      title: okr.title,
      description: okr.description,
      quarter: okr.quarter,
      year: okr.year,
      status: okr.status,
      progress: okr.progress,
      score: okr.score
    });

    // Populate objectives
    if (okr.objectives && okr.objectives.length > 0) {
      okr.objectives.forEach(objective => {
        this.addObjective(objective);
      });
    }

    // Populate key results
    if (okr.key_results && okr.key_results.length > 0) {
      okr.key_results.forEach(keyResult => {
        this.addKeyResult(keyResult);
      });
    }
  }

  private clearFormArray(arrayName: string): void {
    const formArray = this.okrForm.get(arrayName) as FormArray;
    while (formArray.length !== 0) {
      formArray.removeAt(0);
    }
  }

  private getCurrentQuarter(): string {
    const month = new Date().getMonth() + 1;
    if (month <= 3) return 'Q1';
    if (month <= 6) return 'Q2';
    if (month <= 9) return 'Q3';
    return 'Q4';
  }

  // Objectives management
  get objectives(): FormArray {
    return this.okrForm.get('objectives') as FormArray;
  }

  addObjective(text: string = ''): void {
    const objectiveControl = this.createObjectiveControl();
    if (text) {
      objectiveControl.patchValue({ text });
    }
    this.objectives.push(objectiveControl);
  }

  removeObjective(index: number): void {
    if (this.objectives.length > 1) {
      this.objectives.removeAt(index);
    }
  }

  // Key Results management
  get keyResults(): FormArray {
    return this.okrForm.get('key_results') as FormArray;
  }

  addKeyResult(text: string = ''): void {
    const keyResultControl = this.createKeyResultControl();
    if (text) {
      keyResultControl.patchValue({ text });
    }
    this.keyResults.push(keyResultControl);
  }

  removeKeyResult(index: number): void {
    if (this.keyResults.length > 1) {
      this.keyResults.removeAt(index);
    }
  }

  onSubmit(): void {
    if (this.okrForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      
      const formValue = this.okrForm.value;
      const okrData: Partial<EmployeeOKR> = {
        employee_id: formValue.employee_id,
        title: formValue.title,
        description: formValue.description,
        objectives: formValue.objectives.map((obj: any) => obj.text).filter((text: string) => text.trim()),
        key_results: formValue.key_results.map((kr: any) => kr.text).filter((text: string) => text.trim()),
        quarter: formValue.quarter,
        year: formValue.year,
        status: formValue.status,
        progress: formValue.progress,
        score: formValue.score
      };

      this.formSubmit.emit(okrData);
    }
  }

  onCancel(): void {
    this.formCancel.emit();
  }

  // Getter methods for template
  get titleControl() {
    return this.okrForm.get('title');
  }

  get descriptionControl() {
    return this.okrForm.get('description');
  }

  get quarterControl() {
    return this.okrForm.get('quarter');
  }

  get yearControl() {
    return this.okrForm.get('year');
  }

  get statusControl() {
    return this.okrForm.get('status');
  }

  get progressControl() {
    return this.okrForm.get('progress');
  }

  get scoreControl() {
    return this.okrForm.get('score');
  }

  // Validation helper methods
  isFieldInvalid(fieldName: string): boolean {
    const field = this.okrForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isArrayFieldInvalid(arrayName: string, index: number, fieldName: string): boolean {
    const field = this.okrForm.get([arrayName, index, fieldName]);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.okrForm.get(fieldName);
    if (field && field.errors) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} is required`;
      }
      if (field.errors['maxlength']) {
        return `${this.getFieldLabel(fieldName)} must be less than ${field.errors['maxlength'].requiredLength} characters`;
      }
      if (field.errors['min']) {
        return `${this.getFieldLabel(fieldName)} must be greater than or equal to ${field.errors['min'].min}`;
      }
      if (field.errors['max']) {
        return `${this.getFieldLabel(fieldName)} must be less than or equal to ${field.errors['max'].max}`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      title: 'Title',
      description: 'Description',
      quarter: 'Quarter',
      year: 'Year',
      status: 'Status',
      progress: 'Progress',
      score: 'Score'
    };
    return labels[fieldName] || fieldName;
  }
}
