@import '../../../../../styles/variables/_colors';

.menu-group {
  margin-bottom: 8px;
  padding: 0;

  .menu-group-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 16px 8px 16px;
    font-size: 11px;
    font-weight: $font-weight-medium;
    text-transform: uppercase;
    color: $gray-500;
    margin-bottom: 4px;
    letter-spacing: 0.5px;

    &:hover {
      color: $dark;
    }

    i {
      font-size: 12px;
      transition: transform 0.2s ease;
    }
  }

  .menu-group-items {
    padding-left: 0;
    padding-bottom: 0;
  }

  // Special styling for the LAYOUT group
  &:nth-child(2) {
    margin-top: 32px;
  }
}

// Styles for collapsed side menu
:host-context(.side-menu.collapsed) {
  .menu-group {
    margin-bottom: 0;
    padding: 0;

    .menu-group-items {
      max-height: none;
      overflow: visible;
      padding-bottom: 0;
    }
  }
}



