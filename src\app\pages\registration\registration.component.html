<div class="registration__container">
  <div class="registration__wrapper">
    <div class="registration__left">
      <div class="registration__left-content">
        <h1 class="registration__headline">Join our community of HR professionals.</h1>

        <div class="registration__image">
          <img src="assets/images/login/hr-modern-illustration.svg" alt="HR Modern Dashboard">
        </div>

        <p class="registration__subheadline">Start your journey to better HR management today.</p>
      </div>
    </div>

    <div class="registration__right">
      <div class="registration__right-content">
        <div class="registration__header">
          <h2>Create Account</h2>
          <p>Please fill in your details to register</p>
        </div>

        <form [formGroup]="registrationForm" class="registration__form">
          <div class="form__row">
            <div class="form__group">
              <label for="firstName">First Name</label>
              <div class="form__input-wrapper">
                <input
                  class="form__input"
                  type="text"
                  id="firstName"
                  formControlName="firstName"
                  placeholder="Enter your first name"
                >
                <div class="form__icon">
                  <i class="fa fa-user"></i>
                </div>
              </div>
              <div class="form__error" *ngIf="hasError('firstName', 'required')">
                First name is required
              </div>
            </div>

            <div class="form__group">
              <label for="lastName">Last Name</label>
              <div class="form__input-wrapper">
                <input
                  class="form__input"
                  type="text"
                  id="lastName"
                  formControlName="lastName"
                  placeholder="Enter your last name"
                >
                <div class="form__icon">
                  <i class="fa fa-user"></i>
                </div>
              </div>
              <div class="form__error" *ngIf="hasError('lastName', 'required')">
                Last name is required
              </div>
            </div>
          </div>

          <div class="form__group form__group--full-width">
            <label for="companyName">Company Name</label>
            <div class="form__input-wrapper">
              <input
                class="form__input"
                type="text"
                id="companyName"
                formControlName="companyName"
                placeholder="Enter your company name"
              >
              <div class="form__icon">
                <i class="fa fa-building"></i>
              </div>
            </div>
            <div class="form__error" *ngIf="hasError('companyName', 'required')">
              Company name is required
            </div>
          </div>

          <div class="form__group form__group--full-width">
            <label for="email">Email Address</label>
            <div class="form__input-wrapper">
              <input
                class="form__input"
                type="email"
                id="email"
                formControlName="email"
                placeholder="Enter your email address"
              >
              <div class="form__icon">
                <i class="fa fa-envelope"></i>
              </div>
            </div>
            <div class="form__error" *ngIf="hasError('email', 'required')">
              Email is required
            </div>
            <div class="form__error" *ngIf="hasError('email', 'email')">
              Please enter a valid email address
            </div>
          </div>



          <div class="form__row">
            <div class="form__group">
              <label for="password">Password</label>
              <div class="form__input-wrapper">
                <input
                  class="form__input"
                  [type]="showPassword ? 'text' : 'password'"
                  id="password"
                  formControlName="password"
                  placeholder="Enter your password"
                >
                <button
                  type="button"
                  class="form__password-toggle"
                  (click)="togglePasswordVisibility()"
                >
                  <i [class]="showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
                </button>
              </div>
              <div class="form__error" *ngIf="hasError('password', 'required')">
                Password is required
              </div>
              <div class="form__error" *ngIf="hasError('password', 'minlength')">
                Password must be at least 8 characters
              </div>
            </div>

            <div class="form__group">
              <label for="confirmPassword">Confirm Password</label>
              <div class="form__input-wrapper">
                <input
                  class="form__input"
                  [type]="showConfirmPassword ? 'text' : 'password'"
                  id="confirmPassword"
                  formControlName="confirmPassword"
                  placeholder="Confirm your password"
                >
                <button
                  type="button"
                  class="form__password-toggle"
                  (click)="toggleConfirmPasswordVisibility()"
                >
                  <i [class]="showConfirmPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
                </button>
              </div>
              <div class="form__error" *ngIf="hasError('confirmPassword', 'required')">
                Please confirm your password
              </div>
              <div class="form__error" *ngIf="hasPasswordMatchError()">
                Passwords do not match
              </div>
            </div>
          </div>

          <div class="form__options">
            <div class="terms__agreement">
              <input type="checkbox" id="terms" formControlName="termsAgreed">
              <label for="terms">I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a></label>
            </div>
          </div>

          <div class="form__error form__error--global" *ngIf="error()">
            {{ error() }}
            <button type="button" class="error__close" (click)="clearError()">×</button>
          </div>

          <button
            type="button"
            class="button__register"
            [disabled]="isLoading()"
            (click)="register()"
          >
            <ng-container *ngIf="isLoading(); else notLoading">
              <i class="fa fa-spinner fa-spin"></i> Creating Account...
            </ng-container>
            <ng-template #notLoading>
              Create Account
            </ng-template>
          </button>

          <div class="account__link">
            <span>Already have an account?</span>
            <a href="#" (click)="navigateToLogin($event)">Sign In</a>
          </div>

          <div class="registration__divider">
            <span>Or</span>
          </div>

          <div class="social__registration">
            <button type="button" class="button__social button__social--microsoft" (click)="registerWithMicrosoft()">
              <i class="fa fa-windows"></i>
            </button>

            <button type="button" class="button__social button__social--google" (click)="registerWithGoogle()">
              <i class="fa fa-google"></i>
            </button>

            <button type="button" class="button__social button__social--apple" (click)="registerWithApple()">
              <i class="fa fa-apple"></i>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
