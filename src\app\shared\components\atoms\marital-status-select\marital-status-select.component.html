<div class="marital-status-select" [class]="sizeClass + ' ' + variantClass">
  <!-- Label -->
  <label *ngIf="label" class="marital-status-select__label" [class.marital-status-select__label--required]="required">
    {{ label }}
    <span *ngIf="required" class="marital-status-select__required">*</span>
  </label>

  <!-- Select Container -->
  <div class="marital-status-select__container" 
       [class.marital-status-select__container--disabled]="disabled"
       [class.marital-status-select__container--error]="hasError"
       [class.marital-status-select__container--open]="isOpen">
    
    <!-- Select Button -->
    <button type="button" 
            class="marital-status-select__button"
            [disabled]="disabled"
            (click)="toggleDropdown()"
            (blur)="onBlur()"
            [attr.aria-expanded]="isOpen"
            [attr.aria-haspopup]="true"
            [attr.aria-label]="label">
      
      <!-- Selected Value -->
      <div class="marital-status-select__value" *ngIf="selectedOption; else placeholderTemplate">
        <span class="marital-status-select__icon" *ngIf="selectedOption.icon">{{ selectedOption.icon }}</span>
        <span class="marital-status-select__text">{{ selectedOption.label }}</span>
      </div>
      
      <!-- Placeholder -->
      <ng-template #placeholderTemplate>
        <div class="marital-status-select__placeholder">
          <span class="marital-status-select__text">{{ placeholder }}</span>
        </div>
      </ng-template>

      <!-- Clear Button -->
      <button type="button" 
              class="marital-status-select__clear"
              *ngIf="value && !disabled"
              (click)="clearSelection(); $event.stopPropagation()"
              [attr.aria-label]="'Clear selection'">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>

      <!-- Dropdown Arrow -->
      <div class="marital-status-select__arrow" [class.marital-status-select__arrow--open]="isOpen">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="6,9 12,15 18,9"></polyline>
        </svg>
      </div>
    </button>

    <!-- Dropdown Menu -->
    <div class="marital-status-select__dropdown" *ngIf="isOpen" role="listbox">
      <div class="marital-status-select__options">
        <button type="button"
                class="marital-status-select__option"
                *ngFor="let option of maritalStatusOptions"
                [class.marital-status-select__option--selected]="value === option.value"
                (click)="selectOption(option)"
                role="option"
                [attr.aria-selected]="value === option.value">
          <span class="marital-status-select__option-icon" *ngIf="option.icon">{{ option.icon }}</span>
          <span class="marital-status-select__option-text">{{ option.label }}</span>
          
          <!-- Selected Indicator -->
          <div class="marital-status-select__option-check" *ngIf="value === option.value">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="20,6 9,17 4,12"></polyline>
            </svg>
          </div>
        </button>
      </div>
    </div>
  </div>

  <!-- Error Message -->
  <div class="marital-status-select__error" *ngIf="hasError" role="alert">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="12" y1="8" x2="12" y2="12"></line>
      <line x1="12" y1="16" x2="12.01" y2="16"></line>
    </svg>
    <span>{{ error }}</span>
  </div>
</div>
