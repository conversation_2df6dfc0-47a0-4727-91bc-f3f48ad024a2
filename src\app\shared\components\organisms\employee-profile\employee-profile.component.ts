import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-profile',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="profile-card">
      <div class="profile-header">
        <div class="avatar-container">
          <div class="avatar">
            <img *ngIf="employee?.avatar" [src]="employee?.avatar" [alt]="employee?.name">
            <span *ngIf="!employee?.avatar" class="initials">{{ getInitials(employee?.name) }}</span>
          </div>
        </div>
        <div class="profile-info">
          <h3 class="employee-name">{{ employee?.name }}</h3>
          <p class="employee-position">{{ employee?.position }}</p>
        </div>
      </div>

      <div class="profile-details">
        <div class="detail-item">
          <div class="detail-label">
            <i class="fa fa-envelope"></i>
            <span>Email</span>
          </div>
          <div class="detail-value">{{ employee?.email }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">
            <i class="fa fa-phone"></i>
            <span>Phone</span>
          </div>
          <div class="detail-value">{{ employee?.mobile }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">
            <i class="fa fa-building"></i>
            <span>Department</span>
          </div>
          <div class="detail-value">{{ employee?.department }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">
            <i class="fa fa-map-marker-alt"></i>
            <span>Location</span>
          </div>
          <div class="detail-value">{{ employee?.location }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">
            <i class="fa fa-id-card"></i>
            <span>Employee ID</span>
          </div>
          <div class="detail-value">EMP-{{ employee?.id }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">
            <i class="fa fa-calendar"></i>
            <span>Join Date</span>
          </div>
          <div class="detail-value">{{ employee?.joinDate | date:'dd MMM yyyy' }}</div>
        </div>
      </div>

      <div class="profile-actions">
        <button class="action-btn">
          <i class="fa fa-user"></i>
          <span>View Profile</span>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .profile-card {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .profile-header {
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: #343a40;
      color: white;
    }

    .avatar-container {
      margin-right: 16px;
    }

    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #ff6b35;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: 600;
      color: white;
      overflow: hidden;
    }

    .avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .profile-info {
      flex: 1;
    }

    .employee-name {
      margin: 0 0 4px 0;
      font-size: 18px;
      font-weight: 600;
    }

    .employee-position {
      margin: 0;
      font-size: 14px;
      opacity: 0.8;
    }

    .profile-details {
      padding: 16px;
      flex: 1;
    }

    .detail-item {
      margin-bottom: 12px;
    }

    .detail-label {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      color: #6c757d;
      font-size: 13px;
    }

    .detail-label i {
      margin-right: 8px;
      width: 16px;
    }

    .detail-value {
      font-size: 14px;
      color: #343a40;
      font-weight: 500;
    }

    .profile-actions {
      padding: 16px;
      border-top: 1px solid #e9ecef;
    }

    .action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 8px 16px;
      background-color: #ff6b35;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      gap: 8px;
    }

    .action-btn:hover {
      background-color: #e85a2a;
    }
  `]
})
export class EmployeeProfileComponent {
  @Input() employee: any;

  getInitials(name: string): string {
    if (!name) return '';

    const nameParts = name.split(' ');
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    }

    return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
  }
}
