<!-- Loading State -->
<div *ngIf="isLoading()" class="loading-container">
  <div class="spinner"></div>
  <p>Loading department details...</p>
</div>

<!-- Error State -->
<div *ngIf="error() && !isLoading()" class="error-container">
  <app-icon name="exclamation-triangle" size="xl" class="error-icon"></app-icon>
  <h3>Error Loading Department</h3>
  <p>{{ error() }}</p>
  <app-button
    label="Back to Departments"
    variant="primary"
    icon="arrow-left"
    (onClick)="goBack()">
  </app-button>
</div>

<!-- Department Detail Content -->
<div *ngIf="departmentDetail() && !isLoading()" class="department-detail">
  <!-- Breadcrumb Navigation -->
  <nav class="breadcrumb">
    <a routerLink="/app/departments" class="breadcrumb-link">
      <app-icon name="arrow-left" size="sm"></app-icon>
      Departments
    </a>
    <span class="breadcrumb-separator">/</span>
    <span class="breadcrumb-current">{{ departmentDetail()?.name }}</span>
  </nav>

  <!-- Header Section -->
  <div class="header-section">
    <div class="header-content">
      <div class="title-section">
        <h1 class="department-title">{{ departmentDetail()?.name }}</h1>
        <div class="status-badge" [class]="getStatusClass(departmentDetail()?.status || '')">
          {{ departmentDetail()?.status | titlecase }}
        </div>
      </div>
      <div class="action-buttons">
        <app-button
          label="Edit"
          variant="primary"
          icon="edit"
          (onClick)="editDepartment()">
        </app-button>
        <app-button
          label="Delete"
          variant="danger"
          icon="trash"
          (onClick)="deleteDepartment()">
        </app-button>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="statistics-section">
    <div class="stat-card">
      <div class="stat-content">
        <div class="stat-icon">
          <app-icon name="users" size="lg"></app-icon>
        </div>
        <div class="stat-info">
          <h3>{{ getEmployeeCount() }}</h3>
          <p>Total Employees</p>
        </div>
        <div class="stat-trend">
          <div class="trend-indicator">
            <app-icon name="arrow-up" size="xs"></app-icon>
            +12%
          </div>
        </div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-content">
        <div class="stat-icon active">
          <app-icon name="check-circle" size="lg"></app-icon>
        </div>
        <div class="stat-info">
          <h3>{{ getActiveEmployeesCount() }}</h3>
          <p>Active Employees</p>
        </div>
        <div class="stat-trend">
          <div class="trend-indicator">
            <app-icon name="arrow-up" size="xs"></app-icon>
            +8%
          </div>
        </div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-content">
        <div class="stat-icon inactive">
          <app-icon name="times-circle" size="lg"></app-icon>
        </div>
        <div class="stat-info">
          <h3>{{ getInactiveEmployeesCount() }}</h3>
          <p>Inactive Employees</p>
        </div>
        <div class="stat-trend">
          <div class="trend-indicator">
            <app-icon name="arrow-down" size="xs"></app-icon>
            -4%
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Overview Card -->
  <div class="overview-card">
    <div class="card-header">
      <h2 class="card-title">Department Overview</h2>
    </div>
    <div class="card-content">
      <div class="overview-grid">
        <div class="overview-item">
          <label>Description:</label>
          <p>{{ departmentDetail()?.description || 'No description provided' }}</p>
        </div>
        
        <div class="overview-item">
          <label>Manager:</label>
          <div class="manager-info">
            <div *ngIf="departmentDetail()?.manager_name; else noManager" class="manager-details">
              <div class="manager-avatar">
                <img 
                  src="assets/images/default-avatar.png" 
                  alt="Manager Avatar"
                  (error)="onImageError($event)">
              </div>
              <span class="manager-name">{{ departmentDetail()?.manager_name }}</span>
            </div>
            <ng-template #noManager>
              <span class="no-manager">Not assigned</span>
            </ng-template>
          </div>
        </div>

        <div class="overview-item">
          <label>Created:</label>
          <p>{{ formatDate(departmentDetail()?.created_at || '') }}</p>
        </div>

        <div class="overview-item">
          <label>Last Updated:</label>
          <p>{{ formatDate(departmentDetail()?.updated_at || '') }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Employees Section -->
  <div class="employees-card">
    <div class="card-header">
      <h2 class="card-title">Department Employees</h2>
    </div>
    <div class="card-content">
      <div *ngIf="departmentDetail()?.employees && departmentDetail()!.employees.length > 0; else noEmployees"
           class="employees-grid">
        <div *ngFor="let employee of departmentDetail()?.employees" 
             class="employee-card" 
             (click)="viewEmployeeDetail(employee)">
          <div class="employee-avatar">
            <img 
              [src]="employee.profile_picture || getDefaultAvatar()" 
              [alt]="employee.full_name"
              (error)="onImageError($event)">
          </div>
          <div class="employee-info">
            <h4 class="employee-name">{{ employee.full_name }}</h4>
            <p class="employee-email">{{ employee.email }}</p>
            <p class="employee-position">{{ employee.position }}</p>
            <div class="status-badge" [class]="getStatusClass(employee.status)">
              {{ employee.status | titlecase }}
            </div>
          </div>
          <app-icon name="chevron-right" size="sm" class="view-icon"></app-icon>
        </div>
      </div>
      
      <ng-template #noEmployees>
        <div class="no-employees">
          <app-icon name="users-slash" size="xl" class="no-data-icon"></app-icon>
          <h3>No Employees</h3>
          <p>This department doesn't have any employees assigned yet.</p>
        </div>
      </ng-template>
    </div>
  </div>
</div>
