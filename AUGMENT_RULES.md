# AUGMENT DEVELOPMENT RULES & STANDARDS

This document serves as the **single source of truth** for all development standards, guidelines, and best practices for the Angular HRMS application. All developers must adhere to these rules without exception to ensure consistency, maintainability, and code quality.

---

## 🏗️ **ANGULAR DEVELOPMENT STANDARDS**

### **1. Component Architecture (MANDATORY)**

#### **File Structure Requirements**
- **MANDATORY**: All components must have separate HTML and SCSS files
- **PROHIBITED**: Inline templates or styles in components

```typescript
// ✅ CORRECT - Separate files
@Component({
  selector: 'app-example',
  standalone: true,
  templateUrl: './example.component.html',
  styleUrls: ['./example.component.scss']
})

// ❌ INCORRECT - Inline templates/styles
@Component({
  selector: 'app-example',
  template: `<div>...</div>`,
  styles: [`div { color: red; }`]
})
```

#### **File Naming Convention**
- Component: `example.component.ts`
- Template: `example.component.html`
- Styles: `example.component.scss`
- Service: `example.service.ts`
- Store: `example.state.ts`
- Interface: `example.interface.ts`

### **2. State Management (MANDATORY)**

#### **NgRx Signal Store for ALL API Integrations**
- **MANDATORY**: Use NgRx Signal Store for ALL API calls and state management
- **PROHIBITED**: Direct service calls in components

```typescript
// ✅ CORRECT - NgRx Signal Store
@Injectable()
export class ExampleStore extends ComponentStore<ExampleState> {
  // State management implementation
}

// ❌ INCORRECT - Direct API calls in components
export class ExampleComponent {
  constructor(private apiService: ApiService) {
    this.apiService.getData().subscribe(data => {
      // Direct API call - NOT ALLOWED
    });
  }
}
```

#### **Store Structure Requirements**
- Separate stores for each feature module
- Use signals for reactive programming
- Implement proper error handling in stores
- Include loading states for all async operations

### **3. Atomic Design Structure (MANDATORY)**

#### **Component Organization**
```
src/app/shared/components/
├── atoms/           # Basic building blocks (buttons, inputs, icons)
│   ├── button/
│   ├── input/
│   └── icon/
├── molecules/       # Groups of atoms (search-bar, form-field)
│   ├── search-bar/
│   ├── form-field/
│   └── card-header/
├── organisms/       # Groups of molecules (header, sidebar, data-table)
│   ├── header/
│   ├── sidebar/
│   └── data-table/
├── templates/       # Page layouts
│   ├── main-layout/
│   └── auth-layout/
└── pages/          # Complete pages
    ├── dashboard/
    └── employees/
```

#### **Component Classification Rules**
- **Atoms**: Single-purpose, reusable UI elements
- **Molecules**: Combinations of atoms with specific functionality
- **Organisms**: Complex UI sections with business logic
- **Templates**: Page layouts and structure
- **Pages**: Complete views with routing

### **4. Template Syntax (MANDATORY)**

#### **New Control Flow Syntax**
- **MANDATORY**: Use Angular's new control flow syntax (@if, @for, @switch)
- **PROHIBITED**: Old structural directives (*ngIf, *ngFor, *ngSwitch)

```html
<!-- ✅ CORRECT - New control flow -->
@if (isLoading) {
  <div class="loading">Loading...</div>
} @else {
  <div class="content">{{ data }}</div>
}

@for (item of items; track item.id) {
  <div class="item">{{ item.name }}</div>
}

<!-- ❌ INCORRECT - Old structural directives -->
<div *ngIf="isLoading" class="loading">Loading...</div>
<div *ngFor="let item of items; trackBy: trackByFn" class="item">{{ item.name }}</div>
```

### **5. Forms (MANDATORY)**

#### **Reactive Forms Only**
- **MANDATORY**: Use reactive forms for all form implementations
- **PROHIBITED**: Template-driven forms

```typescript
// ✅ CORRECT - Reactive forms
this.employeeForm = this.fb.group({
  firstName: ['', [Validators.required, Validators.minLength(2)]],
  lastName: ['', [Validators.required, Validators.minLength(2)]],
  email: ['', [Validators.required, Validators.email]]
});
```

---

## 🎨 **UI/UX DESIGN GUIDELINES**

### **6. Augment Design System Standards**

#### **Color Scheme**
- **Primary Color**: Orange (`#ff6b35`) for buttons and primary actions
- **Use SCSS Variables**: Always use color variables from `_colors.scss`
- **PROHIBITED**: Hardcoded colors in components

```scss
// ✅ CORRECT - Use color variables
.button {
  background-color: $primary;
  color: $white;
}

// ❌ INCORRECT - Hardcoded colors
.button {
  background-color: #ff6b35;
  color: #ffffff;
}
```

#### **Design Consistency Requirements**
- **Border Radius**: 16px for cards, 12px for inputs and buttons
- **Shadows**: `0 4px 12px rgba(0, 0, 0, 0.1)` for cards
- **Spacing**: Use consistent 24px padding for main containers
- **Typography**: Inter font family, proper font weights
- **Transitions**: 0.3s ease for all animations

### **7. Component UI Standards**

#### **Follow Established Patterns**
- All modules must have **exactly the same UI styling** as the department module
- Maintain consistent design standards across the application
- Use professional, clean interface design over flashy/gradient themes

#### **Button Standards**
```html
<!-- Primary Action Button -->
<button class="btn btn-primary" (click)="onAction()">
  <i class="fa fa-plus"></i>
  <span>Add Item</span>
</button>

<!-- Secondary Action Button -->
<button class="btn btn-secondary" (click)="onCancel()">
  <span>Cancel</span>
</button>
```

#### **Table Design Requirements**
- Active row highlighting when selected
- Professional action icons
- Search and export functionality in headers
- Consistent spacing and typography
- Responsive design for mobile devices

### **8. SCSS Structure Standards (MANDATORY)**

#### **BEM Methodology**
- **MANDATORY**: Use BEM (Block Element Modifier) naming convention
- **Structure**: `.block__element--modifier`

```scss
// ✅ CORRECT - BEM structure
.employee-card {
  padding: 24px;
  
  &__header {
    margin-bottom: 16px;
  }
  
  &__title {
    font-size: 18px;
    font-weight: 600;
    
    &--highlighted {
      color: $primary;
    }
  }
  
  &__content {
    background: $white;
    border-radius: 16px;
  }
}
```

#### **SCSS Organization**
```scss
// File structure
@use 'sass:color';
@import '../../../../../styles/variables/_colors';

.component-name {
  // Main container styles
  
  &__element {
    // Element styles
  }
  
  &__element--modifier {
    // Modifier styles
  }
  
  // Responsive styles at the end
  @media (max-width: 768px) {
    // Mobile styles
  }
}
```

---

## 🔌 **API INTEGRATION PATTERNS**

### **9. HTTP Service Standards**

#### **Service Organization**
- Organize HTTP functions in separate services
- Services are called from NgRx stores, not directly from components
- Implement proper error handling and loading states

```typescript
// ✅ CORRECT - Service structure
@Injectable()
export class EmployeeService {
  constructor(private http: HttpClient) {}
  
  getEmployees(params?: any): Observable<EmployeeResponse> {
    return this.http.get<EmployeeResponse>(`${this.apiUrl}/employees/`, { params });
  }
  
  createEmployee(employee: EmployeeInput): Observable<Employee> {
    return this.http.post<Employee>(`${this.apiUrl}/employees/`, employee);
  }
}
```

#### **API Response Handling**
- Always handle pagination for listing endpoints
- Implement proper error handling with user-friendly messages
- Use TypeScript interfaces for all API responses
- Include loading states for all async operations

### **10. Authentication & Security**

#### **Token Management**
- Only call refresh token when the token is expired, not immediately after login
- Implement proper token storage and retrieval
- Handle authentication errors gracefully

#### **File Downloads**
- File downloads should stay in the same tab
- **PROHIBITED**: Opening new tabs for document downloads

---

## 📋 **CODE ORGANIZATION RULES**

### **11. Project Structure**

#### **Feature Module Organization**
```
src/app/
├── core/                    # Core services, guards, interceptors
│   ├── services/
│   ├── state/
│   ├── guards/
│   └── interceptors/
├── shared/                  # Shared components, pipes, directives
│   ├── components/
│   ├── pipes/
│   └── directives/
├── features/                # Feature modules
│   ├── employees/
│   ├── departments/
│   └── dashboard/
├── layout/                  # Layout components
└── pages/                   # Page components
```

#### **Monolithic Architecture**
- Project has been converted from microfrontend to monolithic architecture
- Code moved from `projects/shell/src` to root `src` folder
- Maintain modular structure within monolithic approach

### **12. TypeScript Standards**

#### **Type Safety Requirements**
- Use strict TypeScript configuration
- Define proper interfaces for all data structures
- Use proper typing for all variables and functions
- **PROHIBITED**: `any` types (use `unknown` if necessary)

```typescript
// ✅ CORRECT - Proper typing
interface Employee {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  department: Department;
}

// ❌ INCORRECT - Any type
let employee: any = {};
```

### **13. Package Management**

#### **Dependency Management**
- **MANDATORY**: Use package managers (npm, yarn) for dependency management
- **PROHIBITED**: Manually editing package.json, requirements.txt, etc.
- Always use appropriate package manager commands for installing/removing dependencies

---

## 🧪 **TESTING REQUIREMENTS**

### **14. Testing Standards**

#### **Test Coverage Requirements**
- Unit tests for all components and services
- Integration tests for complex workflows
- E2E tests for critical user journeys
- **Minimum 80% code coverage**

#### **Testing Best Practices**
- Write tests before implementing features (TDD approach)
- Test both success and error scenarios
- Mock external dependencies properly
- Use descriptive test names and organize tests logically

### **15. Build Standards**

#### **Build Error Resolution**
- All build errors and bugs must be resolved during implementation
- No warnings or errors should be present in production builds
- Optimize bundle sizes and loading performance

#### **Performance Requirements**
- Use OnPush change detection strategy where possible
- Implement proper trackBy functions for @for loops
- Lazy load feature modules
- Use signals for reactive programming

---

## 🔍 **FILTER & SEARCH STANDARDS**

### **16. List Page Filter Requirements (MANDATORY)**

#### **Required Filter Components**
- **Search Input**: Debounced search (300ms) with clear functionality
- **Multi-Select Dropdowns**: For categorical filters (status, department, etc.)
- **Single Select Dropdowns**: For ordering/sorting options
- **Clear All Filters Button**: Reset all filters with one action

#### **Implementation Standards**
- Use reactive forms (`FormGroup`) for filter management
- Implement debounced search to prevent excessive API calls
- Populate dropdown options dynamically from API endpoints
- Maintain filter state during pagination and navigation
- Include proper loading states and error handling

#### **Dropdown Component Requirements**
- Search functionality within dropdowns
- Multi-select capability where appropriate
- Fixed width for consistency
- Populated with actual data from APIs (not static options)
- "All Departments" option should select/deselect all individual options

---

## 📱 **RESPONSIVE DESIGN & ACCESSIBILITY**

### **17. Responsive Design Standards**

#### **Mobile-First Approach**
- Design for mobile devices first, then scale up
- Ensure all components work on mobile devices
- Use proper touch targets for mobile interactions
- Implement responsive navigation and layouts

#### **Breakpoint Standards**
```scss
// Standard breakpoints
@media (max-width: 768px) {
  // Mobile styles
}

@media (min-width: 769px) and (max-width: 1024px) {
  // Tablet styles
}

@media (min-width: 1025px) {
  // Desktop styles
}
```

### **18. Accessibility Standards**

#### **ARIA and Semantic HTML**
- Include proper ARIA labels for all interactive elements
- Use semantic HTML elements (header, nav, main, section, etc.)
- Ensure keyboard navigation support
- Maintain proper color contrast ratios
- Provide alternative text for images

---

## 🚫 **PROHIBITED PRACTICES**

### **19. Absolutely NOT Allowed**

1. **Inline templates or styles** in components
2. **Direct API calls** in components (bypass NgRx)
3. **Old structural directives** (*ngIf, *ngFor, *ngSwitch)
4. **Components outside atomic design structure**
5. **Inconsistent UI patterns** that don't match existing components
6. **Hardcoded colors or spacing values**
7. **Template-driven forms** (use reactive forms only)
8. **Manual package file editing** (use package managers)
9. **Any types** in TypeScript (use proper typing)
10. **Build errors or warnings** in production

---

## ✅ **DEVELOPMENT CHECKLIST**

### **Before Creating Any Component**
- [ ] Determine atomic design level (atom/molecule/organism/template/page)
- [ ] Create separate HTML and SCSS files
- [ ] Set up NgRx store if API integration is needed
- [ ] Follow established UI patterns from existing components
- [ ] Use new control flow syntax (@if, @for, @switch)
- [ ] Implement proper error handling
- [ ] Add responsive design considerations
- [ ] Follow BEM naming convention for CSS classes
- [ ] Use color variables from `_colors.scss`
- [ ] Implement proper TypeScript interfaces

### **Code Review Checklist**
- [ ] No inline templates or styles
- [ ] NgRx used for all state management
- [ ] Atomic design structure followed
- [ ] New control flow syntax used
- [ ] UI consistency with existing components
- [ ] Proper TypeScript types defined
- [ ] Error handling implemented
- [ ] Responsive design included
- [ ] BEM SCSS methodology followed
- [ ] Color variables used (no hardcoded colors)
- [ ] Proper accessibility attributes
- [ ] Tests written and passing

---

## 📚 **DOCUMENTATION STANDARDS**

### **20. Code Documentation Requirements**

#### **Documentation Standards**
- JSDoc comments for all public methods and complex logic
- README files for each feature module
- Component usage examples and API documentation
- Update AUGMENT_RULES.md when establishing new standards

#### **Comment Standards**
```typescript
/**
 * Creates a new employee with the provided data
 * @param employeeData - The employee information to create
 * @returns Observable<Employee> - The created employee object
 * @throws {ValidationError} When employee data is invalid
 */
createEmployee(employeeData: EmployeeInput): Observable<Employee> {
  // Implementation
}
```

---

## 🎯 **EMPLOYEE MANAGEMENT SPECIFIC STANDARDS**

### **21. Employee Module Requirements**

#### **Employee Management Sections**
- Employee management sections (salary, hikes, OKRs, performance, leave, attendance, training, assets) should be in employee detail pages
- Implement full CRUD operations for all employee-related data
- Use modal dialogs for forms and data entry
- Follow Augment design guidelines for all employee components

#### **Employee API Integration**
- Employee API expects department as ID (not string)
- Use 'phone' instead of 'phone_number'
- Use 'hire_date' instead of 'start_date'
- Require actual image data instead of blob URLs for avatars
- Support pagination for employee listings
- Generate table columns dynamically from API response data

### **22. Department Management Standards**

#### **Department Module Requirements**
- Department Management UI with Angular+NgRx
- Manager dropdown populated from employees endpoint
- Reactive forms with validation
- NgRx state management (actions/effects/reducers/selectors)
- Augment design guidelines with modular layout

#### **Department API Integration**
- Department Management API endpoints: list, create, detail, update, delete
- Proper error handling and validation
- Support for status filtering and manager assignment

---

## 🔄 **ENFORCEMENT & UPDATES**

### **23. Rule Enforcement**

#### **Mandatory Compliance**
- All team members must adhere to these guidelines without exception
- Code reviews must verify compliance with all rules
- Build processes should enforce standards where possible
- Regular audits to ensure ongoing compliance

#### **Rule Updates**
- Add new development rules to this document when establishing new standards
- All rule changes must be communicated to the entire team
- Version control all changes to this document
- Regular reviews and updates based on project evolution

---

## 📋 **QUICK REFERENCE SUMMARY**

### **Core Principles**
1. **Separate HTML/SCSS files** for all components
2. **NgRx Signal Store** for all API integrations
3. **Atomic Design** structure for component organization
4. **New control flow syntax** (@if, @for, @switch)
5. **Orange primary color** (#ff6b35) for UI consistency
6. **BEM methodology** for SCSS naming
7. **Reactive forms** only (no template-driven forms)
8. **Professional, clean design** over flashy themes
9. **Mobile-first responsive design**
10. **80% minimum test coverage**

### **Key APIs**
- **Employee Management**: `http://127.0.0.1:8000/swagger-ui/#/Employee%20Management`
- **Department Management**: `/api/v1/departments/` endpoints
- **Authentication**: Token-based with proper refresh handling

### **Project Architecture**
- **Monolithic structure** (converted from microfrontend)
- **Modular organization** within monolithic approach
- **Feature-based modules** with shared components
- **Core services** for cross-cutting concerns

---

*This document serves as the **single source of truth** for all Augment development standards. All team members must adhere to these guidelines without exception.*

**Last Updated**: December 2024
**Version**: 2.0
**Status**: Active
