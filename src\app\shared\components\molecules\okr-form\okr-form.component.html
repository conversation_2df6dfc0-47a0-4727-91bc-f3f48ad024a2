<div class="okr-form">
  <div class="form-header">
    <h3>{{ isEdit ? 'Edit OKR' : 'Create New OKR' }}</h3>
  </div>

  <form [formGroup]="okrForm" (ngSubmit)="onSubmit()" class="form-content">
    <!-- Basic Information -->
    <div class="form-section">
      <h4>Basic Information</h4>

      <!-- Title -->
      <div class="form-group">
        <label for="title" class="form-label">
          OKR Title <span class="required">*</span>
        </label>
        <input
          id="title"
          type="text"
          formControlName="title"
          class="form-input"
          [class.error]="isFieldInvalid('title')"
          placeholder="Enter OKR title"
          maxlength="200">
        <div class="character-count">
          {{ titleControl?.value?.length || 0 }}/200
        </div>
        @if (isFieldInvalid('title')) {
          <div class="error-message">
            {{ getFieldError('title') }}
          </div>
        }
      </div>

      <!-- Description -->
      <div class="form-group">
        <label for="description" class="form-label">Description</label>
        <textarea
          id="description"
          formControlName="description"
          class="form-textarea"
          [class.error]="isFieldInvalid('description')"
          placeholder="Enter OKR description"
          rows="3"
          maxlength="1000"></textarea>
        <div class="character-count">
          {{ descriptionControl?.value?.length || 0 }}/1000
        </div>
        @if (isFieldInvalid('description')) {
          <div class="error-message">
            {{ getFieldError('description') }}
          </div>
        }
      </div>

      <!-- Quarter and Year -->
      <div class="form-row">
        <div class="form-group">
          <label for="quarter" class="form-label">
            Quarter <span class="required">*</span>
          </label>
          <select
            id="quarter"
            formControlName="quarter"
            class="form-select"
            [class.error]="isFieldInvalid('quarter')">
            <option value="">Select Quarter</option>
            @for (quarter of quarters; track quarter.value) {
              <option [value]="quarter.value">{{ quarter.label }}</option>
            }
          </select>
          @if (isFieldInvalid('quarter')) {
            <div class="error-message">
              {{ getFieldError('quarter') }}
            </div>
          }
        </div>

        <div class="form-group">
          <label for="year" class="form-label">
            Year <span class="required">*</span>
          </label>
          <select
            id="year"
            formControlName="year"
            class="form-select"
            [class.error]="isFieldInvalid('year')">
            @for (year of yearOptions; track year) {
              <option [value]="year">{{ year }}</option>
            }
          </select>
          @if (isFieldInvalid('year')) {
            <div class="error-message">
              {{ getFieldError('year') }}
            </div>
          }
        </div>
      </div>
    </div>

    <!-- Objectives -->
    <div class="form-section">
      <div class="section-header">
        <h4>Objectives</h4>
        <app-button
          [variant]="'secondary'"
          [size]="'sm'"
          [icon]="'add'"
          type="button"
          (click)="addObjective()">
          Add Objective
        </app-button>
      </div>

      <div formArrayName="objectives" class="dynamic-list">
        @for (objective of objectives.controls; track $index; let i = $index) {
          <div [formGroupName]="i" class="dynamic-item">
            <div class="item-content">
              <label [for]="'objective-' + i" class="form-label">
                Objective {{ i + 1 }} <span class="required">*</span>
              </label>
              <div class="input-with-action">
                <textarea
                  [id]="'objective-' + i"
                  formControlName="text"
                  class="form-textarea"
                  [class.error]="isArrayFieldInvalid('objectives', i, 'text')"
                  [placeholder]="'Enter objective ' + (i + 1)"
                  rows="2"
                  maxlength="500"></textarea>
                @if (objectives.length > 1) {
                  <button
                    type="button"
                    class="remove-button"
                    (click)="removeObjective(i)"
                    title="Remove objective">
                    <span class="material-icons">delete</span>
                  </button>
                }
              </div>
              <div class="character-count">
                {{ objective.get('text')?.value?.length || 0 }}/500
              </div>
            </div>
          </div>
        }
      </div>
    </div>

    <!-- Key Results -->
    <div class="form-section">
      <div class="section-header">
        <h4>Key Results</h4>
        <app-button
          [variant]="'secondary'"
          [size]="'sm'"
          [icon]="'add'"
          type="button"
          (click)="addKeyResult()">
          Add Key Result
        </app-button>
      </div>

      <div formArrayName="key_results" class="dynamic-list">
        @for (keyResult of keyResults.controls; track $index; let i = $index) {
          <div [formGroupName]="i" class="dynamic-item">
            <div class="item-content">
              <label [for]="'keyresult-' + i" class="form-label">
                Key Result {{ i + 1 }} <span class="required">*</span>
              </label>
              <div class="input-with-action">
                <textarea
                  [id]="'keyresult-' + i"
                  formControlName="text"
                  class="form-textarea"
                  [class.error]="isArrayFieldInvalid('key_results', i, 'text')"
                  [placeholder]="'Enter key result ' + (i + 1)"
                  rows="2"
                  maxlength="500"></textarea>
                @if (keyResults.length > 1) {
                  <button
                    type="button"
                    class="remove-button"
                    (click)="removeKeyResult(i)"
                    title="Remove key result">
                    <span class="material-icons">delete</span>
                  </button>
                }
              </div>
              <div class="character-count">
                {{ keyResult.get('text')?.value?.length || 0 }}/500
              </div>
            </div>
          </div>
        }
      </div>
    </div>

    <!-- Status and Progress -->
    <div class="form-section">
      <h4>Status & Progress</h4>

      <div class="form-row">
        <!-- Status -->
        <div class="form-group">
          <label for="status" class="form-label">
            Status <span class="required">*</span>
          </label>
          <select
            id="status"
            formControlName="status"
            class="form-select"
            [class.error]="isFieldInvalid('status')">
            @for (status of statusOptions; track status.value) {
              <option [value]="status.value">{{ status.label }}</option>
            }
          </select>
          @if (isFieldInvalid('status')) {
            <div class="error-message">
              {{ getFieldError('status') }}
            </div>
          }
        </div>

        <!-- Progress -->
        <div class="form-group">
          <label for="progress" class="form-label">Progress (%)</label>
          <div class="progress-input-wrapper">
            <input
              id="progress"
              type="number"
              formControlName="progress"
              class="form-input"
              [class.error]="isFieldInvalid('progress')"
              placeholder="0"
              min="0"
              max="100"
              step="1">
            <span class="percentage-symbol">%</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="progressControl?.value || 0"></div>
          </div>
          @if (isFieldInvalid('progress')) {
            <div class="error-message">
              {{ getFieldError('progress') }}
            </div>
          }
        </div>

        <!-- Score -->
        <div class="form-group">
          <label for="score" class="form-label">Score (0-10)</label>
          <input
            id="score"
            type="number"
            formControlName="score"
            class="form-input"
            [class.error]="isFieldInvalid('score')"
            placeholder="0"
            min="0"
            max="10"
            step="0.1">
          @if (isFieldInvalid('score')) {
            <div class="error-message">
              {{ getFieldError('score') }}
            </div>
          }
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <app-button
        [variant]="'secondary'"
        [size]="'md'"
        [disabled]="isSubmitting"
        (click)="onCancel()">
        Cancel
      </app-button>

      <app-button
        [variant]="'primary'"
        [size]="'md'"
        [disabled]="okrForm.invalid || isSubmitting"
        type="submit">
        {{ isEdit ? 'Update OKR' : 'Create OKR' }}
      </app-button>
    </div>
  </form>
</div>
