import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-tasks',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="tasks-container">
      <div class="tasks-header">
        <h3 class="section-title">Tasks</h3>
        <div class="header-actions">
          <button class="action-btn">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      
      <div class="tasks-content">
        <div class="task-item">
          <div class="task-checkbox">
            <input type="checkbox" id="task1">
            <label for="task1"></label>
          </div>
          <div class="task-details">
            <div class="task-title">Project appointment booking</div>
            <div class="task-meta">
              <span class="task-priority high">High</span>
              <div class="task-assignees">
                <div class="assignee-avatar">AL</div>
                <div class="assignee-avatar">SR</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="task-item">
          <div class="task-checkbox">
            <input type="checkbox" id="task2">
            <label for="task2"></label>
          </div>
          <div class="task-details">
            <div class="task-title">Appointment booking with payment</div>
            <div class="task-meta">
              <span class="task-priority medium">Medium</span>
              <div class="task-assignees">
                <div class="assignee-avatar">JD</div>
                <div class="assignee-avatar">SR</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="task-item">
          <div class="task-checkbox">
            <input type="checkbox" id="task3">
            <label for="task3"></label>
          </div>
          <div class="task-details">
            <div class="task-title">Patient and Doctor video chat</div>
            <div class="task-meta">
              <span class="task-priority low">Low</span>
              <div class="task-assignees">
                <div class="assignee-avatar">SR</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="task-item">
          <div class="task-checkbox">
            <input type="checkbox" id="task4">
            <label for="task4"></label>
          </div>
          <div class="task-details">
            <div class="task-title">Private chat module</div>
            <div class="task-meta">
              <span class="task-priority high">High</span>
              <div class="task-assignees">
                <div class="assignee-avatar">AL</div>
                <div class="assignee-avatar">SR</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="task-item">
          <div class="task-checkbox">
            <input type="checkbox" id="task5">
            <label for="task5"></label>
          </div>
          <div class="task-details">
            <div class="task-title">Doctor Live Chat</div>
            <div class="task-meta">
              <span class="task-priority medium">Medium</span>
              <div class="task-assignees">
                <div class="assignee-avatar">SR</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .tasks-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .tasks-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e9ecef;
    }
    
    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }
    
    .action-btn {
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      font-size: 16px;
    }
    
    .tasks-content {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    
    .task-item {
      display: flex;
      align-items: flex-start;
      padding: 12px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
    }
    
    .task-checkbox {
      margin-right: 12px;
      margin-top: 2px;
    }
    
    .task-checkbox input[type="checkbox"] {
      display: none;
    }
    
    .task-checkbox label {
      display: inline-block;
      width: 18px;
      height: 18px;
      border: 2px solid #adb5bd;
      border-radius: 4px;
      position: relative;
      cursor: pointer;
    }
    
    .task-checkbox input[type="checkbox"]:checked + label:after {
      content: '✓';
      position: absolute;
      top: -2px;
      left: 3px;
      color: #28a745;
      font-size: 14px;
    }
    
    .task-details {
      flex: 1;
    }
    
    .task-title {
      font-size: 14px;
      font-weight: 500;
      color: #343a40;
      margin-bottom: 8px;
    }
    
    .task-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .task-priority {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 12px;
    }
    
    .task-priority.high {
      background-color: #ffebee;
      color: #f44336;
    }
    
    .task-priority.medium {
      background-color: #fff8e1;
      color: #ffc107;
    }
    
    .task-priority.low {
      background-color: #e8f5e9;
      color: #4caf50;
    }
    
    .task-assignees {
      display: flex;
    }
    
    .assignee-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #ff6b35;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: 600;
      margin-left: -8px;
      border: 2px solid white;
    }
    
    .assignee-avatar:first-child {
      margin-left: 0;
    }
  `]
})
export class EmployeeTasksComponent {
  @Input() employee: any;
}
