<div class="confirmation-modal" [ngClass]="{'confirmation-modal--open': isOpen}">
  <div class="confirmation-modal__overlay" (click)="onCancel()"></div>
  <div class="confirmation-modal__content">
    <div class="confirmation-modal__header">
      <h2 class="confirmation-modal__title">{{ title }}</h2>
      <button class="confirmation-modal__close" (click)="onCancel()">×</button>
    </div>
    <div class="confirmation-modal__body">
      <p class="confirmation-modal__message">{{ message }}</p>
    </div>
    <div class="confirmation-modal__footer">
      <app-button
        [variant]="'secondary'"
        [label]="cancelButtonText"
        (onClick)="onCancel()">
      </app-button>
      <app-button
        [variant]="'danger'"
        [label]="confirmButtonText"
        (onClick)="onConfirm()">
      </app-button>
    </div>
  </div>
</div>
