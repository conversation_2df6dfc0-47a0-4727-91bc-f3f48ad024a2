import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MaintenanceService } from '../../../core/services/maintenance.service';

@Component({
  selector: 'app-maintenance-control',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './maintenance-control.component.html',
  styleUrls: ['./maintenance-control.component.scss']
})
export class MaintenanceControlComponent implements OnInit {
  isMaintenanceMode = false;
  estimatedCompletion = '';
  maintenanceMessage = '';

  constructor(private maintenanceService: MaintenanceService) {}

  ngOnInit(): void {
    this.maintenanceService.maintenanceMode$.subscribe(isActive => {
      this.isMaintenanceMode = isActive;

      if (isActive) {
        const config = this.maintenanceService.getMaintenanceConfig();
        this.maintenanceMessage = config.message;
        this.estimatedCompletion = this.maintenanceService.getEstimatedCompletionTime();
      }
    });
  }

  toggleMaintenanceMode(): void {
    if (this.isMaintenanceMode) {
      this.maintenanceService.disableMaintenanceMode();
    } else {
      this.maintenanceService.enableMaintenanceMode();
    }
  }

  updateMaintenanceConfig(): void {
    // Calculate end time (2 hours from now)
    const endTime = new Date();
    endTime.setHours(endTime.getHours() + 2);

    this.maintenanceService.disableMaintenanceMode();
    this.maintenanceService.enableMaintenanceMode(endTime, this.maintenanceMessage);
  }
}
