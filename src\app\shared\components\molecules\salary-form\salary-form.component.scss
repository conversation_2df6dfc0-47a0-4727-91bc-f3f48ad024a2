@import '../../../../../styles/variables/_colors';

.salary-form {
  background: $white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .form-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, $primary, $primary-light);
    color: $white;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: $font-weight-semibold;
    }
  }

  .form-content {
    padding: 2rem;

    .form-group {
      margin-bottom: 1.5rem;

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: $font-weight-medium;
        color: $text-primary;
        font-size: 0.9rem;

        .required {
          color: $error;
          margin-left: 0.25rem;
        }
      }

      .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .currency-symbol {
          position: absolute;
          left: 1rem;
          color: $text-secondary;
          font-weight: $font-weight-medium;
          z-index: 1;
        }

        .form-input {
          width: 100%;
          padding: 0.75rem 1rem;
          padding-left: 2.5rem;
          border: 2px solid $border-light;
          border-radius: 8px;
          font-size: 1rem;
          transition: all 0.3s ease;
          background-color: $white;

          &:focus {
            outline: none;
            border-color: $primary;
            box-shadow: 0 0 0 3px rgba($primary, 0.1);
          }

          &.error {
            border-color: $error;
            box-shadow: 0 0 0 3px rgba($error, 0.1);
          }

          &.calculated {
            background-color: $background-light;
            color: $text-secondary;
            cursor: not-allowed;
          }

          &::placeholder {
            color: $text-secondary;
          }

          // Remove number input arrows
          &[type="number"] {
            -moz-appearance: textfield;

            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }
          }
        }
      }

      .error-message {
        margin-top: 0.5rem;
        color: $error;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;

        &::before {
          content: '⚠';
          font-size: 0.9rem;
        }
      }

      .field-note {
        margin-top: 0.5rem;
        color: $text-secondary;
        font-size: 0.8rem;
        font-style: italic;
      }
    }

    .calculated-fields {
      background: $background-light;
      padding: 1.5rem;
      border-radius: 12px;
      margin: 1.5rem 0;
      border-left: 4px solid $primary;

      .form-group {
        margin-bottom: 1rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 2rem;
      padding-top: 1.5rem;
      border-top: 1px solid $border-light;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .salary-form {
    .form-header {
      padding: 1rem 1.5rem;

      h3 {
        font-size: 1.1rem;
      }
    }

    .form-content {
      padding: 1.5rem;

      .form-group {
        margin-bottom: 1.25rem;

        .input-wrapper {
          .form-input {
            padding: 0.625rem 1rem;
            padding-left: 2.25rem;
            font-size: 0.9rem;
          }

          .currency-symbol {
            left: 0.75rem;
            font-size: 0.9rem;
          }
        }
      }

      .calculated-fields {
        padding: 1rem;
        margin: 1rem 0;
      }

      .form-actions {
        flex-direction: column-reverse;
        gap: 0.75rem;

        app-button {
          width: 100%;
        }
      }
    }
  }
}

// Animation for calculated fields
@keyframes highlight {
  0% { background-color: rgba($primary, 0.1); }
  100% { background-color: transparent; }
}

.calculated-fields .form-group .input-wrapper .form-input.calculated {
  animation: highlight 0.5s ease-in-out;
}

// Focus states
.form-group:focus-within .form-label {
  color: $primary;
}

// Hover effects
.form-group .input-wrapper:hover .form-input:not(:focus):not(.calculated) {
  border-color: rgba($primary, 0.5);
}
