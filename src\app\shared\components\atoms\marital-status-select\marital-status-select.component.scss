@import '../../../../../styles/variables/colors';

.marital-status-select {
  position: relative;
  width: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  &__label {
    display: block;
    font-weight: 500;
    color: $gray-700;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;

    &--required {
      color: $gray-800;
    }
  }

  &__required {
    color: $danger;
    margin-left: 0.25rem;
  }

  &__container {
    position: relative;
    width: 100%;

    &--disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &--error {
      .marital-status-select__button {
        border-color: $danger;
        box-shadow: 0 0 0 3px rgba($danger, 0.1);
      }
    }

    &--open {
      .marital-status-select__dropdown {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }
    }
  }

  &__button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.75rem 1rem;
    background: $white;
    border: 1px solid $gray-300;
    border-radius: 12px;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: $gray-900;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 2.75rem;

    &:hover:not(:disabled) {
      border-color: $gray-400;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.1);
    }

    &:disabled {
      cursor: not-allowed;
      background-color: $gray-100;
      color: $gray-500;
    }
  }

  &__value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    text-align: left;
  }

  &__placeholder {
    display: flex;
    align-items: center;
    flex: 1;
    text-align: left;
    color: $gray-500;
  }

  &__icon {
    font-size: 1.125rem;
    line-height: 1;
  }

  &__text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__clear {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    margin-right: 0.5rem;
    background: none;
    border: none;
    border-radius: 50%;
    color: $gray-400;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: $gray-100;
      color: $gray-600;
    }

    &:focus {
      outline: none;
      background-color: $gray-100;
      color: $gray-600;
    }
  }

  &__arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    color: $gray-400;
    transition: transform 0.2s ease;

    &--open {
      transform: rotate(180deg);
    }
  }

  &__dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    margin-top: 0.25rem;
    background: $white;
    border: 1px solid $gray-200;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-0.5rem);
    transition: all 0.2s ease;
    max-height: 12rem;
    overflow-y: auto;
  }

  &__options {
    padding: 0.5rem 0;
  }

  &__option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    text-align: left;
    color: $gray-900;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 0.875rem;
    line-height: 1.25rem;

    &:hover {
      background-color: $gray-50;
    }

    &:focus {
      outline: none;
      background-color: $gray-50;
    }

    &--selected {
      background-color: rgba($primary, 0.05);
      color: $primary;
      font-weight: 500;

      &:hover {
        background-color: rgba($primary, 0.1);
      }
    }
  }

  &__option-icon {
    font-size: 1.125rem;
    line-height: 1;
  }

  &__option-text {
    flex: 1;
  }

  &__option-check {
    display: flex;
    align-items: center;
    justify-content: center;
    color: $primary;
  }

  &__error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    color: $danger;
    font-size: 0.75rem;
    line-height: 1rem;

    svg {
      flex-shrink: 0;
    }
  }

  // Size variants
  &--sm {
    .marital-status-select__button {
      padding: 0.5rem 0.75rem;
      font-size: 0.75rem;
      min-height: 2.25rem;
    }

    .marital-status-select__label {
      font-size: 0.75rem;
      margin-bottom: 0.25rem;
    }

    .marital-status-select__option {
      padding: 0.5rem 0.75rem;
      font-size: 0.75rem;
    }
  }

  &--lg {
    .marital-status-select__button {
      padding: 1rem 1.25rem;
      font-size: 1rem;
      min-height: 3.25rem;
    }

    .marital-status-select__label {
      font-size: 1rem;
      margin-bottom: 0.75rem;
    }

    .marital-status-select__option {
      padding: 1rem 1.25rem;
      font-size: 1rem;
    }
  }

  // Variant styles
  &--outline {
    .marital-status-select__button {
      background: transparent;
      border: 2px solid $gray-300;

      &:hover:not(:disabled) {
        border-color: $primary;
      }

      &:focus {
        border-color: $primary;
        box-shadow: 0 0 0 3px rgba($primary, 0.1);
      }
    }
  }

  &--filled {
    .marital-status-select__button {
      background: $gray-50;
      border: 1px solid transparent;

      &:hover:not(:disabled) {
        background: $gray-100;
      }

      &:focus {
        background: $white;
        border-color: $primary;
        box-shadow: 0 0 0 3px rgba($primary, 0.1);
      }
    }
  }
}
