import { HttpInterceptorFn, HttpRequest, HttpHandlerFn, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, filter, switchMap, take, finalize } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { JwtService } from '../services/jwt.service';

// Shared state for the interceptor
let isRefreshing = false;
const refreshTokenSubject = new BehaviorSubject<string | null>(null);

export const authInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> => {
  const authService = inject(AuthService);
  const jwtService = inject(JwtService);

  // Skip adding token for authentication endpoints
  if (isAuthUrl(request.url)) {
    return next(request);
  }

  // Add token to request
  const token = authService.getAccessToken();
  if (token) {
    // Only check for token expiration if it's not a login or refresh token request
    // This prevents unnecessary token refreshes right after login
    if (!request.url.includes('/api/login/') && !request.url.includes('/api/token/refresh/')) {
      // Check if token is expired before making the request
      if (jwtService.isTokenExpired(token) && !isRefreshing) {
        // Token is expired, try to refresh it before proceeding
        return handle401Error(request, next, authService, jwtService);
      }
    }

    request = addToken(request, token);
  }

  return next(request).pipe(
    catchError((error: HttpErrorResponse) => {
      if (error.status === 401) {
        return handle401Error(request, next, authService, jwtService);
      }
      return throwError(() => error);
    })
  );
};

function addToken(request: HttpRequest<any>, token: string): HttpRequest<any> {
  return request.clone({
    setHeaders: {
      Authorization: `Bearer ${token}`
    }
  });
}

function handle401Error(
  request: HttpRequest<any>,
  next: HttpHandlerFn,
  authService: AuthService,
  jwtService: JwtService
): Observable<HttpEvent<any>> {
  if (!isRefreshing) {
    isRefreshing = true;
    refreshTokenSubject.next(null);

    return authService.refreshToken().pipe(
      switchMap(token => {
        isRefreshing = false;

        // Verify the new token is valid
        if (token && !jwtService.isTokenExpired(token)) {
          refreshTokenSubject.next(token);
          return next(addToken(request, token));
        } else {
          // If we got an invalid token, logout
          authService.logout();
          return throwError(() => new Error('Invalid token received after refresh'));
        }
      }),
      catchError(error => {
        isRefreshing = false;
        authService.logout();
        return throwError(() => error);
      }),
      finalize(() => {
        isRefreshing = false;
      })
    );
  } else {
    return refreshTokenSubject.pipe(
      filter(token => token !== null),
      take(1),
      switchMap(token => {
        // Double-check the token is still valid before using it
        if (token && !jwtService.isTokenExpired(token as string)) {
          return next(addToken(request, token as string));
        } else {
          // If token is expired, logout and fail the request
          authService.logout();
          return throwError(() => new Error('Token expired'));
        }
      })
    );
  }
}

function isAuthUrl(url: string): boolean {
  // Skip token validation for auth endpoints
  return url.includes('/token/') || url.includes('/register/');
}
