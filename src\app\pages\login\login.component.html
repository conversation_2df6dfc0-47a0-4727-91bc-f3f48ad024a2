<div class="login-container">
  <div class="login-wrapper">
    <div class="login-left">
      <div class="login-left-content">
        <h1 class="headline">Empowering people through seamless HR management.</h1>

        <div class="login-image">
          <img src="assets/images/login/hr-modern-illustration.svg" alt="HR Modern Dashboard">
        </div>

        <p class="subheadline">Efficiently manage your workforce, streamline operations effortlessly.</p>

        <div class="decorative-circle circle-1"></div>
        <div class="decorative-circle circle-2"></div>
      </div>
    </div>

    <div class="login-right">
      <div class="login-right-content">
        <div class="logo-container">
          <h2 class="logo-text">HR<span class="highlight">Shell</span></h2>
        </div>

        <div class="login-header">
          <h2>Sign In</h2>
          <p>Please enter your details to sign in</p>
        </div>

        <!-- Success message after registration -->
        @if (registeredMessage) {
          <div class="alert alert-success">
            <i class="fa fa-check-circle"></i>
            <span>{{ registeredMessage }}</span>
            <button type="button" class="alert-close" (click)="registeredMessage = null">×</button>
          </div>
        }

        <!-- Error message -->
        @if (error()) {
          <div class="alert alert-error">
            <i class="fa fa-exclamation-circle"></i>
            <span>{{ error() }}</span>
            <button type="button" class="alert-close" (click)="clearError()">×</button>
          </div>
        }

        <div class="login-form">
          <div class="form-group">
            <label for="username">Username</label>
            <div class="input-wrapper">
              <input
                type="text"
                id="username"
                [(ngModel)]="username"
                name="username"
                [disabled]="isLoading()"
              >
              <div class="input-icon">
                <i class="fa fa-user"></i>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="password">Password</label>
            <div class="input-wrapper">
              <input
                [type]="showPassword ? 'text' : 'password'"
                id="password"
                [(ngModel)]="password"
                name="password"
                [disabled]="isLoading()"
              >
              <button
                type="button"
                class="password-toggle"
                (click)="togglePasswordVisibility()"
              >
                <i [class]="showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
              </button>
            </div>
          </div>

          <div class="form-options">
            <div class="remember-me">
              <input type="checkbox" id="remember" [(ngModel)]="rememberMe" name="rememberMe">
              <label for="remember">Remember Me</label>
            </div>
            <a href="#" class="forgot-password">Forgot Password?</a>
          </div>

          <button
            type="button"
            class="btn-login"
            (click)="login()"
            [disabled]="isLoading()"
          >
            @if (isLoading()) {
              <i class="fa fa-spinner fa-spin"></i> Signing In...
            } @else {
              Sign In
            }
          </button>

          <div class="account-link">
            <span>Don't have an account?</span>
            <a href="#" (click)="navigateToRegister($event)">Create Account</a>
          </div>

          <div class="login-divider">
            <span>Or</span>
          </div>

          <div class="social-login">
            <button type="button" class="btn-social facebook" (click)="loginWithMicrosoft()">
              <i class="fa fa-facebook"></i>
            </button>

            <button type="button" class="btn-social google" (click)="loginWithGoogle()">
              <i class="fa fa-google"></i>
            </button>

            <button type="button" class="btn-social apple" (click)="loginWithApple()">
              <i class="fa fa-apple"></i>
            </button>
          </div>
        </div>

        <div class="login-footer">
          <p>Copyright © 2024 - HR Shell</p>
        </div>
      </div>
    </div>
  </div>
</div>
