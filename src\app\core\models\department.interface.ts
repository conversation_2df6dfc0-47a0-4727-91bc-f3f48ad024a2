/**
 * Department interface - matches API response
 */
export interface Department {
  id: number;
  name: string;
  description?: string;
  manager?: number | null;
  manager_name?: string;
  employee_count?: number;
  status?: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

/**
 * Department creation/update interface - matches API request
 */
export interface DepartmentInput {
  name: string;
  description?: string;
  manager?: number | null;
  status: 'active' | 'inactive';
}

/**
 * Paginated response interface for departments - matches API response
 */
export interface PaginatedDepartmentsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Department[];
}

/**
 * Department list query parameters - matches API filters
 */
export interface DepartmentListParams {
  page?: number;
  page_size?: number;
  search?: string;
  ordering?: string;
  manager?: number;
  manager_name?: string;
  status?: string;
  name?: string;
}

/**
 * Department filter state interface
 */
export interface DepartmentFilters {
  status: string[];
  manager_name: string[]; // Keep for backward compatibility
  manager: number[]; // New field for manager IDs
  search: string;
  ordering: string;
}

/**
 * Employee list interface for department details
 */
export interface EmployeeList {
  id: number;
  full_name: string;
  email: string;
  position: string;
  department_name: string;
  status: 'active' | 'inactive';
  profile_picture?: string;
}

/**
 * Department detail interface - matches API response for department details
 */
export interface DepartmentDetail {
  id: number;
  name: string;
  description?: string;
  manager?: number;
  manager_name?: string;
  status: 'active' | 'inactive';
  employee_count: string;
  created_at: string;
  updated_at: string;
  employees: EmployeeList[];
}
