@import '../../../styles/variables/_colors';

// Layout variables
:host {
  --header-height: 64px;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 60px;
}

// Main Layout - BEM Methodology
.layout {
  // Block: layout
  &__main {
    display: flex;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background: var(--theme-background, $white);
    color: var(--theme-on-background, $text-primary);
    transition: all var(--theme-transition-normal, 250ms) ease;

    &.menu-collapsed {
      .layout__content {
        margin-left: var(--sidebar-collapsed-width, 60px);
      }
    }

    // Header position styles
    &.header-fixed {
      .main-header {
        position: fixed;
        top: 0;
        left: var(--sidebar-width, 280px);
        right: 0;
        z-index: 1000;
        transition: left var(--theme-transition-normal, 250ms) ease;
      }

      .layout__main-content {
        padding-top: var(--header-height, 64px);
      }

      &.menu-collapsed .main-header {
        left: var(--sidebar-collapsed-width, 60px);
      }
    }

    &.header-static {
      .main-header {
        position: static;
      }

      .layout__main-content {
        padding-top: 0;
      }
    }

    &.header-sticky {
      .main-header {
        position: sticky;
        top: 0;
        z-index: 1000;
      }

      .layout__main-content {
        padding-top: 0;
      }
    }
  }

  // Element: content area
  &__content {
    flex: 1;
    display: flex;
    transition: margin-left var(--theme-transition-normal, 250ms) ease;
    flex-direction: column;
    overflow: hidden;
  }

  // Element: main content
  &__main-content {
    flex: 1;
    padding: 0;
    overflow-y: auto;
    background-color: var(--theme-surface, $body-bg);
    display: flex;
    flex-direction: column;
    height: calc(100vh - var(--header-height, 64px));
    transition: background-color var(--theme-transition-normal, 250ms) ease;
  }
}

// Block: header
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px;
  background-color: $header-bg;
  border-bottom: 1px solid $border-color;

  // Element: actions
  &__actions {
    display: flex;
    align-items: center;
  }
}

// Block: search
.search {
  // Element: bar
  &__bar {
    display: flex;
    align-items: center;
    width: 300px;
    height: 36px;
    background-color: $gray-100;
    border-radius: 4px;
    overflow: hidden;
  }

  // Element: input
  &__input {
    flex: 1;
    height: 100%;
    padding: 0 10px;
    border: none;
    background: transparent;
    font-size: $font-size-sm;

    &:focus {
      outline: none;
    }
  }

  // Element: button
  &__button {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    color: $secondary;

    &:hover {
      color: $dark;
    }
  }
}

// Block: action
.action {
  // Element: button
  &__button {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    color: $secondary;
    margin-left: 10px;
    position: relative;

    &:hover {
      color: $dark;
    }

    // Modifier: notification
    &--notification {
      position: relative;
    }
  }
}

// Block: notification
.notification {
  // Element: badge
  &__badge {
    position: absolute;
    top: 0;
    right: 0;
    width: 18px;
    height: 18px;
    background-color: $notification-bg;
    color: $white;
    font-size: $font-size-xs;
    font-weight: $font-weight-semibold;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }
}

// Block: user
.user {
  // Element: profile
  &__profile {
    margin-left: 15px;
  }

  // Element: avatar
  &__avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
  }
}



