@import '../../../styles/variables/_colors';

// Main Layout - BEM Methodology
.layout {
  // Block: layout
  &__main {
    display: flex;
    width: 100%;
    height: 100vh;
    overflow: hidden;
  }

  // Element: content area
  &__content {
    flex: 1;
    display: flex;
    transition: margin-left 0.3s ease;
    flex-direction: column;
    overflow: hidden;
  }

  // Element: main content
  &__main-content {
    flex: 1;
    padding: 0;
    overflow-y: auto;
    background-color: $body-bg;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 60px);
  }
}

// Block: header
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px;
  background-color: $header-bg;
  border-bottom: 1px solid $border-color;

  // Element: actions
  &__actions {
    display: flex;
    align-items: center;
  }
}

// Block: search
.search {
  // Element: bar
  &__bar {
    display: flex;
    align-items: center;
    width: 300px;
    height: 36px;
    background-color: $gray-100;
    border-radius: 4px;
    overflow: hidden;
  }

  // Element: input
  &__input {
    flex: 1;
    height: 100%;
    padding: 0 10px;
    border: none;
    background: transparent;
    font-size: $font-size-sm;

    &:focus {
      outline: none;
    }
  }

  // Element: button
  &__button {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    color: $secondary;

    &:hover {
      color: $dark;
    }
  }
}

// Block: action
.action {
  // Element: button
  &__button {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    color: $secondary;
    margin-left: 10px;
    position: relative;

    &:hover {
      color: $dark;
    }

    // Modifier: notification
    &--notification {
      position: relative;
    }
  }
}

// Block: notification
.notification {
  // Element: badge
  &__badge {
    position: absolute;
    top: 0;
    right: 0;
    width: 18px;
    height: 18px;
    background-color: $notification-bg;
    color: $white;
    font-size: $font-size-xs;
    font-weight: $font-weight-semibold;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }
}

// Block: user
.user {
  // Element: profile
  &__profile {
    margin-left: 15px;
  }

  // Element: avatar
  &__avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
  }
}



