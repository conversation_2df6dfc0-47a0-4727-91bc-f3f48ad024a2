import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, catchError, map, of, throwError } from 'rxjs';
import { environment } from '../../../environments/environment';
import { API_ENDPOINTS } from '../constants/api.constants';
import { Employee, EmployeeInput, PaginatedEmployeesResponse } from '../state/employees/employees.state';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService {
  private apiUrl = environment.apiUrl;
  private employeesEndpoint = API_ENDPOINTS.EMPLOYEES.EMPLOYEES;

  constructor(private http: HttpClient) {}

  /**
   * Get all employees with pagination and optional search and department filtering
   * @param page Page number (1-based)
   * @param pageSize Number of items per page
   * @param searchTerm Optional search term
   * @param departmentIds Optional array of department IDs to filter by
   * @returns Observable of paginated employees response
   */
  getEmployees(page: number = 1, pageSize: number = 10, searchTerm?: string, departmentIds?: (string | number)[]): Observable<PaginatedEmployeesResponse> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    if (searchTerm) {
      params = params.set('search', searchTerm);
    }

    // Add department filtering if provided and not "All Departments"
    if (departmentIds && departmentIds.length > 0) {
      // Filter out empty string (All Departments) and only include actual department IDs
      const validDepartmentIds = departmentIds.filter(id => id !== '' && id !== null && id !== undefined);

      if (validDepartmentIds.length > 0) {
        // Convert to comma-separated string for the API
        params = params.set('department', validDepartmentIds.join(','));
      }
    }

    return this.http.get<PaginatedEmployeesResponse>(`${this.apiUrl}${this.employeesEndpoint}`, { params })
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all employees (legacy method for backward compatibility)
   * @returns Observable of Employee array
   */
  getAllEmployees(): Observable<Employee[]> {
    // Use the paginated endpoint but return just the results array
    return this.getEmployees(1, 100).pipe(
      map(response => response.results),
      catchError(error => {
        console.error('Error in getAllEmployees:', error);
        return of([]);
      })
    );
  }

  /**
   * Get employees by company ID
   * @param companyId The company ID
   * @returns Observable of Employee array
   */
  getEmployeesByCompany(companyId: number): Observable<Employee[]> {
    return this.http.get<any>(`${this.apiUrl}/companies/${companyId}/employees/`)
      .pipe(
        // Handle both array and paginated response formats
        map((response: any) => {
          // If response is an array, return it directly
          if (Array.isArray(response)) {
            return response;
          }
          // If response is a paginated object with results property, return the results
          else if (response && response.results && Array.isArray(response.results)) {
            return response.results;
          }
          // If response is something else, log it and return an empty array
          else {
            console.error('Unexpected response format from employees API:', response);
            return [];
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get employee by ID
   * @param id The employee ID
   * @returns Observable of Employee
   */
  getEmployeeById(id: number): Observable<Employee> {
    return this.http.get<Employee>(`${this.apiUrl}${this.employeesEndpoint}${id}/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Create a new employee
   * @param employeeData The employee data
   * @returns Observable of the created Employee
   */
  createEmployee(employeeData: EmployeeInput): Observable<Employee> {
    // Check if we have a file to upload
    if (employeeData.avatar && employeeData.avatar instanceof File) {
      // Create a FormData object for multipart/form-data
      const formData = new FormData();

      // Add all employee data to the form
      Object.keys(employeeData).forEach(key => {
        // Use type assertion to avoid TypeScript errors
        const value = (employeeData as any)[key];

        if (key === 'avatar' && value instanceof File) {
          formData.append(key, value);
        } else if (value !== null && value !== undefined) {
          // Only append if the value is not null or undefined
          formData.append(key, value.toString());
        }
      });

      // Send the form data
      return this.http.post<Employee>(`${this.apiUrl}${this.employeesEndpoint}`, formData)
        .pipe(
          catchError(this.handleError)
        );
    } else {
      // No file to upload, send as JSON
      return this.http.post<Employee>(`${this.apiUrl}${this.employeesEndpoint}`, employeeData)
        .pipe(
          catchError(this.handleError)
        );
    }
  }

  /**
   * Update an existing employee
   * @param id The employee ID
   * @param employeeData The updated employee data
   * @returns Observable of the updated Employee
   */
  updateEmployee(id: number, employeeData: EmployeeInput): Observable<Employee> {
    // Check if we have a file to upload
    if (employeeData.avatar && employeeData.avatar instanceof File) {
      // Create a FormData object for multipart/form-data
      const formData = new FormData();

      // Add all employee data to the form
      Object.keys(employeeData).forEach(key => {
        // Use type assertion to avoid TypeScript errors
        const value = (employeeData as any)[key];

        if (key === 'avatar' && value instanceof File) {
          formData.append(key, value);
        } else if (value !== null && value !== undefined) {
          // Only append if the value is not null or undefined
          formData.append(key, value.toString());
        }
      });

      // Send the form data
      return this.http.put<Employee>(`${this.apiUrl}${this.employeesEndpoint}${id}/`, formData)
        .pipe(
          catchError(this.handleError)
        );
    } else {
      // No file to upload, send as JSON
      return this.http.put<Employee>(`${this.apiUrl}${this.employeesEndpoint}${id}/`, employeeData)
        .pipe(
          catchError(this.handleError)
        );
    }
  }

  /**
   * Delete an employee
   * @param id The employee ID
   * @returns Observable of void
   */
  deleteEmployee(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}${this.employeesEndpoint}${id}/`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Upload employee avatar
   * @param employeeId The employee ID
   * @param file The avatar file
   * @returns Observable of the upload response
   */
  uploadAvatar(employeeId: number, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('avatar', file);

    return this.http.post<any>(`${this.apiUrl}${this.employeesEndpoint}${employeeId}/avatar/`, formData)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Handle HTTP errors
   * @param error The HTTP error response
   * @returns Observable with error
   */
  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = error.error?.detail || error.error?.message || `Error Code: ${error.status}, Message: ${error.message}`;
    }

    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
