@import '../../../styles/variables/_colors';

// Registration Page - BEM Methodology
.registration {
  // Block: registration
  &__container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $gray-100;
    padding: 0;
    position: relative;
    overflow: hidden;
  }

  &__wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
    background-color: $white;
    overflow: hidden;
    position: relative;
  }

  // Element: left panel
  &__left {
    flex: 1;
    background: linear-gradient(135deg, #ff6b35 0%, #e85a2a 100%);
    color: $white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    position: relative;
    overflow: hidden;

    // Element: left content
    &-content {
      max-width: 450px;
      position: relative;
      z-index: 1;
      padding: 2rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }

  // Element: right panel
  &__right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    background-color: $white;

    // Element: right content
    &-content {
      width: 100%;
      max-width: 480px;
      padding: 1rem;
      margin-top: 20px;
    }
  }

  // Element: headline
  &__headline {
    font-size: 2.5rem;
    font-weight: $font-weight-bold;
    margin-bottom: 2rem;
    line-height: $line-height-tight;
    text-align: center;
  }

  // Element: subheadline
  &__subheadline {
    font-size: 1.2rem;
    margin-top: 2rem;
    line-height: $line-height-base;
    text-align: center;
  }

  // Element: decorative circle
  &__circle {
    position: absolute;
    border-radius: 50%;
    background-color: rgba($white, 0.1);

    // Modifier: first circle
    &--1 {
      top: 20%;
      right: 10%;
      width: 100px;
      height: 100px;
    }

    // Modifier: second circle
    &--2 {
      bottom: 15%;
      left: 5%;
      width: 150px;
      height: 150px;
    }
  }

  // Element: image
  &__image {
    margin: 2rem 0;
    text-align: center;
    position: relative;
    z-index: 1;
    width: 90%;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    transition: all 0.5s ease;

    &:hover {
      transform: translateY(-5px);
    }

    img {
      width: 100%;
      height: auto;
      display: block;
      border-radius: 12px;
      box-shadow: 0 15px 30px rgba($black, 0.15);
      transition: all 0.5s ease;
    }

    &:hover img {
      box-shadow: 0 20px 40px rgba(#e85a2a, 0.2);
      transform: scale(1.02);
    }
  }

  // Element: header
  &__header {
    text-align: center;
    margin-bottom: 1.5rem;

    h2 {
      font-size: 1.5rem;
      font-weight: $font-weight-semibold;
      color: $gray-800;
      margin-bottom: 0.5rem;
    }

    p {
      color: $gray-600;
      font-size: 0.85rem;
    }
  }

  // Element: form
  &__form {
    margin-bottom: 2rem;
  }

  // Element: divider
  &__divider {
    position: relative;
    text-align: center;
    margin: 1rem 0;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 1px;
      background-color: $gray-300;
    }

    span {
      position: relative;
      background-color: $white;
      padding: 0 10px;
      font-size: 0.8rem;
      color: $gray-600;
    }
  }

  // Element: footer
  &__footer {
    text-align: center;
    font-size: 0.8rem;
    color: $gray-500;
    margin-top: 2rem;
  }
}

// Block: logo
.logo {
  &__container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  &__text {
    font-size: 2rem;
    font-weight: $font-weight-bold;
    color: $gray-800;
    margin: 0 auto;
    text-align: center;

    .highlight {
      color: #ff6b35;
    }
  }
}

// Block: form
.form {
  // Element: row
  &__row {
    display: flex;
    gap: 1.25rem;
    margin-bottom: 0.5rem;
  }

  // Element: group
  &__group {
    flex: 1;
    margin-bottom: 1rem;

    // Modifier: full width
    &--full-width {
      width: 100%;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-size: 0.85rem;
      color: $gray-800;
      font-weight: $font-weight-medium;
    }
  }

  // Element: input wrapper
  &__input-wrapper {
    position: relative;
  }

  // Element: input
  &__input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 2.5rem;
    border: 1px solid $gray-300;
    border-radius: 4px;
    font-size: 0.85rem;
    transition: border-color 0.3s, box-shadow 0.3s;
    background-color: $white;

    &:focus {
      outline: none;
      border-color: #ff6b35;
      box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    }

    &::placeholder {
      color: $gray-500;
      font-size: 0.85rem;
    }
  }

  // Element: input icon
  &__icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: $gray-500;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // Element: password toggle
  &__password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: $gray-500;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: #ff6b35;
    }
  }

  // Element: error message
  &__error {
    color: $danger;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-weight: $font-weight-regular;

    // Modifier: global error
    &--global {
      font-size: 0.85rem;
      text-align: center;
      margin-bottom: 1rem;
      padding: 0.75rem 2rem 0.75rem 1rem;
      background-color: rgba($danger, 0.1);
      border-radius: 4px;
      border-left: 3px solid $danger;
      position: relative;
    }
  }

  // Element: error close button
  .error__close {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: $danger;
    opacity: 0.7;

    &:hover {
      opacity: 1;
    }
  }

  // Element: options
  &__options {
    margin-bottom: 1.25rem;
    font-size: 0.8rem;
  }
}

// Block: terms
.terms {
  &__agreement {
    display: flex;
    align-items: flex-start;

    input {
      margin-right: 0.5rem;
      margin-top: 0.2rem;
      cursor: pointer;
    }

    label {
      line-height: 1.4;
      color: $gray-600;
      font-size: 0.8rem;
    }

    a {
      color: #ff6b35;
      text-decoration: none;
      font-weight: $font-weight-medium;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Block: button
.button {
  // Element: register
  &__register {
    width: 100%;
    padding: 0.85rem;
    background-color: #ff6b35;
    color: $white;
    border: none;
    border-radius: 4px;
    font-size: 0.95rem;
    font-weight: $font-weight-medium;
    cursor: pointer;
    margin-bottom: 1rem;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(232, 90, 42, 0.2);
    letter-spacing: 0.25px;

    &:hover:not(:disabled) {
      background-color: #e85a2a;
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(232, 90, 42, 0.25);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(232, 90, 42, 0.2);
    }

    &:disabled {
      background-color: #ffaa8c;
      cursor: not-allowed;
      opacity: 0.7;
    }

    i {
      margin-right: 0.5rem;
    }
  }

  // Element: social
  &__social {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid $gray-300;
    background-color: $white;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 10px rgba($black, 0.1);
    }

    // Modifier: microsoft
    &--microsoft {
      color: #00a4ef;
    }

    // Modifier: google
    &--google {
      color: #ea4335;
    }

    // Modifier: apple
    &--apple {
      color: $black;
    }
  }
}

// Block: account
.account {
  // Element: link
  &__link {
    text-align: center;
    margin-bottom: 0.75rem;
    font-size: 0.8rem;
    color: $gray-600;

    a {
      color: #ff6b35;
      text-decoration: none;
      margin-left: 0.25rem;
      font-weight: $font-weight-medium;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Block: social
.social {
  // Element: registration
  &__registration {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 1rem;
    margin-top: 1rem;
  }
}

// Media queries
@media (max-width: 992px) {
  .registration {
    &__wrapper {
      flex-direction: column;
    }

    &__left, &__right {
      padding: 2rem;
    }
  }
}

@media (max-width: 576px) {
  .registration {
    &__container {
      padding: 1rem;
    }

    &__left, &__right {
      padding: 1.5rem;
    }

    &__header {
      h2 {
        font-size: 1.5rem;
      }
    }
  }
}


