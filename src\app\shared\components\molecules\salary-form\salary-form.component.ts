import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

// Import atomic components
import { ButtonComponent } from '../../atoms/button/button.component';

// Import interfaces
import { EmployeeSalary } from '../../../../core/services/employee-management.service';

@Component({
  selector: 'app-salary-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonComponent
  ],
  templateUrl: './salary-form.component.html',
  styleUrls: ['./salary-form.component.scss']
})
export class SalaryFormComponent implements OnInit, OnDestroy {
  @Input() employeeId: number | null = null;
  @Input() salaryData: EmployeeSalary | null = null;
  @Input() isEdit: boolean = false;
  @Output() formSubmit = new EventEmitter<Partial<EmployeeSalary>>();
  @Output() formCancel = new EventEmitter<void>();

  private destroy$ = new Subject<void>();
  private fb = inject(FormBuilder);

  salaryForm: FormGroup;
  isSubmitting = false;

  constructor() {
    this.salaryForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.salaryData && this.isEdit) {
      this.populateForm(this.salaryData);
    }

    // Watch for changes in basic salary and allowances to calculate gross salary
    this.salaryForm.get('basic_salary')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => this.calculateGrossSalary());

    this.salaryForm.get('allowances')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => this.calculateGrossSalary());

    this.salaryForm.get('deductions')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => this.calculateNetSalary());
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      employee_id: [this.employeeId, [Validators.required]],
      basic_salary: [0, [Validators.required, Validators.min(0)]],
      allowances: [0, [Validators.min(0)]],
      deductions: [0, [Validators.min(0)]],
      gross_salary: [{ value: 0, disabled: true }],
      net_salary: [{ value: 0, disabled: true }],
      effective_date: [new Date().toISOString().split('T')[0], [Validators.required]]
    });
  }

  private populateForm(salary: EmployeeSalary): void {
    this.salaryForm.patchValue({
      employee_id: salary.employee_id,
      basic_salary: salary.basic_salary,
      allowances: salary.allowances || 0,
      deductions: salary.deductions || 0,
      gross_salary: salary.gross_salary,
      net_salary: salary.net_salary,
      effective_date: salary.effective_date
    });
  }

  private calculateGrossSalary(): void {
    const basicSalary = this.salaryForm.get('basic_salary')?.value || 0;
    const allowances = this.salaryForm.get('allowances')?.value || 0;
    const grossSalary = basicSalary + allowances;
    
    this.salaryForm.get('gross_salary')?.setValue(grossSalary);
    this.calculateNetSalary();
  }

  private calculateNetSalary(): void {
    const grossSalary = this.salaryForm.get('gross_salary')?.value || 0;
    const deductions = this.salaryForm.get('deductions')?.value || 0;
    const netSalary = grossSalary - deductions;
    
    this.salaryForm.get('net_salary')?.setValue(netSalary);
  }

  onSubmit(): void {
    if (this.salaryForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      
      const formValue = this.salaryForm.getRawValue();
      const salaryData: Partial<EmployeeSalary> = {
        employee_id: formValue.employee_id,
        basic_salary: formValue.basic_salary,
        allowances: formValue.allowances,
        deductions: formValue.deductions,
        gross_salary: formValue.gross_salary,
        net_salary: formValue.net_salary,
        effective_date: formValue.effective_date
      };

      this.formSubmit.emit(salaryData);
    }
  }

  onCancel(): void {
    this.formCancel.emit();
  }

  // Getter methods for template
  get basicSalaryControl() {
    return this.salaryForm.get('basic_salary');
  }

  get allowancesControl() {
    return this.salaryForm.get('allowances');
  }

  get deductionsControl() {
    return this.salaryForm.get('deductions');
  }

  get grossSalaryControl() {
    return this.salaryForm.get('gross_salary');
  }

  get netSalaryControl() {
    return this.salaryForm.get('net_salary');
  }

  get effectiveDateControl() {
    return this.salaryForm.get('effective_date');
  }

  // Validation helper methods
  isFieldInvalid(fieldName: string): boolean {
    const field = this.salaryForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.salaryForm.get(fieldName);
    if (field && field.errors) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} is required`;
      }
      if (field.errors['min']) {
        return `${this.getFieldLabel(fieldName)} must be greater than or equal to ${field.errors['min'].min}`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      basic_salary: 'Basic Salary',
      allowances: 'Allowances',
      deductions: 'Deductions',
      effective_date: 'Effective Date'
    };
    return labels[fieldName] || fieldName;
  }
}
