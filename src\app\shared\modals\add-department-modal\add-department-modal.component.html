<app-modal
  id="add-department-modal"
  title="Add New Department"
  size="medium"
  [showFooter]="true">

  <form [formGroup]="departmentForm" (ngSubmit)="onSubmit()" class="department-form">
    <div class="department-form__group">
      <label for="name" class="department-form__label">
        Department Name <span class="department-form__required">*</span>
      </label>
      <input
        type="text"
        id="name"
        formControlName="name"
        class="department-form__input"
        [class.department-form__input--invalid]="isFieldInvalid('name')"
        placeholder="Enter department name"
      >
      <div class="department-form__error" *ngIf="getFieldError('name')">
        {{ getFieldError('name') }}
      </div>
    </div>

    <div class="department-form__group">
      <label for="description" class="department-form__label">
        Description
      </label>
      <textarea
        id="description"
        formControlName="description"
        class="department-form__textarea"
        [class.department-form__input--invalid]="isFieldInvalid('description')"
        placeholder="Enter department description (optional)"
        rows="4"
      ></textarea>
      <div class="department-form__error" *ngIf="getFieldError('description')">
        {{ getFieldError('description') }}
      </div>
    </div>

    <div class="department-form__group">
      <label for="manager" class="department-form__label">
        Manager
        <span *ngIf="isLoadingEmployees" class="department-form__loading-indicator">
          <app-icon name="fa fa-spinner fa-spin" size="sm"></app-icon>
        </span>
      </label>
      <app-searchable-dropdown
        id="manager-dropdown"
        [options]="managerOptions"
        [placeholder]="getManagerPlaceholder()"
        [disabled]="isLoadingEmployees || !!employeeLoadError"
        [invalid]="isFieldInvalid('manager')"
        formControlName="manager"
        searchPlaceholder="Search employees..."
        [showSearch]="true">
      </app-searchable-dropdown>
      <div class="department-form__error" *ngIf="getFieldError('manager')">
        {{ getFieldError('manager') }}
      </div>
      <div class="department-form__error" *ngIf="employeeLoadError">
        <app-icon name="fa fa-exclamation-triangle" size="sm"></app-icon>
        Failed to load employees: {{ employeeLoadError }}
        <button
          type="button"
          class="department-form__retry-button"
          (click)="retryLoadEmployees()"
          [disabled]="isLoadingEmployees">
          <app-icon name="fa fa-refresh" size="sm"></app-icon>
          Retry
        </button>
      </div>
      <div class="department-form__info" *ngIf="!isLoadingEmployees && !employeeLoadError && managerOptions.length === 1">
        <app-icon name="fa fa-info-circle" size="sm"></app-icon>
        No employees available to assign as manager
      </div>
    </div>

    <div class="department-form__group">
      <label for="status" class="department-form__label">
        Status <span class="department-form__required">*</span>
      </label>
      <app-searchable-dropdown
        id="status-dropdown"
        [options]="statusOptions"
        placeholder="Select status"
        [invalid]="isFieldInvalid('status')"
        formControlName="status"
        [showSearch]="false">
      </app-searchable-dropdown>
      <div class="department-form__error" *ngIf="getFieldError('status')">
        {{ getFieldError('status') }}
      </div>
    </div>
  </form>

  <div modal-footer class="department-form__footer">
    <app-button
      class="department-form__button"
      label="Cancel"
      variant="secondary"
      (onClick)="closeModal()">
    </app-button>

    <app-button
      class="department-form__button"
      [label]="isSubmitting ? 'Creating...' : 'Create Department'"
      variant="primary"
      [disabled]="departmentForm.invalid || isSubmitting"
      [icon]="isSubmitting ? 'fa fa-spinner fa-spin' : 'fa fa-plus'"
      (onClick)="onSubmit()">
    </app-button>
  </div>
</app-modal>
