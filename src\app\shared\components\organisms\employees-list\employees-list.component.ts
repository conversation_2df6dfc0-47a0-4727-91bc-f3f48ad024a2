import { Component, OnInit, On<PERSON><PERSON>roy, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { DataTableContainerComponent } from '../../molecules/data-table-container/data-table-container.component';
import { TableColumn } from '../../atoms/data-table/data-table.component';
import { DropdownOption } from '../../atoms/dropdown-select/dropdown-select.component';

import { EmployeesStore, Employee, DepartmentsStore } from '../../../../core/state';
import { ModalService as SharedModalService } from '../../../services/modal.service';
import { ModalService as CoreModalService } from '../../../../core/services/modal.service';
import { ToastService } from '../../../services/toast.service';
import { AddEmployeeModalComponent } from '../add-employee-modal/add-employee-modal.component';
import { ToastComponent } from '../../molecules/toast/toast.component';
import { EmployeeStatisticsCardsComponent, EmployeeStatistics } from '../../molecules/employee-statistics-cards/employee-statistics-cards.component';
import { SubSink } from 'subsink';

@Component({
  selector: 'app-employees-list',
  standalone: true,
  imports: [
    CommonModule,
    DataTableContainerComponent,
    AddEmployeeModalComponent,
    ToastComponent,
    EmployeeStatisticsCardsComponent
  ],
  templateUrl: './employees-list.component.html',
  styleUrls: ['./employees-list.component.scss']
})
export class EmployeesListComponent implements OnInit, OnDestroy {
  private router = inject(Router);
  private employeesStore = inject(EmployeesStore);
  private departmentsStore = inject(DepartmentsStore);
  private sharedModalService = inject(SharedModalService);
  private coreModalService = inject(CoreModalService);
  private toastService = inject(ToastService);


  // Access state from the store using signals
  employees = this.employeesStore.employees;
  isLoading = this.employeesStore.isLoading;
  error = this.employeesStore.error;
  employeesCount = this.employeesStore.employeesCount;
  activeEmployeesCount = this.employeesStore.activeEmployeesCount;
  onLeaveEmployeesCount = this.employeesStore.onLeaveEmployeesCount;
  departmentCounts = this.employeesStore.departmentCounts;

  // Pagination signals
  page = this.employeesStore.page;
  pageSize = this.employeesStore.pageSize;
  totalCount = this.employeesStore.totalCount;
  totalPages = this.employeesStore.totalPages;
  hasNextPage = this.employeesStore.hasNextPage;
  hasPreviousPage = this.employeesStore.hasPreviousPage;

  // Search signal
  searchTerm = this.employeesStore.searchTerm;
  lastError: string | null = null;


  // Table columns
  columns: TableColumn[] = [];

  // Filter options for the table actions component
  filters: {
    name: string;
    options: DropdownOption[];
    selectedValue: string | number;
    placeholder: string;
  }[] = [];

  // Filter options for the data table header
  tableFilters: { label: string, value: string }[] = [];

  // Department options for the dropdown
  departmentOptions: DropdownOption[] = [];

  // Selected department IDs for filtering
  selectedDepartmentIds: (string | number)[] = [];

  // Date range
  startDate: Date = new Date(2023, 0, 1); // Jan 1, 2023
  endDate: Date = new Date();   // Current date

  // Pagination (local variables for template binding)
  get currentPage(): number { return this.page(); }
  get currentPageSize(): number { return this.pageSize(); }
  get totalItems(): number { return this.totalCount(); }

  // Statistics for the cards
  get statistics(): EmployeeStatistics {
    return {
      totalEmployees: this.employeesCount(),
      activeEmployees: this.activeEmployeesCount(),
      onLeaveEmployees: this.onLeaveEmployeesCount(),
      newJoiners: 67 // Static value for now, can be made dynamic later
    };
  }

  // Filtered and selected employees
  filteredEmployees: Employee[] = [];
  selectedEmployees: Employee[] = [];

  // Search query
  searchQuery: string = '';

  // Delete confirmation (kept for backward compatibility)
  showDeleteConfirmation: boolean = false;
  employeeToDelete: Employee | null = null;

  // Dynamic columns configuration
  public useDynamicColumns: boolean = true; // Set to true to use dynamic columns

  private subs = new SubSink();

  constructor() {
    // Use effect() for reactive handling of signal changes
    effect(() => {
      const currentEmployees = this.employees();
      // Since filtering is now handled by the API, we can directly use the employees from the store
      this.filteredEmployees = currentEmployees || [];
    });

    effect(() => {
      const currentError = this.error();
      if (currentError && currentError !== this.lastError) {
        console.error('Error loading employees:', currentError);
        this.lastError = currentError;
        // You could show a toast or notification here
      }
    });

    // Subscribe to departments to populate dropdown options
    effect(() => {
      const departments = this.departmentsStore.departments();
      console.log('Departments loaded:', departments);
      if (departments && departments.length > 0) {
        // Map departments to dropdown options from API
        this.departmentOptions = [
          { value: '', label: 'All Departments' }, // Keep the "All Departments" option
          ...departments.map(dept => ({
            value: dept.id,
            label: dept.name
          }))
        ];

        console.log('Department options updated from API:', this.departmentOptions);
      } else {
        console.log('No departments available from API');
        // Clear options if no departments are loaded
        this.departmentOptions = [{ value: '', label: 'All Departments' }];
      }
    });
  }

  ngOnInit(): void {
    // Load employees from the store with pagination
    this.employeesStore.loadEmployees({});

    // Load departments for filtering - force reload to ensure we have the latest data
    this.departmentsStore.loadAllDepartments();

    // Initialize table filters
    this.tableFilters = [
      { label: 'Department', value: 'department' }
    ];
  }

  ngOnDestroy(): void {
    // Clean up all subscriptions
    this.subs.unsubscribe();
  }

  /**
   * Transform employee data to match the table columns
   * @param employees The raw employee data from the API
   * @returns Transformed employee data for display
   */


  applyFilters(): void {
    const employeesData = this.employees();
    if (!employeesData || employeesData.length === 0) {
      this.filteredEmployees = [];
      return;
    }

    let filtered = [...employeesData];

    // Apply department filter using multi-select
    if (this.selectedDepartmentIds && this.selectedDepartmentIds.length > 0) {
      // If "All Departments" is selected (empty string), don't filter
      if (!(this.selectedDepartmentIds.length === 1 && this.selectedDepartmentIds[0] === '')) {
        filtered = filtered.filter(employee => {
          if (!employee.department) return false;

          // Handle department as either string, number, or object
          let departmentId: number | string = 0;

          if (typeof employee.department === 'object' && employee.department) {
            if ('id' in employee.department) {
              departmentId = (employee.department as any).id;
            }
          } else if (typeof employee.department === 'number') {
            departmentId = employee.department;
          } else if (typeof employee.department === 'string' && !isNaN(Number(employee.department))) {
            departmentId = Number(employee.department);
          }

          // Check if the employee's department ID is in the selected departments
          return this.selectedDepartmentIds.includes(departmentId);
        });
      }
    }

    // Apply status filter
    const statusFilter = this.filters.find(f => f.name === 'status');
    if (statusFilter && statusFilter.selectedValue) {
      filtered = filtered.filter(employee => {
        if (!employee.status) return false;

        if (statusFilter.selectedValue === 'active') {
          return employee.status === 'active';
        } else if (statusFilter.selectedValue === 'on-leave') {
          return employee.status === 'on_leave';
        } else if (statusFilter.selectedValue === 'remote') {
          // Handle remote status - since it's not in the Employee type, we need to check differently
          const status = employee.status.toLowerCase();
          return status.includes('remote');
        }
        return true;
      });
    }

    // Apply date range filter
    if (this.startDate && this.endDate) {
      filtered = filtered.filter(employee => {
        if (!employee.hire_date) return false;

        const date = new Date(employee.hire_date);
        return date >= this.startDate && date <= this.endDate;
      });
    }

    // Transform and set filtered employees
    this.filteredEmployees = filtered;
  }

  onSearch(query: string): void {
    this.searchQuery = query;
    this.employeesStore.setSearchTerm(query);
  }

  onExport(): void {
    console.log('Exporting employees data...');
    // Implement export functionality
  }

  onAddEmployee(): void {
    // Open the add employee modal
    this.sharedModalService.open({ id: 'add-employee-modal' });
  }

  onDateRangeChange(dateRange: { start: Date, end: Date }): void {
    this.startDate = dateRange.start;
    this.endDate = dateRange.end;
    this.applyFilters();
  }

  onFilterChange(filter: { name: string, value: string | number }): void {
    const filterIndex = this.filters.findIndex(f => f.name === filter.name);
    if (filterIndex !== -1) {
      this.filters[filterIndex].selectedValue = filter.value;

      // For now, we'll handle filters client-side
      // In a real implementation, you would send these filters to the API
      this.employeesStore.setPage(1); // Reset to first page
      this.applyFilters();
    }
  }

  onDepartmentFilterChange(selectedDepartmentIds: (string | number)[]): void {
    console.log('Department filter changed:', selectedDepartmentIds);

    // Store the selected department IDs as-is
    this.selectedDepartmentIds = selectedDepartmentIds;

    // For API calls, determine what to send based on selection
    let apiDepartmentIds: (string | number)[];

    if (selectedDepartmentIds.length === 0) {
      // No departments selected - show all departments
      apiDepartmentIds = [''];
    } else if (selectedDepartmentIds.includes('')) {
      // "All Departments" is selected - show all departments (don't send department filter)
      apiDepartmentIds = [''];
    } else {
      // Specific departments selected - filter by those departments
      apiDepartmentIds = selectedDepartmentIds;
    }

    // Use the store's department filter method to trigger API call
    this.employeesStore.setDepartmentFilter(apiDepartmentIds);

    // Show toast notification
    if (selectedDepartmentIds.length === 0 || selectedDepartmentIds.includes('')) {
      this.toastService.info('Showing all departments');
    } else {
      const departmentNames = selectedDepartmentIds
        .filter(id => id !== '') // Exclude "All Departments" from the list
        .map(id => {
          const dept = this.departmentOptions.find(opt => opt.value === id);
          return dept ? dept.label : id;
        }).join(', ');

      this.toastService.info(`Filtered by departments: ${departmentNames}`);
    }
  }

  onRowClick(employee: Employee): void {
    // Select the employee in the store
    this.employeesStore.selectEmployee(employee);

    // Navigate to employee detail page
    this.router.navigate(['/app/employees', employee.id]);
  }

  onSelectionChange(selection: Employee[]): void {
    this.selectedEmployees = selection;
    console.log('Selected employees:', this.selectedEmployees);
  }

  onPageChange(page: number): void {
    this.employeesStore.setPage(page);
  }

  onPageSizeChange(size: number): void {
    this.employeesStore.setPageSize(size);
  }

  onTableFilterClick(filter: { label: string, value: string }): void {
    // This method is kept for backward compatibility
    // Department filtering is now handled by the multi-select dropdown
    console.log(`Filter clicked: ${filter.label}`);
  }

  /**
   * Handle view employee action
   * @param employee The employee to view
   */
  onViewEmployee(employee: Employee): void {
    console.log('View employee:', employee);

    // Load the employee details
    this.employeesStore.loadEmployee(employee.id);

    // Navigate to employee detail page
    this.router.navigate(['/app/employees', employee.id]);
  }

  /**
   * Handle edit employee action
   * @param employee The employee to edit
   */
  onEditEmployee(employee: Employee): void {
    console.log('Edit employee:', employee);

    // Load the employee details first to ensure we have the latest data
    this.employeesStore.loadEmployee(employee.id);

    // Set up a watcher to wait for the employee to be loaded
    const employeeWatcher = setInterval(() => {
      const selectedEmployee = this.employeesStore.selectedEmployee();
      if (selectedEmployee && selectedEmployee.id === employee.id) {
        clearInterval(employeeWatcher);

        // Open the add employee modal with the employee data
        this.sharedModalService.open({
          id: 'add-employee-modal',
          data: {
            isEdit: true,
            employee: selectedEmployee
          }
        });
      }
    }, 100);
  }

  /**
   * Handle delete employee action
   * @param employee The employee to delete
   */
  async onDeleteEmployee(employee: Employee): Promise<void> {
    console.log('Delete employee:', employee);

    // Use the same modal service pattern as department delete
    const confirmed = await this.coreModalService.confirm({
      title: 'Delete Employee',
      message: `Are you sure you want to delete ${employee.first_name} ${employee.last_name}? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel'
    });

    if (confirmed) {
      // Delete the employee
      this.employeesStore.deleteEmployee(employee.id);

      // Show success message
      this.toastService.success(`Employee ${employee.first_name} ${employee.last_name} has been deleted.`);
    }
  }

  /**
   * Confirm employee deletion (kept for backward compatibility)
   */
  confirmDeleteEmployee(): void {
    if (!this.employeeToDelete) return;

    // Delete the employee
    this.employeesStore.deleteEmployee(this.employeeToDelete.id);

    // Show success message
    this.toastService.success(`Employee ${this.employeeToDelete.first_name} ${this.employeeToDelete.last_name} has been deleted.`);

    // Reset state
    this.employeeToDelete = null;
    this.showDeleteConfirmation = false;
  }

  /**
   * Cancel employee deletion (kept for backward compatibility)
   */
  cancelDeleteEmployee(): void {
    this.employeeToDelete = null;
    this.showDeleteConfirmation = false;
  }


}
