@use 'sass:color';
@import '../../../../styles/variables/_colors';

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.department-form {
  padding: 16px 0;

  &__group {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: $gray-700;
    margin-bottom: 8px;
    line-height: 1.4;
  }

  &__required {
    color: $danger;
    margin-left: 2px;
    font-weight: 600;
  }

  &__input,
  &__textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid $gray-300;
    border-radius: 12px;
    font-size: 14px;
    color: $gray-800;
    background-color: $white;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    &::placeholder {
      color: $gray-500;
    }

    &:hover {
      border-color: $gray-400;
    }

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.15);
    }

    &--invalid {
      border-color: $danger;
      background-color: rgba($danger, 0.02);

      &:focus {
        box-shadow: 0 0 0 3px rgba($danger, 0.15);
      }
    }
  }

  &__textarea {
    resize: vertical;
    min-height: 100px;
  }

  &__error {
    font-size: 12px;
    color: $danger;
    margin-top: 6px;
    line-height: 1.4;
    display: flex;
    align-items: center;
    gap: 6px;

    app-icon {
      color: $danger;
      flex-shrink: 0;
    }

    &::before {
      content: '⚠';
      font-size: 10px;
    }
  }

  &__retry-button {
    margin-left: 8px;
    padding: 4px 8px;
    background-color: $primary;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 11px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background-color: color.adjust($primary, $lightness: -10%);
      transform: translateY(-1px);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    app-icon {
      font-size: 10px;
    }
  }

  &__info {
    color: $gray-600;
    font-size: 12px;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 6px;
    line-height: 1.4;

    app-icon {
      color: $gray-500;
    }
  }

  &__loading-indicator {
    margin-left: 8px;
    color: $primary;

    app-icon {
      animation: spin 1s linear infinite;
    }
  }

  // Enhanced dropdown styling integration
  app-searchable-dropdown {
    display: block;
    width: 100%;

    ::ng-deep .searchable-dropdown {
      width: 100%;

      &__toggle {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid $gray-300;
        border-radius: 12px;
        font-size: 14px;
        color: $gray-800;
        background-color: $white;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

        &:hover {
          border-color: $gray-400;
        }

        &:focus {
          outline: none;
          border-color: $primary;
          box-shadow: 0 0 0 3px rgba($primary, 0.15);
        }
      }

      // Enhanced menu styling for manager dropdown
      &__menu {
        max-height: 320px; // Increased height to show 6-8 options
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        border: 1px solid $gray-300;
      }

      &__options {
        max-height: 280px; // Adjusted for search area
        overflow-y: auto;

        // Custom scrollbar styling
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: $gray-100;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: $gray-400;
          border-radius: 3px;

          &:hover {
            background: $gray-500;
          }
        }
      }

      // Enhanced option styling for employee display
      &__option {
        padding: 14px 16px; // Increased padding for better touch targets
        min-height: 56px; // Ensure consistent height for employee options

        &:hover:not(.is-disabled) {
          background-color: rgba($primary, 0.05);
          border-left: 3px solid $primary;
        }

        &.is-active {
          background-color: rgba($primary, 0.1);
          border-left: 3px solid $primary;
        }
      }

      &__option-content {
        flex: 1;
        min-width: 0;
      }

      &__option-label {
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 2px;
      }

      &__option-subtitle {
        font-size: 12px;
        color: $gray-600;
        line-height: 1.3;
      }

      &.is-invalid .searchable-dropdown__toggle {
        border-color: $danger;
        background-color: rgba($danger, 0.02);

        &:focus {
          box-shadow: 0 0 0 3px rgba($danger, 0.15);
        }
      }

      &.is-disabled .searchable-dropdown__toggle {
        background-color: $gray-100;
        color: $gray-500;
        cursor: not-allowed;
      }
    }

    // Special styling for manager dropdown
    &#manager-dropdown {
      ::ng-deep .searchable-dropdown {
        &__menu {
          max-height: 360px; // Even more height for manager selection
        }

        &__options {
          max-height: 320px;
        }

        &__option {
          // Enhanced styling for employee options
          &:first-child {
            // "No Manager" option styling
            border-bottom: 1px solid $gray-200;
            margin-bottom: 4px;

            .searchable-dropdown__option-content {
              .searchable-dropdown__option-label {
                color: $gray-600;
                font-style: italic;
              }
            }
          }
        }
      }
    }
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid $gray-200;

    @media (max-width: 576px) {
      flex-direction: column;
      gap: 8px;
    }
  }

  &__button {
    min-width: 120px;

    @media (max-width: 576px) {
      width: 100%;
    }
  }
}

// Responsive design enhancements for mobile devices
@media (max-width: 768px) {
  .department-form {
    app-searchable-dropdown {
      ::ng-deep .searchable-dropdown {
        &__menu {
          max-height: 280px; // Reduced height for mobile
        }

        &__options {
          max-height: 240px;
        }

        &__option {
          padding: 12px 14px; // Slightly reduced padding for mobile
          min-height: 48px; // Reduced min-height for mobile
        }
      }

      // Manager dropdown mobile adjustments
      &#manager-dropdown {
        ::ng-deep .searchable-dropdown {
          &__menu {
            max-height: 300px;
          }

          &__options {
            max-height: 260px;
          }
        }
      }
    }

    &__retry-button {
      padding: 6px 10px;
      font-size: 12px;
    }
  }
}

@media (max-width: 480px) {
  .department-form {
    &__group {
      margin-bottom: 20px;
    }

    &__label {
      font-size: 13px;
    }

    &__input,
    &__textarea {
      padding: 10px 14px;
      font-size: 13px;
    }

    app-searchable-dropdown {
      ::ng-deep .searchable-dropdown {
        &__toggle {
          padding: 10px 14px;
          font-size: 13px;
        }

        &__menu {
          max-height: 240px; // Further reduced for small screens
        }

        &__options {
          max-height: 200px;
        }

        &__option {
          padding: 10px 12px;
          min-height: 44px;
        }

        &__option-label {
          font-size: 13px;
        }

        &__option-subtitle {
          font-size: 11px;
        }
      }
    }
  }
}
