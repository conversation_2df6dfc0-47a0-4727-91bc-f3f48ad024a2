<div class="toast-container" *ngIf="toasts.length > 0">
  <div 
    *ngFor="let toast of toasts" 
    class="toast" 
    [ngClass]="'toast-' + toast.type"
    [@toastAnimation]
    (click)="removeToast(toast.id)">
    <div class="toast-icon">
      <i class="fa" [ngClass]="getIconClass(toast.type)"></i>
    </div>
    <div class="toast-content">
      <div class="toast-message">{{ toast.message }}</div>
    </div>
    <button class="toast-close" (click)="removeToast(toast.id); $event.stopPropagation()">
      <i class="fa fa-times"></i>
    </button>
  </div>
</div>
