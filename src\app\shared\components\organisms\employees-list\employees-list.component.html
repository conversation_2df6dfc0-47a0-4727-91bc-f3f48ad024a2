<div class="employees">
  <div class="employees__header">
    <div class="employees__title-section">
      <div class="employees__title-row">
        <h1 class="employees__page-title">Employee</h1>
        <div class="employees__actions">
          <button class="btn btn-export" (click)="onExport()">
            <i class="fa fa-download"></i>
            <span>Export</span>
          </button>

          <button class="btn btn-primary" (click)="onAddEmployee()">
            <i class="fa fa-plus"></i>
            <span>Add Employee</span>
          </button>
        </div>
      </div>
      <div class="employees__breadcrumb">
        <span class="employees__breadcrumb-item"><i class="fa fa-home"></i></span>
        <span class="employees__breadcrumb-separator">/</span>
        <span class="employees__breadcrumb-item">Employees</span>
        <span class="employees__breadcrumb-separator">/</span>
        <span class="employees__breadcrumb-item employees__breadcrumb-item--active">Employee List</span>
      </div>
    </div>
  </div>

  <!-- Employee Statistics Cards -->
  <app-employee-statistics-cards
    [statistics]="statistics"
    [isLoading]="isLoading()">
  </app-employee-statistics-cards>

  <div class="employees__table-container">
    <app-data-table-container
      [columns]="columns"
      [data]="filteredEmployees"
      [selectable]="true"
      [showPagination]="true"
      [showHeader]="true"
      [tableTitle]="'Plan List'"
      [currentPage]="currentPage"
      [pageSize]="currentPageSize"
      [totalItems]="totalItems"
      [filters]="tableFilters"
      [departmentOptions]="departmentOptions"
      [selectedDepartmentIds]="selectedDepartmentIds"
      [dynamicColumns]="useDynamicColumns"
      (rowClick)="onRowClick($event)"
      (selectionChange)="onSelectionChange($event)"
      (pageChange)="onPageChange($event)"
      (pageSizeChange)="onPageSizeChange($event)"
      (filterClick)="onTableFilterClick($event)"
      (departmentFilterChange)="onDepartmentFilterChange($event)"
      (search)="onSearch($event)"
      (clearSearch)="onSearch('')"
      (export)="onExport()"
      (addEmployee)="onAddEmployee()"
      (viewAction)="onViewEmployee($event)"
      (editAction)="onEditEmployee($event)"
      (deleteAction)="onDeleteEmployee($event)">
    </app-data-table-container>
  </div>
</div>

<!-- Add Employee Modal -->
<app-add-employee-modal></app-add-employee-modal>

<!-- Toast Notifications -->
<app-toast></app-toast>

<!-- Confirmation Modal for Delete -->
<app-confirmation-modal
  [title]="'Delete Employee'"
  [message]="'Are you sure you want to delete this employee? This action cannot be undone.'"
  [confirmButtonText]="'Delete'"
  [cancelButtonText]="'Cancel'"
  [isOpen]="showDeleteConfirmation"
  (confirm)="confirmDeleteEmployee()"
  (cancel)="cancelDeleteEmployee()">
</app-confirmation-modal>
