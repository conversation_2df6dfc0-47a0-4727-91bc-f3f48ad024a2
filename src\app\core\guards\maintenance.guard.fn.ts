import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { map, take } from 'rxjs/operators';
import { MaintenanceService } from '../services/maintenance.service';

export const maintenanceGuard: CanActivateFn = (route, state) => {
  const maintenanceService = inject(MaintenanceService);
  const router = inject(Router);

  // Skip maintenance check for the maintenance page itself
  if (state.url === '/maintenance') {
    return true;
  }

  return maintenanceService.maintenanceMode$.pipe(
    take(1),
    map(isInMaintenance => {
      if (isInMaintenance) {
        // Redirect to maintenance page if the site is in maintenance mode
        router.navigate(['/maintenance']);
        return false;
      }
      return true;
    })
  );
};
