@import '../../../../../styles/variables/colors';

.searchable-dropdown {
  position: relative;
  width: 100%;

  &__toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 12px 16px;
    background: linear-gradient(to bottom, #ffffff, #f8f9fa);
    border: 1px solid $gray-300;
    border-radius: 12px;
    font-size: 14px;
    color: $gray-800;
    cursor: pointer;
    text-align: left;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    &:hover {
      border-color: $gray-400;
      background: linear-gradient(to bottom, #f8f9fa, #f1f3f5);
    }

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.15);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background: $gray-100;
    }
  }

  &__selected-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__toggle-icon {
    margin-left: 8px;
    transition: transform 0.2s ease;
  }

  &.is-open &__toggle-icon {
    transform: rotate(180deg);
  }

  &__menu {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    width: 100%;
    background-color: white;
    border: 1px solid $gray-300;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;
    max-height: 300px;
    display: flex;
    flex-direction: column;
  }

  &__search {
    position: relative;
    padding: 8px;
    border-bottom: 1px solid $gray-200;
  }

  &__search-input {
    width: 100%;
    padding: 8px 32px 8px 12px;
    border: 1px solid $gray-300;
    border-radius: 8px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba($primary, 0.1);
    }
  }

  &__search-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: $gray-500;
  }

  &__options {
    overflow-y: auto;
    max-height: 240px;

    // Enhanced scrollbar styling for better UX
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: $gray-100;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: $gray-400;
      border-radius: 3px;

      &:hover {
        background: $gray-500;
      }
    }

    // Firefox scrollbar styling
    scrollbar-width: thin;
    scrollbar-color: $gray-400 $gray-100;
  }

  &__option {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 2px 8px;

    &:hover:not(.is-disabled) {
      background-color: $gray-100;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    &.is-active {
      background-color: rgba($primary, 0.1);
      color: $primary;
      font-weight: 500;
      border: 1px solid rgba($primary, 0.2);
    }

    &.is-disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background-color: $gray-100;
    }
  }

  &__option-icon {
    margin-right: 12px;
    color: $gray-600;
  }

  &__option-avatar {
    margin-right: 12px;

    .avatar-image {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid $gray-200;
    }
  }

  &__option-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; // Allow text truncation
  }

  &__option-label {
    font-size: 14px;
    font-weight: 500;
    color: $gray-800;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
  }

  &__option-subtitle {
    font-size: 12px;
    color: $gray-600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 2px;
    line-height: 1.3;
  }

  &__no-results {
    padding: 12px 16px;
    color: $gray-600;
    text-align: center;
    font-style: italic;
  }

  &.is-invalid &__toggle {
    border-color: $danger;
    background-color: rgba($danger, 0.02);

    &:focus {
      box-shadow: 0 0 0 3px rgba($danger, 0.15);
    }
  }
}
