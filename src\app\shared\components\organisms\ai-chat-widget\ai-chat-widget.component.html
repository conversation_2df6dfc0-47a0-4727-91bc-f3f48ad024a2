<!-- AI Chat Widget Container -->
<div class="ai-chat-widget"
     [class.ai-chat-widget--expanded]="isExpanded()">

  <!-- Floating Chat Bubble (Collapsed State) -->
  <div class="chat-bubble"
       *ngIf="!isExpanded()"
       (click)="expandWidget()"
       [attr.aria-label]="'Open AI Assistant Chat'"
       role="button"
       tabindex="0"
       (keydown.enter)="expandWidget()"
       (keydown.space)="expandWidget()">

    <!-- Chat Icon -->
    <div class="chat-bubble__icon">
      <app-icon name="fas fa-headset" size="lg"></app-icon>
    </div>

    <!-- Notification Indicator -->
    <div class="chat-bubble__notification"
         *ngIf="hasNewMessage()"
         [attr.aria-label]="'New message available'">
      <span class="notification-dot"></span>
    </div>

    <!-- Tooltip -->
    <div class="chat-bubble__tooltip">
      AI Assistant
    </div>
  </div>

  <!-- Chat Panel (Expanded State) -->
  <div class="chat-panel" 
       *ngIf="isExpanded()"
       role="dialog"
       aria-labelledby="chat-header-title"
       aria-describedby="chat-messages">
    
    <!-- Chat Header -->
    <div class="chat-header">
      <div class="chat-header__content">
        <div class="chat-header__avatar">
          <app-icon name="fas fa-headset" size="md"></app-icon>
        </div>
        <div class="chat-header__info">
          <h3 id="chat-header-title" class="chat-header__title">AI Assistant</h3>
          <p class="chat-header__status" *ngIf="!isLoading()">Online</p>
          <p class="chat-header__status chat-header__status--typing" *ngIf="isLoading()">
            <span class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </span>
            Typing...
          </p>
        </div>
      </div>
      
      <div class="chat-header__actions">
        <!-- Clear Chat Button -->
        <button class="chat-header__action" 
                (click)="clearChat()"
                [attr.aria-label]="'Clear chat history'"
                title="Clear chat">
          <app-icon name="fas fa-trash" size="sm"></app-icon>
        </button>
        
        <!-- Minimize Button -->
        <button class="chat-header__action" 
                (click)="collapseWidget()"
                [attr.aria-label]="'Minimize chat'"
                title="Minimize">
          <app-icon name="fas fa-minus" size="sm"></app-icon>
        </button>
        
        <!-- Close Button -->
        <button class="chat-header__action chat-header__action--close" 
                (click)="collapseWidget()"
                [attr.aria-label]="'Close chat'"
                title="Close">
          <app-icon name="fas fa-times" size="sm"></app-icon>
        </button>
      </div>
    </div>

    <!-- Error Banner -->
    <div class="error-banner" *ngIf="error()">
      <div class="error-banner__content">
        <app-icon name="fas fa-exclamation-triangle" size="sm"></app-icon>
        <span class="error-banner__message">{{ error() }}</span>
        <button class="error-banner__retry" 
                (click)="retryLastMessage()"
                title="Retry">
          <app-icon name="fas fa-redo" size="sm"></app-icon>
        </button>
        <button class="error-banner__close" 
                (click)="clearError()"
                title="Dismiss">
          <app-icon name="fas fa-times" size="sm"></app-icon>
        </button>
      </div>
    </div>

    <!-- Messages Container -->
    <div class="chat-messages" 
         #messagesContainer
         id="chat-messages"
         [attr.aria-live]="'polite'"
         [attr.aria-atomic]="'false'">
      
      <!-- Welcome Message -->
      <div class="welcome-message" *ngIf="messages().length === 0">
        <div class="welcome-message__avatar">
          <app-icon name="fas fa-headset" size="lg"></app-icon>
        </div>
        <div class="welcome-message__content">
          <h4>Hello! I'm your AI Assistant</h4>
          <p>I'm here to help you with questions about the company, policies, procedures, and more. What would you like to know?</p>
          <div class="welcome-message__suggestions">
            <button class="suggestion-chip" (click)="messageForm.patchValue({message: 'What is the company leave policy?'})">
              Leave Policy
            </button>
            <button class="suggestion-chip" (click)="messageForm.patchValue({message: 'How do I submit an expense report?'})">
              Expense Reports
            </button>
            <button class="suggestion-chip" (click)="messageForm.patchValue({message: 'What are the working hours?'})">
              Working Hours
            </button>
          </div>
        </div>
      </div>

      <!-- Chat Messages -->
      <div class="messages-list" *ngIf="messages().length > 0">
        <div class="message-wrapper" 
             *ngFor="let message of messages(); trackBy: trackMessage"
             [ngClass]="getMessageClasses(message)">
          
          <!-- AI Message -->
          <div class="message message--ai" *ngIf="message.sender === 'ai'">
            <div class="message__avatar">
              <app-icon name="fas fa-headset" size="sm"></app-icon>
            </div>
            <div class="message__content">
              <div class="message__bubble" *ngIf="!message.isLoading">
                <div class="message__text" [innerHTML]="message.content"></div>
                <div class="message__timestamp">{{ formatTimestamp(message.timestamp) }}</div>
              </div>
              
              <!-- Loading State -->
              <div class="message__bubble message__bubble--loading" *ngIf="message.isLoading">
                <div class="loading-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>

          <!-- User Message -->
          <div class="message message--user" *ngIf="message.sender === 'user'">
            <div class="message__content">
              <div class="message__bubble">
                <div class="message__text">{{ message.content }}</div>
                <div class="message__timestamp">{{ formatTimestamp(message.timestamp) }}</div>
              </div>
            </div>
            <div class="message__avatar">
              <app-icon name="fas fa-user" size="sm"></app-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Message Input -->
    <div class="chat-input">
      <form [formGroup]="messageForm" (ngSubmit)="sendMessage()" class="chat-input__form">
        <div class="chat-input__container">
          <textarea #messageInput
                    formControlName="message"
                    class="chat-input__field message-input"
                    [placeholder]="getInputPlaceholder()"
                    [disabled]="isInputDisabled()"
                    rows="1"
                    maxlength="1000"
                    [attr.aria-label]="'Type your message'"
                    (keydown.enter)="onEnterKeyPress($any($event))">
          </textarea>
          
          <button type="submit" 
                  class="chat-input__send"
                  [disabled]="messageForm.invalid || isLoading()"
                  [attr.aria-label]="'Send message'"
                  title="Send message (Enter)">
            <app-icon [name]="isLoading() ? 'fas fa-spinner fa-spin' : 'fas fa-paper-plane'" size="sm"></app-icon>
          </button>
        </div>
        
        <!-- Character Counter -->
        <div class="chat-input__counter" *ngIf="messageForm.get('message')?.value?.length > 800">
          {{ messageForm.get('message')?.value?.length || 0 }}/1000
        </div>
      </form>
    </div>
  </div>
</div>
