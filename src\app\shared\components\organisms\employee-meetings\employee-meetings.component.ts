import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-meetings',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="meetings-container">
      <div class="meetings-header">
        <h3 class="section-title">Meetings & Schedule</h3>
        <div class="header-actions">
          <button class="action-btn">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      
      <div class="meetings-content">
        <div class="meeting-item">
          <div class="meeting-time">09:25 AM</div>
          <div class="meeting-details">
            <h4 class="meeting-title">Marketing Strategy Presentation</h4>
            <p class="meeting-location">Meeting Room 1</p>
          </div>
          <div class="meeting-actions">
            <button class="action-btn join">
              <i class="fa fa-video"></i>
              <span>Join</span>
            </button>
          </div>
        </div>
        
        <div class="meeting-item">
          <div class="meeting-time">10:30 AM</div>
          <div class="meeting-details">
            <h4 class="meeting-title">Design Review: Insight Dashboard</h4>
            <p class="meeting-location">Meeting Room 2</p>
          </div>
          <div class="meeting-actions">
            <button class="action-btn join">
              <i class="fa fa-video"></i>
              <span>Join</span>
            </button>
          </div>
        </div>
        
        <div class="meeting-item">
          <div class="meeting-time">01:00 PM</div>
          <div class="meeting-details">
            <h4 class="meeting-title">Design Coordinator of Branding</h4>
            <p class="meeting-location">Meeting Room 3</p>
          </div>
          <div class="meeting-actions">
            <button class="action-btn join">
              <i class="fa fa-video"></i>
              <span>Join</span>
            </button>
          </div>
        </div>
        
        <div class="meeting-item">
          <div class="meeting-time">03:00 PM</div>
          <div class="meeting-details">
            <h4 class="meeting-title">Update of Project Plan</h4>
            <p class="meeting-location">Meeting Room 1</p>
          </div>
          <div class="meeting-actions">
            <button class="action-btn join">
              <i class="fa fa-video"></i>
              <span>Join</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .meetings-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .meetings-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e9ecef;
    }
    
    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }
    
    .action-btn {
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      font-size: 16px;
    }
    
    .meetings-content {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      overflow-y: auto;
    }
    
    .meeting-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
    }
    
    .meeting-time {
      font-size: 14px;
      font-weight: 600;
      color: #343a40;
      min-width: 80px;
    }
    
    .meeting-details {
      flex: 1;
      min-width: 0;
    }
    
    .meeting-title {
      margin: 0 0 4px 0;
      font-size: 14px;
      font-weight: 500;
      color: #343a40;
    }
    
    .meeting-location {
      margin: 0;
      font-size: 12px;
      color: #6c757d;
    }
    
    .meeting-actions {
      margin-left: 12px;
    }
    
    .action-btn.join {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 12px;
      background-color: #e8f5e9;
      color: #4caf50;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .action-btn.join:hover {
      background-color: #d4edda;
    }
  `]
})
export class EmployeeMeetingsComponent {
  @Input() employee: any;
}
