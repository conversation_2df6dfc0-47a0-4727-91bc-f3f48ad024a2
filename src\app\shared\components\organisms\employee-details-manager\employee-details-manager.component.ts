import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';

// Import molecules and atoms
import { ButtonComponent } from '../../atoms/button/button.component';

// Import stores and interfaces
import { EmployeeDetailsStore } from '../../../../core/state/employee-details/employee-details.state';
import {
  EmployeeDetails,
  EmployeeDetailsInput,
  EmployeeDocument,
  EmployeeSkill,
  EmployeeJobHistory
} from '../../../../core/models/employee-extended.interface';
import { Employee } from '../../../../core/state/employees/employees.state';

export type EmployeeDetailsView = 'overview' | 'edit' | 'documents' | 'skills' | 'history';

@Component({
  selector: 'app-employee-details-manager',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent
  ],
  templateUrl: './employee-details-manager.component.html',
  styleUrls: ['./employee-details-manager.component.scss']
})
export class EmployeeDetailsManagerComponent implements OnInit, OnDestroy {
  @Input() employee: Employee | null = null;
  @Input() initialView: EmployeeDetailsView = 'overview';

  @Output() viewChange = new EventEmitter<EmployeeDetailsView>();
  @Output() detailsUpdated = new EventEmitter<EmployeeDetails>();
  @Output() close = new EventEmitter<void>();

  private employeeDetailsStore = inject(EmployeeDetailsStore);
  private destroy$ = new Subject<void>();

  currentView: EmployeeDetailsView = 'overview';

  // Store signals
  employeeDetails = this.employeeDetailsStore.selectedEmployeeDetails;
  documents = this.employeeDetailsStore.documents;
  skills = this.employeeDetailsStore.skills;
  jobHistory = this.employeeDetailsStore.jobHistory;

  // Loading states
  isLoading = this.employeeDetailsStore.isAnyLoading;
  isLoadingDetails = this.employeeDetailsStore.isLoadingDetails;
  isLoadingDocuments = this.employeeDetailsStore.isLoadingDocuments;
  isLoadingSkills = this.employeeDetailsStore.isLoadingSkills;
  isLoadingJobHistory = this.employeeDetailsStore.isLoadingJobHistory;

  // Error states
  error = this.employeeDetailsStore.error;
  detailsError = this.employeeDetailsStore.detailsError;
  documentsError = this.employeeDetailsStore.documentsError;
  skillsError = this.employeeDetailsStore.skillsError;
  jobHistoryError = this.employeeDetailsStore.jobHistoryError;

  // Computed properties
  hasEmployeeDetails = this.employeeDetailsStore.hasEmployeeDetails;
  hasDocuments = this.employeeDetailsStore.hasDocuments;
  hasSkills = this.employeeDetailsStore.hasSkills;
  hasJobHistory = this.employeeDetailsStore.hasJobHistory;
  skillsByCategory = this.employeeDetailsStore.skillsByCategory;
  documentsByType = this.employeeDetailsStore.documentsByType;

  // View configuration
  views: { id: EmployeeDetailsView; label: string; icon: string }[] = [
    { id: 'overview', label: 'Overview', icon: '📋' },
    { id: 'edit', label: 'Edit Details', icon: '✏️' },
    { id: 'documents', label: 'Documents', icon: '📄' },
    { id: 'skills', label: 'Skills', icon: '🎯' },
    { id: 'history', label: 'Job History', icon: '📈' }
  ];

  constructor() {
    // Watch for employee changes
    effect(() => {
      const currentEmployee = this.employee;
      if (currentEmployee?.id) {
        this.loadEmployeeData(currentEmployee.id);
      }
    });

    // Watch for details updates
    effect(() => {
      const details = this.employeeDetails();
      if (details) {
        this.detailsUpdated.emit(details);
      }
    });
  }

  ngOnInit(): void {
    this.currentView = this.initialView;

    if (this.employee?.id) {
      this.loadEmployeeData(this.employee.id);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.employeeDetailsStore.clearSelectedEmployee();
  }

  private loadEmployeeData(employeeId: number): void {
    // Load all employee-related data
    this.employeeDetailsStore.loadAllEmployeeData(employeeId);
  }

  onViewChange(view: EmployeeDetailsView): void {
    this.currentView = view;
    this.viewChange.emit(view);
  }

  onDetailsFormSubmit(detailsInput: EmployeeDetailsInput): void {
    const currentDetails = this.employeeDetails();

    if (currentDetails) {
      // Update existing details
      this.employeeDetailsStore.updateEmployeeDetails({
        id: currentDetails.id,
        details: detailsInput
      });
    } else {
      // Create new details
      this.employeeDetailsStore.createEmployeeDetails(detailsInput);
    }

    // Switch back to overview after successful submission
    this.onViewChange('overview');
  }

  onDetailsFormCancel(): void {
    this.onViewChange('overview');
  }

  onClose(): void {
    this.close.emit();
  }

  refreshData(): void {
    if (this.employee?.id) {
      this.loadEmployeeData(this.employee.id);
    }
  }

  // Helper methods for data display
  formatDate(dateString: string | undefined): string {
    if (!dateString) return 'Not provided';

    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  }

  formatFileSize(bytes: number | undefined): string {
    if (!bytes) return 'Unknown size';

    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  getDocumentTypeLabel(type: string): string {
    const labels: { [key: string]: string } = {
      'resume': 'Resume',
      'contract': 'Contract',
      'id_proof': 'ID Proof',
      'address_proof': 'Address Proof',
      'education': 'Education Certificate',
      'certification': 'Professional Certification',
      'other': 'Other Document'
    };
    return labels[type] || type;
  }

  getSkillCategoryLabel(category: string): string {
    const labels: { [key: string]: string } = {
      'technical': 'Technical Skills',
      'soft': 'Soft Skills',
      'language': 'Languages',
      'certification': 'Certifications',
      'other': 'Other Skills'
    };
    return labels[category] || category;
  }

  getProficiencyColor(level: string): string {
    const colors: { [key: string]: string } = {
      'beginner': '#f59e0b',
      'intermediate': '#3b82f6',
      'advanced': '#10b981',
      'expert': '#8b5cf6'
    };
    return colors[level] || '#6b7280';
  }

  get isEditMode(): boolean {
    return this.currentView === 'edit';
  }

  get hasAnyData(): boolean {
    return this.hasEmployeeDetails() || this.hasDocuments() || this.hasSkills() || this.hasJobHistory();
  }

  get employeeFullName(): string {
    if (!this.employee) return 'Unknown Employee';
    return `${this.employee.first_name} ${this.employee.last_name}`;
  }

  // Expose Object for template use
  Object = Object;
}
