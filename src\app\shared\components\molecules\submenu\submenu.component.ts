import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuItemComponent } from '../../atoms/menu-item/menu-item.component';
import { MenuItem } from '../menu-group/menu-group.component';

@Component({
  selector: 'app-submenu',
  standalone: true,
  imports: [CommonModule, MenuItemComponent],
  templateUrl: './submenu.component.html',
  styleUrl: './submenu.component.scss'
})
export class SubmenuComponent {
  @Input() items: MenuItem[] = [];
  @Input() expanded: boolean = false;
  @Input() collapsed: boolean = false;
}
