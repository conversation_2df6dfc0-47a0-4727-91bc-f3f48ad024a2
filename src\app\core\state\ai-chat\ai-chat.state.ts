import { Injectable, inject } from '@angular/core';
import { signalStore, withState, withMethods, patchState, withHooks } from '@ngrx/signals';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, switchMap, tap, catchError, of, finalize } from 'rxjs';
import { ChatMessage, ChatState, ChatWidgetConfig } from '../../models/ai-chat.interface';
import { AiChatService } from '../../services/ai-chat.service';

// Initial state
const initialState: ChatState = {
  messages: [],
  isExpanded: false,
  isLoading: false,
  error: null,
  hasNewMessage: false,
  conversationId: undefined
};

// Default configuration
const defaultConfig: ChatWidgetConfig = {
  position: 'bottom-right',
  theme: 'auto',
  enableNotifications: true,
  enablePersistence: true,
  maxMessages: 100
};

@Injectable({
  providedIn: 'root'
})
export class AiChatStore extends signalStore(
  withState(initialState),
  withMethods((store, aiChatService = inject(AiChatService)) => ({
    
    /**
     * Toggle chat widget expanded state
     */
    toggleExpanded: () => {
      const isExpanded = !store.isExpanded();
      patchState(store, { 
        isExpanded,
        hasNewMessage: isExpanded ? false : store.hasNewMessage()
      });
      
      // Save state to localStorage if persistence is enabled
      if (defaultConfig.enablePersistence) {
        localStorage.setItem('ai-chat-expanded', JSON.stringify(isExpanded));
      }
    },

    /**
     * Expand the chat widget
     */
    expand: () => {
      patchState(store, { 
        isExpanded: true,
        hasNewMessage: false
      });
      
      if (defaultConfig.enablePersistence) {
        localStorage.setItem('ai-chat-expanded', 'true');
      }
    },

    /**
     * Collapse the chat widget
     */
    collapse: () => {
      patchState(store, { isExpanded: false });
      
      if (defaultConfig.enablePersistence) {
        localStorage.setItem('ai-chat-expanded', 'false');
      }
    },

    /**
     * Add a message to the chat
     */
    addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
      const newMessage: ChatMessage = {
        ...message,
        id: aiChatService.generateMessageId(),
        timestamp: new Date()
      };

      const currentMessages = store.messages();
      let updatedMessages = [...currentMessages, newMessage];

      // Limit messages if maxMessages is set
      if (updatedMessages.length > defaultConfig.maxMessages) {
        updatedMessages = updatedMessages.slice(-defaultConfig.maxMessages);
      }

      patchState(store, { 
        messages: updatedMessages,
        hasNewMessage: !store.isExpanded() && message.sender === 'ai'
      });

      // Save to localStorage if persistence is enabled
      if (defaultConfig.enablePersistence) {
        localStorage.setItem('ai-chat-messages', JSON.stringify(updatedMessages));
      }
    },

    /**
     * Update a specific message
     */
    updateMessage: (messageId: string, updates: Partial<ChatMessage>) => {
      const currentMessages = store.messages();
      const updatedMessages = currentMessages.map(msg => 
        msg.id === messageId ? { ...msg, ...updates } : msg
      );

      patchState(store, { messages: updatedMessages });

      if (defaultConfig.enablePersistence) {
        localStorage.setItem('ai-chat-messages', JSON.stringify(updatedMessages));
      }
    },

    /**
     * Clear all messages
     */
    clearMessages: () => {
      patchState(store, { 
        messages: [],
        error: null,
        conversationId: undefined
      });

      if (defaultConfig.enablePersistence) {
        localStorage.removeItem('ai-chat-messages');
        localStorage.removeItem('ai-chat-conversation-id');
      }
    },

    /**
     * Set error state
     */
    setError: (error: string | null) => {
      patchState(store, { error, isLoading: false });
    },

    /**
     * Clear error state
     */
    clearError: () => {
      patchState(store, { error: null });
    },

    /**
     * Send message to AI assistant
     */
    sendMessage: rxMethod<string>(
      pipe(
        tap((question) => {
          // Validate message
          const validation = aiChatService.validateMessage(question);
          if (!validation.isValid) {
            patchState(store, { error: validation.error || 'Invalid message' });
            return;
          }

          // Clear any existing errors
          patchState(store, { error: null, isLoading: true });

          // Add user message
          const userMessage: ChatMessage = {
            id: aiChatService.generateMessageId(),
            content: question,
            sender: 'user',
            timestamp: new Date()
          };

          const currentMessages = store.messages();
          let updatedMessages = [...currentMessages, userMessage];

          // Add loading AI message
          const loadingMessage: ChatMessage = {
            id: aiChatService.generateMessageId(),
            content: '',
            sender: 'ai',
            timestamp: new Date(),
            isLoading: true
          };

          updatedMessages = [...updatedMessages, loadingMessage];

          // Limit messages
          if (updatedMessages.length > defaultConfig.maxMessages) {
            updatedMessages = updatedMessages.slice(-defaultConfig.maxMessages);
          }

          patchState(store, { messages: updatedMessages });
        }),
        switchMap((question) => 
          aiChatService.sendMessage(question).pipe(
            tap((response) => {
              // Remove loading message and add AI response
              const currentMessages = store.messages();
              const messagesWithoutLoading = currentMessages.filter(msg => !msg.isLoading);
              
              const aiMessage: ChatMessage = {
                id: aiChatService.generateMessageId(),
                content: response.answer,
                sender: 'ai',
                timestamp: new Date()
              };

              let updatedMessages = [...messagesWithoutLoading, aiMessage];

              // Limit messages
              if (updatedMessages.length > defaultConfig.maxMessages) {
                updatedMessages = updatedMessages.slice(-defaultConfig.maxMessages);
              }

              patchState(store, { 
                messages: updatedMessages,
                conversationId: response.conversation_id,
                hasNewMessage: !store.isExpanded()
              });

              // Save to localStorage
              if (defaultConfig.enablePersistence) {
                localStorage.setItem('ai-chat-messages', JSON.stringify(updatedMessages));
                if (response.conversation_id) {
                  localStorage.setItem('ai-chat-conversation-id', response.conversation_id);
                }
              }
            }),
            catchError((error) => {
              // Remove loading message and show error
              const currentMessages = store.messages();
              const messagesWithoutLoading = currentMessages.filter(msg => !msg.isLoading);
              
              const errorMessage: ChatMessage = {
                id: aiChatService.generateMessageId(),
                content: error.message || 'Sorry, I encountered an error. Please try again.',
                sender: 'ai',
                timestamp: new Date(),
                error: true
              };

              const updatedMessages = [...messagesWithoutLoading, errorMessage];

              patchState(store, { 
                messages: updatedMessages,
                error: error.message
              });

              return of(null);
            }),
            finalize(() => {
              patchState(store, { isLoading: false });
            })
          )
        )
      )
    ),

    /**
     * Load persisted state from localStorage
     */
    loadPersistedState: () => {
      if (!defaultConfig.enablePersistence) return;

      try {
        // Load messages
        const savedMessages = localStorage.getItem('ai-chat-messages');
        if (savedMessages) {
          const messages = JSON.parse(savedMessages).map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }));
          patchState(store, { messages });
        }

        // Load expanded state
        const savedExpanded = localStorage.getItem('ai-chat-expanded');
        if (savedExpanded) {
          const isExpanded = JSON.parse(savedExpanded);
          patchState(store, { isExpanded });
        }

        // Load conversation ID
        const savedConversationId = localStorage.getItem('ai-chat-conversation-id');
        if (savedConversationId) {
          patchState(store, { conversationId: savedConversationId });
        }
      } catch (error) {
        console.error('Error loading persisted chat state:', error);
      }
    }
  })),
  withHooks({
    onInit(store) {
      // Load persisted state on initialization
      store.loadPersistedState();
    }
  })
) {}
