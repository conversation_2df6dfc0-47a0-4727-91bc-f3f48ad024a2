import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-landing',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="landing-container">
      <!-- Header Navigation -->
      <header class="landing-header">
        <div class="header-logo">
          <a href="#" class="logo-link">
            <span class="header-logo-text">HR<span class="highlight">Shell</span></span>
          </a>
        </div>
        <nav class="header-nav">
          <a href="#features" class="nav-link">Features</a>
          <a href="#" class="nav-link">Pricing</a>
          <a href="#" class="nav-link">About</a>
          <button class="btn-login" (click)="navigateToLogin()">Login</button>
          <button class="btn-signup" (click)="navigateToSignup()">Sign Up</button>
        </nav>
      </header>

      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <div class="logo-container">
            <h1 class="hero-logo-text">HR<span class="highlight">Shell</span></h1>
            <span class="tagline">Complete HR Management Solution</span>
          </div>
          <h2 class="hero-title">Transform Your HR Operations</h2>
          <p class="hero-description">
            Streamline your HR processes, manage employee data, and boost productivity with our all-in-one HR management platform.
          </p>
          <div class="hero-buttons">
            <button class="btn-primary" (click)="navigateToDashboard()">Get Started</button>
            <button class="btn-secondary" (click)="scrollToFeatures()">Learn More</button>
          </div>
        </div>
        <div class="hero-image">
          <div class="dashboard-preview">
            <div class="preview-header">
              <div class="preview-dots">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
              </div>
              <div class="preview-title">HR Shell Dashboard</div>
            </div>
            <div class="preview-content">
              <div class="preview-sidebar">
                <div class="sidebar-item active"></div>
                <div class="sidebar-item"></div>
                <div class="sidebar-item"></div>
                <div class="sidebar-item"></div>
                <div class="sidebar-item"></div>
              </div>
              <div class="preview-main">
                <div class="preview-stats">
                  <div class="stat-card"></div>
                  <div class="stat-card"></div>
                  <div class="stat-card"></div>
                  <div class="stat-card"></div>
                </div>
                <div class="preview-chart"></div>
                <div class="preview-table">
                  <div class="table-row"></div>
                  <div class="table-row"></div>
                  <div class="table-row"></div>
                  <div class="table-row"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section class="features-section" id="features">
        <h2 class="section-title">Key Features</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fa fa-users"></i>
            </div>
            <h3 class="feature-title">Employee Management</h3>
            <p class="feature-description">
              Easily manage employee information, documents, and performance records in one centralized location.
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fa fa-calendar-check"></i>
            </div>
            <h3 class="feature-title">Attendance Tracking</h3>
            <p class="feature-description">
              Track employee attendance, manage time-off requests, and monitor work hours with ease.
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fa fa-chart-line"></i>
            </div>
            <h3 class="feature-title">Performance Analytics</h3>
            <p class="feature-description">
              Gain insights into employee performance with comprehensive analytics and reporting tools.
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fa fa-file-invoice"></i>
            </div>
            <h3 class="feature-title">Payroll Management</h3>
            <p class="feature-description">
              Streamline payroll processing, tax calculations, and salary disbursements with automated tools.
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fa fa-laptop-code"></i>
            </div>
            <h3 class="feature-title">Recruitment Portal</h3>
            <p class="feature-description">
              Simplify your hiring process with job posting, applicant tracking, and interview scheduling.
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fa fa-graduation-cap"></i>
            </div>
            <h3 class="feature-title">Training & Development</h3>
            <p class="feature-description">
              Manage employee training programs, track skill development, and plan career growth paths.
            </p>
          </div>
        </div>
      </section>

      <!-- Testimonials Section -->
      <section class="testimonials-section">
        <h2 class="section-title">What Our Clients Say</h2>
        <div class="testimonials-container">
          <div class="testimonial-card">
            <div class="testimonial-content">
              <p>"HR Shell has transformed how we manage our workforce. The intuitive interface and comprehensive features have saved us countless hours."</p>
            </div>
            <div class="testimonial-author">
              <div class="author-avatar">JD</div>
              <div class="author-info">
                <h4>Jane Doe</h4>
                <p>HR Director, Tech Solutions Inc.</p>
              </div>
            </div>
          </div>
          <div class="testimonial-card">
            <div class="testimonial-content">
              <p>"The employee dashboard feature has been a game-changer for our team. Our staff can now access all their HR information in one place."</p>
            </div>
            <div class="testimonial-author">
              <div class="author-avatar">JS</div>
              <div class="author-info">
                <h4>John Smith</h4>
                <p>CEO, Global Enterprises</p>
              </div>
            </div>
          </div>
          <div class="testimonial-card">
            <div class="testimonial-content">
              <p>"The analytics and reporting capabilities have given us valuable insights into our workforce that we never had before."</p>
            </div>
            <div class="testimonial-author">
              <div class="author-avatar">AR</div>
              <div class="author-info">
                <h4>Amanda Rodriguez</h4>
                <p>Operations Manager, Innovate Corp</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="cta-section">
        <div class="cta-content">
          <h2 class="cta-title">Ready to Transform Your HR Operations?</h2>
          <p class="cta-description">
            Join thousands of companies that have streamlined their HR processes with HR Shell.
          </p>
          <button class="btn-primary" (click)="navigateToDashboard()">Get Started Today</button>
        </div>
      </section>

      <!-- Footer -->
      <footer class="landing-footer">
        <div class="footer-content">
          <div class="footer-logo">
            <h3 class="footer-logo-text">HR<span class="highlight">Shell</span></h3>
            <p>Complete HR Management Solution</p>
          </div>
          <div class="footer-links">
            <div class="link-group">
              <h4>Product</h4>
              <ul>
                <li><a href="#">Features</a></li>
                <li><a href="#">Pricing</a></li>
                <li><a href="#">Integrations</a></li>
                <li><a href="#">Updates</a></li>
              </ul>
            </div>
            <div class="link-group">
              <h4>Company</h4>
              <ul>
                <li><a href="#">About Us</a></li>
                <li><a href="#">Careers</a></li>
                <li><a href="#">Contact Us</a></li>
                <li><a href="#">Blog</a></li>
              </ul>
            </div>
            <div class="link-group">
              <h4>Resources</h4>
              <ul>
                <li><a href="#">Documentation</a></li>
                <li><a href="#">Support</a></li>
                <li><a href="#">Community</a></li>
                <li><a href="#">Webinars</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 HR Shell. All rights reserved.</p>
          <div class="footer-social">
            <a href="#"><i class="fa fa-facebook"></i></a>
            <a href="#"><i class="fa fa-twitter"></i></a>
            <a href="#"><i class="fa fa-linkedin"></i></a>
            <a href="#"><i class="fa fa-instagram"></i></a>
          </div>
        </div>
      </footer>
    </div>
  `,
  styles: [`
    .landing-container {
      font-family: 'Poppins', sans-serif;
      color: #343a40;
      overflow-x: hidden;
    }

    /* Header Navigation */
    .landing-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem 5%;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 100;
      background-color: rgba(255, 255, 255, 0.95);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .header-logo {
      display: flex;
      align-items: center;
    }

    .logo-link {
      display: block;
      text-decoration: none;
    }

    .header-logo-text {
      font-size: 1.8rem;
      font-weight: 700;
      color: #343a40;
      transition: transform 0.3s ease;
      display: inline-block;
    }

    .header-logo-text .highlight {
      color: #ff6b35;
    }

    .logo-link:hover .header-logo-text {
      transform: scale(1.05);
    }

    .header-nav {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    .nav-link {
      color: #343a40;
      text-decoration: none;
      font-weight: 500;
      font-size: 1rem;
      transition: color 0.3s;
    }

    .nav-link:hover {
      color: #ff6b35;
    }

    .btn-login {
      padding: 0.6rem 1.5rem;
      background-color: transparent;
      color: #ff6b35;
      border: 2px solid #ff6b35;
      border-radius: 4px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s;
    }

    .btn-login:hover {
      background-color: #ff6b35;
      color: white;
    }

    .btn-signup {
      padding: 0.6rem 1.5rem;
      background-color: #ff6b35;
      color: white;
      border: 2px solid #ff6b35;
      border-radius: 4px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s;
    }

    .btn-signup:hover {
      background-color: #e85a2a;
      border-color: #e85a2a;
    }

    /* Hero Section */
    .hero-section {
      display: flex;
      min-height: 100vh;
      padding: 5rem 5% 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .hero-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-right: 5%;
    }

    .logo-container {
      margin-bottom: 2rem;
    }

    .hero-logo-text {
      font-size: 3rem;
      font-weight: 700;
      color: #343a40;
      margin: 0 0 1rem 0;
    }

    .hero-logo-text .highlight {
      color: #ff6b35;
    }

    .tagline {
      font-size: 1.2rem;
      color: #6c757d;
    }

    .hero-title {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      line-height: 1.2;
    }

    .hero-description {
      font-size: 1.2rem;
      line-height: 1.6;
      color: #6c757d;
      margin-bottom: 2.5rem;
    }

    .hero-buttons {
      display: flex;
      gap: 1rem;
    }

    .btn-primary {
      padding: 0.8rem 2rem;
      background-color: #ff6b35;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .btn-primary:hover {
      background-color: #e85a2a;
    }

    .btn-secondary {
      padding: 0.8rem 2rem;
      background-color: transparent;
      color: #ff6b35;
      border: 2px solid #ff6b35;
      border-radius: 4px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s;
    }

    .btn-secondary:hover {
      background-color: rgba(255, 107, 53, 0.1);
    }

    .hero-image {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .dashboard-preview {
      width: 100%;
      max-width: 600px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .preview-header {
      display: flex;
      align-items: center;
      padding: 1rem;
      background-color: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
    }

    .preview-dots {
      display: flex;
      gap: 0.5rem;
      margin-right: 1rem;
    }

    .dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #dee2e6;
    }

    .preview-title {
      font-weight: 600;
      color: #495057;
    }

    .preview-content {
      display: flex;
      height: 400px;
    }

    .preview-sidebar {
      width: 60px;
      background-color: #343a40;
      padding: 1rem 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }

    .sidebar-item {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      background-color: #495057;
    }

    .sidebar-item.active {
      background-color: #ff6b35;
    }

    .preview-main {
      flex: 1;
      padding: 1.5rem;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .preview-stats {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 1rem;
    }

    .stat-card {
      height: 80px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }

    .preview-chart {
      height: 120px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }

    .preview-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .table-row {
      height: 30px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }

    /* Features Section */
    .features-section {
      padding: 5rem 5%;
      background-color: white;
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 3rem;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .feature-card {
      padding: 2rem;
      background-color: #f8f9fa;
      border-radius: 8px;
      transition: transform 0.3s, box-shadow 0.3s;
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    }

    .feature-icon {
      font-size: 2.5rem;
      color: #ff6b35;
      margin-bottom: 1.5rem;
    }

    .feature-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .feature-description {
      color: #6c757d;
      line-height: 1.6;
    }

    /* Testimonials Section */
    .testimonials-section {
      padding: 5rem 5%;
      background-color: #f8f9fa;
    }

    .testimonials-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .testimonial-card {
      background-color: white;
      border-radius: 8px;
      padding: 2rem;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .testimonial-content {
      font-style: italic;
      color: #495057;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .testimonial-author {
      display: flex;
      align-items: center;
    }

    .author-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: #ff6b35;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      margin-right: 1rem;
    }

    .author-info h4 {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .author-info p {
      margin: 0;
      font-size: 0.9rem;
      color: #6c757d;
    }

    /* CTA Section */
    .cta-section {
      padding: 5rem 5%;
      background-color: #343a40;
      color: white;
      text-align: center;
    }

    .cta-content {
      max-width: 800px;
      margin: 0 auto;
    }

    .cta-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
    }

    .cta-description {
      font-size: 1.2rem;
      color: #adb5bd;
      margin-bottom: 2.5rem;
    }

    /* Footer */
    .landing-footer {
      background-color: #212529;
      color: #adb5bd;
      padding: 4rem 5% 2rem;
    }

    .footer-content {
      display: flex;
      flex-wrap: wrap;
      gap: 3rem;
      margin-bottom: 3rem;
    }

    .footer-logo {
      flex: 1;
      min-width: 250px;
    }

    .footer-logo-text {
      font-size: 1.8rem;
      font-weight: 700;
      color: white;
      margin: 0 0 1rem 0;
    }

    .footer-logo-text .highlight {
      color: #ff6b35;
    }

    .footer-logo p {
      margin: 0;
    }

    .footer-links {
      flex: 2;
      display: flex;
      flex-wrap: wrap;
      gap: 2rem;
    }

    .link-group {
      flex: 1;
      min-width: 150px;
    }

    .link-group h4 {
      color: white;
      margin: 0 0 1rem 0;
    }

    .link-group ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .link-group li {
      margin-bottom: 0.5rem;
    }

    .link-group a {
      color: #adb5bd;
      text-decoration: none;
      transition: color 0.3s;
    }

    .link-group a:hover {
      color: white;
    }

    .footer-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 2rem;
      border-top: 1px solid #495057;
    }

    .footer-social {
      display: flex;
      gap: 1rem;
    }

    .footer-social a {
      color: #adb5bd;
      font-size: 1.2rem;
      transition: color 0.3s;
    }

    .footer-social a:hover {
      color: white;
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
      .hero-section {
        flex-direction: column;
        padding-top: 2rem;
        padding-bottom: 2rem;
      }

      .hero-content {
        padding-right: 0;
        margin-bottom: 3rem;
      }

      .hero-title {
        font-size: 2.5rem;
      }
    }

    @media (max-width: 768px) {
      .section-title {
        font-size: 2rem;
      }

      .cta-title {
        font-size: 2rem;
      }

      .footer-content {
        flex-direction: column;
        gap: 2rem;
      }
    }
  `]
})
export class LandingComponent {
  constructor(private router: Router) {}

  navigateToLogin(): void {
    this.router.navigate(['/login']);
  }

  navigateToSignup(): void {
    this.router.navigate(['/register']);
  }

  navigateToDashboard(): void {
    // Navigate to login page instead of directly to dashboard
    this.router.navigate(['/login']);
  }

  scrollToFeatures(): void {
    const featuresSection = document.getElementById('features');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    }
  }
}
