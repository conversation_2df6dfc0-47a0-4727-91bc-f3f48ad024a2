<app-modal
  id="edit-department-modal"
  title="Edit Department"
  size="medium"
  [showFooter]="true">

  <form [formGroup]="departmentForm" (ngSubmit)="onSubmit()" class="department-form">
    <div class="department-form__group">
      <label for="name" class="department-form__label">
        Department Name <span class="department-form__required">*</span>
      </label>
      <input
        type="text"
        id="name"
        formControlName="name"
        class="department-form__input"
        [class.department-form__input--invalid]="isFieldInvalid('name')"
        placeholder="Enter department name"
      >
      <div class="department-form__error" *ngIf="isFieldInvalid('name')">
        <span *ngIf="departmentForm.get('name')?.errors?.['required']">
          Department name is required
        </span>
        <span *ngIf="departmentForm.get('name')?.errors?.['maxlength']">
          Department name cannot exceed 100 characters
        </span>
      </div>
    </div>

    <div class="department-form__group">
      <label for="description" class="department-form__label">
        Description
      </label>
      <textarea
        id="description"
        formControlName="description"
        class="department-form__textarea"
        [class.department-form__input--invalid]="isFieldInvalid('description')"
        placeholder="Enter department description"
        rows="4"
      ></textarea>
      <div class="department-form__error" *ngIf="isFieldInvalid('description')">
        <span *ngIf="departmentForm.get('description')?.errors?.['maxlength']">
          Description cannot exceed 500 characters
        </span>
      </div>
    </div>

    <div class="department-form__group">
      <label for="manager" class="department-form__label">
        Manager
      </label>
      <app-searchable-dropdown
        id="manager"
        formControlName="manager"
        [options]="managerOptions"
        [disabled]="isLoadingEmployees"
        [placeholder]="isLoadingEmployees ? 'Loading managers...' : 'Select a manager'"
        searchPlaceholder="Search managers..."
        [invalid]="isFieldInvalid('manager')">
      </app-searchable-dropdown>
      <div class="department-form__error" *ngIf="employeeLoadError">
        <span>{{ employeeLoadError }}</span>
      </div>
    </div>

    <div class="department-form__group">
      <label class="department-form__label">
        Status <span class="department-form__required">*</span>
      </label>
      <div class="department-form__radio-group">
        <div class="department-form__radio-option" *ngFor="let option of statusOptions">
          <input
            type="radio"
            [id]="'status-' + option.value"
            [value]="option.value"
            formControlName="status"
            class="department-form__radio-input">
          <label [for]="'status-' + option.value" class="department-form__radio-label">
            <app-icon [name]="option.icon || 'fa fa-circle'" size="sm" class="department-form__radio-icon"></app-icon>
            <div class="department-form__radio-content">
              <span class="department-form__radio-title">{{ option.label }}</span>
              <span class="department-form__radio-subtitle" *ngIf="option.subtitle">{{ option.subtitle }}</span>
            </div>
          </label>
        </div>
      </div>
      <div class="department-form__error" *ngIf="isFieldInvalid('status')">
        <span *ngIf="departmentForm.get('status')?.errors?.['required']">
          Status is required
        </span>
      </div>
    </div>
  </form>

  <div modal-footer class="department-form__footer">
    <app-button
      class="department-form__button"
      label="Cancel"
      variant="secondary"
      (onClick)="closeModal()">
    </app-button>

    <app-button
      class="department-form__button"
      label="Save Changes"
      variant="primary"
      [disabled]="departmentForm.invalid || isSubmitting"
      [icon]="isSubmitting ? 'fa fa-spinner fa-spin' : ''"
      (onClick)="onSubmit()">
    </app-button>
  </div>
</app-modal>
