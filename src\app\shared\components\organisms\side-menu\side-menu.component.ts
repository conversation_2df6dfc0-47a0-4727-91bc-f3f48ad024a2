import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MenuGroupComponent, MenuItem } from '../../molecules/menu-group/menu-group.component';
import { MenuItemComponent } from '../../atoms/menu-item/menu-item.component';
import { AuthStore } from '../../../../core/state';

export interface MenuGroup {
  title: string;
  items: MenuItem[];
  expanded?: boolean;
}

export interface SubmenuData {
  parentLabel: string;
  items: MenuItem[];
  expanded: boolean;
}

@Component({
  selector: 'app-side-menu',
  standalone: true,
  imports: [CommonModule, RouterModule, MenuGroupComponent, MenuItemComponent],
  templateUrl: './side-menu.component.html',
  styleUrl: './side-menu.component.scss'
})
export class SideMenuComponent {
  @Input() title: string = 'SmartHR';
  @Input() logo: string = '';
  @Input() menuGroups: MenuGroup[] = [];
  @Input() collapsed: boolean = false;

  private authStore = inject(AuthStore);

  // Access state from the store using signals
  user = this.authStore.user;
  isAuthenticated = this.authStore.isAuthenticated;
  fullName = this.authStore.fullName;
  isAdmin = this.authStore.isAdmin;

  // Submenu data
  dashboardSubmenu: MenuItem[] = [
    {
      label: 'Admin Dashboard',
      route: '/app/admin-dashboard'
    },
    {
      label: 'Employee Dashboard',
      route: '/app/employee-dashboard'
    },
    {
      label: 'Deals Dashboard',
      route: '/app/deals-dashboard'
    },
    {
      label: 'Leads Dashboard',
      route: '/app/leads-dashboard',
      active: true
    }
  ];

  applicationsSubmenu: MenuItem[] = [
    {
      label: 'Mail',
      route: '/app/mail'
    },
    {
      label: 'Documents',
      route: '/app/documents'
    }
  ];

  adminSubmenu: MenuItem[] = [
    {
      label: 'Settings',
      route: '/app/settings'
    },
    {
      label: 'Logs',
      route: '/app/logs'
    },
    {
      label: 'Server',
      route: '/app/server'
    },
    {
      label: 'Maintenance Mode',
      route: '/app/admin/maintenance'
    }
  ];

  // Track expanded state for each submenu
  expandedSubmenus: { [key: string]: boolean } = {
    'Dashboard': true,
    'Applications': false,
    'Super Admin': false
  };

  // Toggle submenu expansion
  toggleSubmenu(label: string): void {
    this.expandedSubmenus[label] = !this.expandedSubmenus[label];
  }

  // Get submenu items based on parent label
  getSubmenuItems(label: string): MenuItem[] {
    switch(label) {
      case 'Dashboard':
        return this.dashboardSubmenu;
      case 'Applications':
        return this.applicationsSubmenu;
      case 'Super Admin':
        return this.adminSubmenu;
      default:
        return [];
    }
  }

  // Get menu groups based on user role
  get employeeMenuGroups(): MenuGroup[] {
    const mainMenuItems: MenuItem[] = [
      {
        icon: 'fa fa-house',
        label: 'Dashboard',
        route: '/app/dashboard',
        badge: 'Hot',
        badgeType: 'danger',
        active: true,
        hasSubmenu: true
      },
      {
        icon: 'fa fa-th-large',
        label: 'Applications',
        route: '/app/applications',
        hasSubmenu: true
      }
    ];

    // Add admin menu item if user is admin
    if (this.isAdmin()) {
      mainMenuItems.push({
        icon: 'fa fa-user-shield',
        label: 'Super Admin',
        route: '/app/admin',
        hasSubmenu: true
      });
    }

    const menuGroups: MenuGroup[] = [
      {
        title: 'MAIN MENU',
        expanded: true,
        items: mainMenuItems
      },
      {
        title: 'LAYOUT',
        expanded: true,
        items: [
          {
            icon: 'fa fa-building',
            label: 'Companies',
            route: '/app/companies'
          },
          {
            icon: 'fa fa-users',
            label: 'Employees',
            route: '/app/employees'
          },
          {
            icon: 'fa fa-sitemap',
            label: 'Departments',
            route: '/app/departments'
          }
        ]
      },
      {
        title: 'USER',
        expanded: true,
        items: [
          {
            icon: 'fa fa-user',
            label: 'Profile',
            route: '/app/profile'
          },
          {
            icon: 'fa fa-cog',
            label: 'Settings',
            route: '/app/settings'
          }
        ]
      }
    ];

    return menuGroups;
  }


}
