import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  AbstractControl,
  ValidationErrors,
  ValidatorFn
} from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../core/services/auth.service';
import { RegistrationRequest } from '../../core/models/user.interface';
import { AuthStore } from '../../core/state';

@Component({
  selector: 'app-registration',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './registration.component.html',
  styleUrls: ['./registration.component.scss']
})
export class RegistrationComponent implements OnInit {
  registrationForm!: FormGroup;
  showPassword: boolean = false;
  showConfirmPassword: boolean = false;

  // Inject services using the new inject function
  private router = inject(Router);
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private authStore = inject(AuthStore);

  // Access state from the store
  isLoading = this.authStore.isLoading;
  error = this.authStore.error;

  ngOnInit(): void {
    this.initForm();
  }

  private initForm(): void {
    this.registrationForm = this.fb.nonNullable.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      companyName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]],
      termsAgreed: [false, [Validators.requiredTrue]]
    });

    // Add the password match validator
    this.registrationForm.addValidators(this.passwordMatchValidator());
  }

  private passwordMatchValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const form = control as FormGroup;
      const password = form.get('password')?.value;
      const confirmPassword = form.get('confirmPassword')?.value;

      // Only validate if both fields have values
      if (password && confirmPassword && password !== confirmPassword) {
        // We need to use setErrors this way to avoid overriding other validators
        const confirmPasswordControl = form.get('confirmPassword');
        if (confirmPasswordControl) {
          const currentErrors = confirmPasswordControl.errors || {};
          confirmPasswordControl.setErrors({
            ...currentErrors,
            passwordMismatch: true
          });
        }
        return { passwordMismatch: true };
      }

      // If passwords match, remove the passwordMismatch error but keep other errors
      const confirmPasswordControl = form.get('confirmPassword');
      if (confirmPasswordControl && confirmPasswordControl.errors) {
        const { passwordMismatch, ...otherErrors } = confirmPasswordControl.errors;
        confirmPasswordControl.setErrors(
          Object.keys(otherErrors).length > 0 ? otherErrors : null
        );
      }

      return null;
    };
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPasswordVisibility(): void {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  register(): void {
    if (this.registrationForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.registrationForm.controls).forEach(key => {
        const control = this.registrationForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    // Clear any previous errors
    this.authStore.clearError();

    // Extract form values and prepare registration request
    const formValues = this.registrationForm.value;

    // Generate a username from the email if not provided
    const username = formValues.email?.split('@')[0] || '';

    const registrationData: RegistrationRequest = {
      username: username,
      email: formValues.email || '',
      password: formValues.password || '',
      password2: formValues.confirmPassword || '',
      first_name: formValues.firstName || '',
      last_name: formValues.lastName || '',
      company: formValues.companyName || ''
    };

    // Use the register method from the auth store
    this.authStore.register(registrationData);
  }

  clearError(): void {
    this.authStore.clearError();
  }

  // Helper methods for form validation
  get f() {
    return this.registrationForm.controls;
  }

  hasError(controlName: string, errorName: string): boolean {
    const control = this.registrationForm.get(controlName);
    return (control?.touched || control?.dirty) && control?.hasError(errorName) || false;
  }

  hasPasswordMatchError(): boolean {
    const confirmPasswordControl = this.registrationForm.get('confirmPassword');
    return (confirmPasswordControl?.touched || confirmPasswordControl?.dirty) &&
           this.registrationForm.hasError('passwordMismatch') || false;
  }

  registerWithGoogle(): void {
    // Implement Google registration
    console.log('Register with Google');
    // For now, just navigate to the dashboard
    this.router.navigate(['/app/dashboard']);
  }

  registerWithMicrosoft(): void {
    // Implement Microsoft registration
    console.log('Register with Microsoft');
    // For now, just navigate to the dashboard
    this.router.navigate(['/app/dashboard']);
  }

  registerWithApple(): void {
    // Implement Apple registration
    console.log('Register with Apple');
    // For now, just navigate to the dashboard
    this.router.navigate(['/app/dashboard']);
  }

  navigateToLogin(event: Event): void {
    event.preventDefault();
    this.router.navigate(['/login']);
  }
}
