<div class="searchable-dropdown" [ngClass]="{'is-open': isOpen, 'is-disabled': disabled, 'is-invalid': invalid}">
  <button
    type="button"
    class="searchable-dropdown__toggle"
    [disabled]="disabled"
    (click)="toggleDropdown($event)">
    <span class="searchable-dropdown__selected-text">{{ selectedLabel }}</span>
    <app-icon
      name="fa fa-chevron-down"
      size="sm"
      class="searchable-dropdown__toggle-icon">
    </app-icon>
  </button>

  <div class="searchable-dropdown__menu" *ngIf="isOpen">
    <div class="searchable-dropdown__search" *ngIf="showSearch">
      <input
        type="text"
        class="searchable-dropdown__search-input"
        [placeholder]="searchPlaceholder"
        [formControl]="searchControl"
        (click)="$event.stopPropagation()">
      <app-icon
        name="fa fa-search"
        size="sm"
        class="searchable-dropdown__search-icon">
      </app-icon>
    </div>

    <div class="searchable-dropdown__options">
      <div
        *ngFor="let option of filteredOptions"
        class="searchable-dropdown__option"
        [ngClass]="{
          'is-active': option.value === selectedValue,
          'is-disabled': option.disabled
        }"
        (click)="selectOption(option)">

        <!-- Avatar for employees -->
        <div *ngIf="option.avatar" class="searchable-dropdown__option-avatar">
          <img [src]="option.avatar" [alt]="option.label" class="avatar-image">
        </div>

        <!-- Icon fallback -->
        <app-icon
          *ngIf="option.icon && !option.avatar"
          [name]="option.icon"
          size="sm"
          class="searchable-dropdown__option-icon">
        </app-icon>

        <!-- Option content -->
        <div class="searchable-dropdown__option-content">
          <span class="searchable-dropdown__option-label">{{ option.label }}</span>
          <span *ngIf="option.subtitle" class="searchable-dropdown__option-subtitle">{{ option.subtitle }}</span>
        </div>
      </div>

      <div *ngIf="filteredOptions.length === 0" class="searchable-dropdown__no-results">
        No results found
      </div>
    </div>
  </div>
</div>
