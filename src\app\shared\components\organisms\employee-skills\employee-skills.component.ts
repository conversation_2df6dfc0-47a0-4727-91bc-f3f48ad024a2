import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-skills',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="skills-container">
      <div class="skills-header">
        <h3 class="section-title">My Skills</h3>
        <div class="header-actions">
          <button class="action-btn">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      
      <div class="skills-content">
        <div class="skill-item">
          <div class="skill-info">
            <div class="skill-name">Figma</div>
            <div class="skill-level">
              <span class="level-indicator advanced">Advanced</span>
              <span class="level-date">Updated: 14 May 2023</span>
            </div>
          </div>
          <div class="skill-progress">
            <div class="progress-bar">
              <div class="progress-fill" style="width: 90%;"></div>
            </div>
            <div class="progress-value">90%</div>
          </div>
        </div>
        
        <div class="skill-item">
          <div class="skill-info">
            <div class="skill-name">HTML</div>
            <div class="skill-level">
              <span class="level-indicator advanced">Advanced</span>
              <span class="level-date">Updated: 14 May 2023</span>
            </div>
          </div>
          <div class="skill-progress">
            <div class="progress-bar">
              <div class="progress-fill" style="width: 85%;"></div>
            </div>
            <div class="progress-value">85%</div>
          </div>
        </div>
        
        <div class="skill-item">
          <div class="skill-info">
            <div class="skill-name">CSS</div>
            <div class="skill-level">
              <span class="level-indicator intermediate">Intermediate</span>
              <span class="level-date">Updated: 12 May 2023</span>
            </div>
          </div>
          <div class="skill-progress">
            <div class="progress-bar">
              <div class="progress-fill" style="width: 75%;"></div>
            </div>
            <div class="progress-value">75%</div>
          </div>
        </div>
        
        <div class="skill-item">
          <div class="skill-info">
            <div class="skill-name">JavaScript</div>
            <div class="skill-level">
              <span class="level-indicator beginner">Beginner</span>
              <span class="level-date">Updated: 10 May 2023</span>
            </div>
          </div>
          <div class="skill-progress">
            <div class="progress-bar">
              <div class="progress-fill" style="width: 60%;"></div>
            </div>
            <div class="progress-value">60%</div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .skills-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .skills-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e9ecef;
    }
    
    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }
    
    .action-btn {
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      font-size: 16px;
    }
    
    .skills-content {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .skill-item {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .skill-info {
      display: flex;
      flex-direction: column;
    }
    
    .skill-name {
      font-size: 16px;
      font-weight: 500;
      color: #343a40;
      margin-bottom: 4px;
    }
    
    .skill-level {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .level-indicator {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 12px;
    }
    
    .level-indicator.advanced {
      background-color: #e8f5e9;
      color: #4caf50;
    }
    
    .level-indicator.intermediate {
      background-color: #fff8e1;
      color: #ffc107;
    }
    
    .level-indicator.beginner {
      background-color: #ffebee;
      color: #f44336;
    }
    
    .level-date {
      font-size: 12px;
      color: #adb5bd;
    }
    
    .skill-progress {
      display: flex;
      align-items: center;
      gap: 12px;
    }
    
    .progress-bar {
      flex: 1;
      height: 6px;
      background-color: #e9ecef;
      border-radius: 3px;
      overflow: hidden;
    }
    
    .progress-fill {
      height: 100%;
      background-color: #28a745;
    }
    
    .progress-value {
      font-size: 14px;
      font-weight: 600;
      color: #343a40;
      min-width: 40px;
      text-align: right;
    }
  `]
})
export class EmployeeSkillsComponent {
  @Input() employee: any;
}
