import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-attendance',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="attendance-container">
      <div class="attendance-header">
        <h3 class="section-title">Attendance</h3>
        <div class="date-display">
          <span>08:35 AM, 11 Mar 2023</span>
        </div>
      </div>
      
      <div class="attendance-content">
        <div class="attendance-stats">
          <div class="stat-item">
            <div class="stat-value">8.36 <span class="stat-unit">/ 9</span></div>
            <div class="stat-label">Working Hours</div>
            <div class="stat-indicator">
              <span class="indicator-dot green"></span>
              <span class="indicator-text">On Time</span>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-value">10 <span class="stat-unit">/ 40</span></div>
            <div class="stat-label">Total Breaks</div>
            <div class="stat-indicator">
              <span class="indicator-dot green"></span>
              <span class="indicator-text">On Limit</span>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-value">75 <span class="stat-unit">/ 88</span></div>
            <div class="stat-label">Total Hours</div>
            <div class="stat-indicator">
              <span class="indicator-dot red"></span>
              <span class="indicator-text">On Target</span>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-value">16 <span class="stat-unit">/ 28</span></div>
            <div class="stat-label">Overtime Hrs</div>
            <div class="stat-indicator">
              <span class="indicator-dot red"></span>
              <span class="indicator-text">On Limit</span>
            </div>
          </div>
        </div>
        
        <div class="time-tracking">
          <div class="tracking-item">
            <div class="tracking-label">Total Working</div>
            <div class="tracking-value">12h 36m</div>
          </div>
          
          <div class="tracking-item">
            <div class="tracking-label">Production Hours</div>
            <div class="tracking-value">08h 36m</div>
          </div>
          
          <div class="tracking-item">
            <div class="tracking-label">Break Hours</div>
            <div class="tracking-value">22m 15s</div>
          </div>
          
          <div class="tracking-item">
            <div class="tracking-label">Overtime</div>
            <div class="tracking-value">07h 15m</div>
          </div>
        </div>
        
        <div class="time-chart">
          <div class="chart-bars">
            <div class="chart-bar" style="height: 60%;"></div>
            <div class="chart-bar" style="height: 80%;"></div>
            <div class="chart-bar" style="height: 40%;"></div>
            <div class="chart-bar" style="height: 70%;"></div>
            <div class="chart-bar" style="height: 90%;"></div>
            <div class="chart-bar" style="height: 50%;"></div>
            <div class="chart-bar" style="height: 75%;"></div>
            <div class="chart-bar" style="height: 85%;"></div>
            <div class="chart-bar" style="height: 65%;"></div>
            <div class="chart-bar" style="height: 55%;"></div>
          </div>
          <div class="chart-labels">
            <div class="chart-label">08:00</div>
            <div class="chart-label">09:00</div>
            <div class="chart-label">10:00</div>
            <div class="chart-label">11:00</div>
            <div class="chart-label">12:00</div>
            <div class="chart-label">13:00</div>
            <div class="chart-label">14:00</div>
            <div class="chart-label">15:00</div>
            <div class="chart-label">16:00</div>
            <div class="chart-label">17:00</div>
          </div>
        </div>
      </div>
      
      <div class="attendance-footer">
        <div class="punch-status">
          <i class="fa fa-clock"></i>
          <span>Punch in at 10:00 AM</span>
        </div>
        <button class="punch-btn">
          <span>PUNCH OUT</span>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .attendance-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .attendance-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e9ecef;
    }
    
    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }
    
    .date-display {
      font-size: 14px;
      color: #6c757d;
    }
    
    .attendance-content {
      padding: 16px;
      flex: 1;
    }
    
    .attendance-stats {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      margin-bottom: 24px;
    }
    
    .stat-item {
      text-align: center;
    }
    
    .stat-value {
      font-size: 20px;
      font-weight: 600;
      color: #343a40;
      margin-bottom: 4px;
    }
    
    .stat-unit {
      font-size: 14px;
      font-weight: normal;
      color: #6c757d;
    }
    
    .stat-label {
      font-size: 14px;
      color: #6c757d;
      margin-bottom: 8px;
    }
    
    .stat-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }
    
    .indicator-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 4px;
    }
    
    .indicator-dot.green {
      background-color: #28a745;
    }
    
    .indicator-dot.red {
      background-color: #dc3545;
    }
    
    .indicator-text {
      color: #6c757d;
    }
    
    .time-tracking {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      margin-bottom: 24px;
    }
    
    .tracking-item {
      text-align: center;
    }
    
    .tracking-label {
      font-size: 14px;
      color: #6c757d;
      margin-bottom: 4px;
    }
    
    .tracking-value {
      font-size: 16px;
      font-weight: 600;
      color: #343a40;
    }
    
    .time-chart {
      margin-top: 16px;
    }
    
    .chart-bars {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      height: 60px;
      margin-bottom: 8px;
    }
    
    .chart-bar {
      width: 8px;
      background: linear-gradient(to top, #28a745, #20c997);
      border-radius: 4px;
    }
    
    .chart-labels {
      display: flex;
      justify-content: space-between;
    }
    
    .chart-label {
      font-size: 10px;
      color: #6c757d;
      transform: rotate(-45deg);
      transform-origin: top left;
      white-space: nowrap;
    }
    
    .attendance-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-top: 1px solid #e9ecef;
    }
    
    .punch-status {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #6c757d;
    }
    
    .punch-status i {
      margin-right: 8px;
    }
    
    .punch-btn {
      padding: 8px 16px;
      background-color: #ff6b35;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
    }
    
    .punch-btn:hover {
      background-color: #e85a2a;
    }
    
    @media (max-width: 1200px) {
      .attendance-stats, .time-tracking {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    @media (max-width: 576px) {
      .attendance-stats, .time-tracking {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class EmployeeAttendanceComponent {
  @Input() employee: any;
}
