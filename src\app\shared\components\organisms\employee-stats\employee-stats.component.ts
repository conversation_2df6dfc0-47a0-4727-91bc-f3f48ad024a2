import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-stats',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="stats-container">
      <div class="stats-header">
        <h3 class="section-title">Leave Details</h3>
        <div class="actions">
          <button class="action-btn">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      
      <div class="stats-content">
        <div class="stats-row">
          <div class="stats-column">
            <div class="stats-label">Total Leaves</div>
            <div class="stats-value">{{ employee?.leaves?.taken + employee?.leaves?.remaining }}</div>
          </div>
          <div class="stats-column">
            <div class="stats-label">Taken</div>
            <div class="stats-value">{{ employee?.leaves?.taken }}</div>
          </div>
        </div>
        
        <div class="leave-chart">
          <div class="chart-container">
            <div class="donut-chart">
              <svg viewBox="0 0 36 36" class="donut">
                <circle class="donut-ring" cx="18" cy="18" r="15.91549430918954" fill="transparent" stroke="#e9ecef" stroke-width="3"></circle>
                <circle class="donut-segment" cx="18" cy="18" r="15.91549430918954" fill="transparent" 
                  [attr.stroke-dasharray]="calculateDonutSegment(employee?.leaves?.taken, employee?.leaves?.taken + employee?.leaves?.remaining)"
                  stroke-dashoffset="25" stroke="#ff6b35" stroke-width="3"></circle>
              </svg>
              <div class="donut-text">
                <span class="donut-number">{{ calculatePercentage(employee?.leaves?.taken, employee?.leaves?.taken + employee?.leaves?.remaining) }}%</span>
                <span class="donut-label">Used</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="stats-row">
          <div class="stats-column">
            <div class="stats-label">Absent</div>
            <div class="stats-value">{{ employee?.attendance?.absent }}</div>
          </div>
          <div class="stats-column">
            <div class="stats-label">Present</div>
            <div class="stats-value">{{ employee?.attendance?.present }}</div>
          </div>
        </div>
        
        <div class="stats-row">
          <div class="stats-column">
            <div class="stats-label">Pending</div>
            <div class="stats-value">{{ employee?.tasks?.pending }}</div>
          </div>
          <div class="stats-column">
            <div class="stats-label">Loss of Pay</div>
            <div class="stats-value">0</div>
          </div>
        </div>
        
        <div class="stats-row">
          <div class="stats-column">
            <div class="stats-label">Overtime</div>
            <div class="stats-value">2</div>
          </div>
          <div class="stats-column">
            <div class="stats-label">Total Tasks</div>
            <div class="stats-value">{{ employee?.tasks?.completed + employee?.tasks?.pending }}</div>
          </div>
        </div>
      </div>
      
      <div class="stats-footer">
        <button class="apply-btn">
          <span>Apply Now Leave</span>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .stats-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .stats-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e9ecef;
    }
    
    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }
    
    .action-btn {
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      font-size: 16px;
    }
    
    .stats-content {
      padding: 16px;
      flex: 1;
    }
    
    .stats-row {
      display: flex;
      margin-bottom: 16px;
    }
    
    .stats-column {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .stats-label {
      font-size: 14px;
      color: #6c757d;
      margin-bottom: 4px;
    }
    
    .stats-value {
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }
    
    .leave-chart {
      display: flex;
      justify-content: center;
      margin: 16px 0;
    }
    
    .chart-container {
      position: relative;
      width: 120px;
      height: 120px;
    }
    
    .donut-chart {
      position: relative;
      width: 100%;
      height: 100%;
    }
    
    .donut {
      width: 100%;
      height: 100%;
    }
    
    .donut-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
    }
    
    .donut-number {
      display: block;
      font-size: 20px;
      font-weight: 600;
      color: #343a40;
    }
    
    .donut-label {
      display: block;
      font-size: 12px;
      color: #6c757d;
    }
    
    .stats-footer {
      padding: 16px;
      border-top: 1px solid #e9ecef;
    }
    
    .apply-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 8px 16px;
      background-color: #ff6b35;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
    }
    
    .apply-btn:hover {
      background-color: #e85a2a;
    }
  `]
})
export class EmployeeStatsComponent {
  @Input() employee: any;
  
  calculateDonutSegment(taken: number, total: number): string {
    if (!taken || !total) return '0 100';
    
    const percentage = (taken / total) * 100;
    return `${percentage} ${100 - percentage}`;
  }
  
  calculatePercentage(taken: number, total: number): number {
    if (!taken || !total) return 0;
    
    return Math.round((taken / total) * 100);
  }
}
