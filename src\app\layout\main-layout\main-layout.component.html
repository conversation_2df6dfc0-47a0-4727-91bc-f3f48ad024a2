<div class="layout__main main-layout"
     [ngClass]="{
       'menu-collapsed': menuCollapsed,
       'theme-dark': isDarkTheme(),
       'theme-light': !isDarkTheme(),
       'header-fixed': isHeaderFixed(),
       'header-static': isHeaderStatic(),
       'header-sticky': isHeaderSticky()
     }"
     [attr.data-theme]="currentTheme()"
     [attr.data-header-position]="headerPosition()">

  <app-side-menu
    class="main-sidebar"
    [logo]="logoUrl"
    title="SmartHR"
    [collapsed]="menuCollapsed">
  </app-side-menu>

  <div class="layout__content main-content">
    <app-header
      class="main-header"
      [menuCollapsed]="menuCollapsed"
      (toggleMenuCollapse)="toggleMenuCollapse()">
    </app-header>
    <main class="layout__main-content">
      <router-outlet></router-outlet>
    </main>
  </div>

  <!-- AI Chat Widget - Global across all pages -->
  <app-ai-chat-widget></app-ai-chat-widget>
</div>
