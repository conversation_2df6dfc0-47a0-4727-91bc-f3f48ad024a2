import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-confirmation-dialog',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="confirmation-dialog-overlay" *ngIf="isOpen" (click)="onOverlayClick($event)">
      <div class="confirmation-dialog">
        <div class="confirmation-dialog__header">
          <h3 class="confirmation-dialog__title">{{ title }}</h3>
          <button class="confirmation-dialog__close" (click)="onCancel()">×</button>
        </div>
        <div class="confirmation-dialog__content">
          <p class="confirmation-dialog__message">{{ message }}</p>
        </div>
        <div class="confirmation-dialog__actions">
          <button class="confirmation-dialog__button confirmation-dialog__button--cancel" (click)="onCancel()">
            {{ cancelText }}
          </button>
          <button class="confirmation-dialog__button confirmation-dialog__button--confirm" (click)="onConfirm()">
            {{ confirmText }}
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .confirmation-dialog-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      animation: fadeIn 0.2s ease;
    }
    
    .confirmation-dialog {
      background-color: #ffffff;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      width: 100%;
      max-width: 400px;
      overflow: hidden;
      animation: slideIn 0.3s ease;
    }
    
    .confirmation-dialog__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .confirmation-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #111827;
      margin: 0;
    }
    
    .confirmation-dialog__close {
      background: none;
      border: none;
      font-size: 24px;
      color: #6b7280;
      cursor: pointer;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 8px;
      transition: all 0.2s ease;
    }
    
    .confirmation-dialog__close:hover {
      background-color: #f3f4f6;
      color: #111827;
    }
    
    .confirmation-dialog__content {
      padding: 24px;
    }
    
    .confirmation-dialog__message {
      font-size: 16px;
      color: #4b5563;
      margin: 0;
      line-height: 1.5;
    }
    
    .confirmation-dialog__actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      padding: 16px 24px;
      border-top: 1px solid #f0f0f0;
    }
    
    .confirmation-dialog__button {
      padding: 10px 20px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      border: none;
    }
    
    .confirmation-dialog__button--cancel {
      background-color: #f3f4f6;
      color: #4b5563;
    }
    
    .confirmation-dialog__button--cancel:hover {
      background-color: #e5e7eb;
    }
    
    .confirmation-dialog__button--confirm {
      background-color: #ef4444;
      color: #ffffff;
    }
    
    .confirmation-dialog__button--confirm:hover {
      background-color: #dc2626;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes slideIn {
      from { transform: translateY(-20px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
  `]
})
export class ConfirmationDialogComponent {
  @Input() isOpen: boolean = false;
  @Input() title: string = 'Confirm Action';
  @Input() message: string = 'Are you sure you want to proceed?';
  @Input() confirmText: string = 'Confirm';
  @Input() cancelText: string = 'Cancel';
  
  @Output() confirm = new EventEmitter<void>();
  @Output() cancel = new EventEmitter<void>();
  
  onConfirm(): void {
    this.confirm.emit();
    this.isOpen = false;
  }
  
  onCancel(): void {
    this.cancel.emit();
    this.isOpen = false;
  }
  
  onOverlayClick(event: MouseEvent): void {
    // Only close if the overlay itself was clicked
    if ((event.target as HTMLElement).classList.contains('confirmation-dialog-overlay')) {
      this.onCancel();
    }
  }
}
