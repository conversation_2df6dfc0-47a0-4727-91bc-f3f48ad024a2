import { Component, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { DepartmentsStore } from '../../../core/state/departments/departments.state';
import { DepartmentDetail, EmployeeList } from '../../../core/models/department.interface';
import { EditDepartmentModalComponent } from '../../../shared/modals/edit-department-modal/edit-department-modal.component';
import { ButtonComponent } from '../../../shared/components/atoms/button/button.component';
import { IconComponent } from '../../../shared/components/atoms/icon/icon.component';
import { ModalService } from '../../../core/services/modal.service';
import { ConfirmationDialogComponent } from '../../../shared/components/atoms/confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'app-department-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ButtonComponent,
    IconComponent
  ],
  templateUrl: './department-detail.component.html',
  styleUrls: ['./department-detail.component.scss']
})
export class DepartmentDetailComponent implements OnInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly modalService = inject(ModalService);
  private readonly departmentsStore = inject(DepartmentsStore);
  private readonly destroy$ = new Subject<void>();

  // Component state
  departmentId: number | null = null;
  departmentDetail = this.departmentsStore.selectedDepartmentDetail;
  isLoading = this.departmentsStore.isLoadingDetail;
  error = this.departmentsStore.error;

  ngOnInit(): void {
    // Get department ID from route parameters
    this.route.params
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        const id = parseInt(params['id'], 10);
        if (!isNaN(id)) {
          this.departmentId = id;
          this.loadDepartmentDetail(id);
        } else {
          this.router.navigate(['/departments']);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.departmentsStore.clearDepartmentDetail();
  }

  /**
   * Load department details
   */
  private loadDepartmentDetail(id: number): void {
    this.departmentsStore.loadDepartmentDetail(id);
  }

  /**
   * Navigate back to departments list
   */
  goBack(): void {
    this.router.navigate(['/app/departments']);
  }

  /**
   * Open edit department modal
   */
  editDepartment(): void {
    const department = this.departmentDetail();
    if (!department) return;

    // TODO: Implement edit department modal integration
    console.log('Edit department:', department);
    // For now, just reload the data
    if (this.departmentId) {
      this.loadDepartmentDetail(this.departmentId);
    }
  }

  /**
   * Delete department with confirmation
   */
  async deleteDepartment(): Promise<void> {
    const department = this.departmentDetail();
    if (!department) return;

    const confirmed = await this.modalService.confirm({
      title: 'Delete Department',
      message: `Are you sure you want to delete "${department.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel'
    });

    if (confirmed && department.id) {
      this.departmentsStore.deleteDepartment(department.id);
      // Navigate back to departments list after deletion
      this.router.navigate(['/app/departments']);
    }
  }

  /**
   * Navigate to employee detail page
   */
  viewEmployeeDetail(employee: EmployeeList): void {
    this.router.navigate(['/app/employees', employee.id]);
  }

  /**
   * Get status badge class
   */
  getStatusClass(status: string): string {
    return status === 'active' ? 'status-active' : 'status-inactive';
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Get employee count as number
   */
  getEmployeeCount(): number {
    const department = this.departmentDetail();
    return department ? parseInt(department.employee_count, 10) || 0 : 0;
  }

  /**
   * Get active employees count
   */
  getActiveEmployeesCount(): number {
    const department = this.departmentDetail();
    return department ? department.employees.filter(emp => emp.status === 'active').length : 0;
  }

  /**
   * Get inactive employees count
   */
  getInactiveEmployeesCount(): number {
    const department = this.departmentDetail();
    return department ? department.employees.filter(emp => emp.status === 'inactive').length : 0;
  }

  /**
   * Get default avatar URL
   */
  getDefaultAvatar(): string {
    return 'assets/images/default-avatar.png';
  }

  /**
   * Handle image load error
   */
  onImageError(event: any): void {
    event.target.src = this.getDefaultAvatar();
  }
}
