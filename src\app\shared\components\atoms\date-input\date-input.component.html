<div class="date-input" [class]="sizeClass + ' ' + variantClass">
  <!-- Label -->
  <label *ngIf="label" class="date-input__label" [class.date-input__label--required]="required">
    {{ label }}
    <span *ngIf="required" class="date-input__required">*</span>
  </label>

  <!-- Input Container -->
  <div class="date-input__container" 
       [class.date-input__container--disabled]="disabled"
       [class.date-input__container--error]="hasError">
    
    <!-- Date Input -->
    <input
      type="date"
      class="date-input__input"
      [value]="value"
      [disabled]="disabled"
      [min]="min"
      [max]="max"
      [placeholder]="placeholder"
      [attr.aria-label]="label"
      (input)="onDateChange($event)"
      (blur)="onBlur()">

    <!-- Age Display -->
    <div class="date-input__age" *ngIf="showAge && calculatedAge !== null">
      <span class="date-input__age-label">Age:</span>
      <span class="date-input__age-value">{{ calculatedAge }} years</span>
    </div>
  </div>

  <!-- Error Message -->
  <div class="date-input__error" *ngIf="hasError" role="alert">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="12" y1="8" x2="12" y2="12"></line>
      <line x1="12" y1="16" x2="12.01" y2="16"></line>
    </svg>
    <span>{{ error }}</span>
  </div>
</div>
