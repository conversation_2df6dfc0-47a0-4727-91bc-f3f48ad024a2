import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { ChatRequest, ChatResponse, ApiError } from '../models/ai-chat.interface';

@Injectable({
  providedIn: 'root'
})
export class AiChatService {
  private readonly http = inject(HttpClient);
  private readonly apiUrl = environment.apiUrl || 'http://127.0.0.1:8000';
  private readonly chatEndpoint = '/api/v1/ai/chat/';

  /**
   * Send a chat message to the AI assistant
   * @param question The user's question
   * @returns Observable of the AI response
   */
  sendMessage(question: string): Observable<ChatResponse> {
    const payload: ChatRequest = { question: question.trim() };

    console.log('Sending AI chat request:', payload);

    return this.http.post<ChatResponse>(`${this.apiUrl}${this.chatEndpoint}`, payload)
      .pipe(
        map(response => {
          console.log('AI chat response received:', response);
          return {
            answer: response.answer || 'I apologize, but I couldn\'t generate a response. Please try again.',
            timestamp: response.timestamp || new Date().toISOString(),
            conversation_id: response.conversation_id
          };
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Handle HTTP errors
   * @param error The HTTP error response
   * @returns Observable error
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    console.error('AI Chat Service Error:', error);

    let errorMessage = 'An unexpected error occurred. Please try again.';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Network error: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage = 'Invalid request. Please check your message and try again.';
          break;
        case 401:
          errorMessage = 'Authentication required. Please log in and try again.';
          break;
        case 403:
          errorMessage = 'Access denied. You don\'t have permission to use the AI assistant.';
          break;
        case 404:
          errorMessage = 'AI service not available. Please try again later.';
          break;
        case 429:
          errorMessage = 'Too many requests. Please wait a moment and try again.';
          break;
        case 500:
          errorMessage = 'Server error. Please try again later.';
          break;
        case 503:
          errorMessage = 'AI service temporarily unavailable. Please try again later.';
          break;
        default:
          errorMessage = `Error ${error.status}: ${error.error?.message || error.message}`;
      }
    }

    const apiError: ApiError = {
      message: errorMessage,
      code: error.status?.toString(),
      details: error.error
    };

    return throwError(() => apiError);
  }

  /**
   * Validate chat message
   * @param message The message to validate
   * @returns Validation result
   */
  validateMessage(message: string): { isValid: boolean; error?: string } {
    if (!message || message.trim().length === 0) {
      return { isValid: false, error: 'Message cannot be empty' };
    }

    if (message.trim().length > 1000) {
      return { isValid: false, error: 'Message is too long (max 1000 characters)' };
    }

    return { isValid: true };
  }

  /**
   * Generate unique message ID
   * @returns Unique message ID
   */
  generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
