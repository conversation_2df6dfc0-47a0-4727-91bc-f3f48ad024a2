@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

:host {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

// Employee Form - BEM Structure
// Block: employee-form

.employee-form {
  $root: &;

  // Elements
  &__container {
    display: flex;
    gap: 32px;
  }

  &__avatar-section {
    width: 250px;
    flex-shrink: 0;
  }

  &__fields-section {
    flex: 1;
  }

  &__section-title {
    font-size: 16px;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
  }

  &__help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 8px;
    text-align: center;
  }

  &__row {
    display: flex;
    gap: 20px;
    margin-bottom: 24px;
  }

  &__group {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  &__label {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #495057;
  }

  &__required {
    color: #dc3545;
    margin-left: 2px;
  }

  &__input {
    padding: 12px 16px;
    border: 1px solid #ced4da;
    border-radius: 12px;
    font-size: 14px;
    color: #495057;
    transition: all 0.3s ease;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    &::placeholder {
      color: #adb5bd;
    }

    &:hover {
      border-color: #adb5bd;
    }

    &:focus {
      border-color: #ff6b35;
      outline: 0;
      box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15);
    }

    &--invalid {
      border-color: #dc3545;
      background-color: #fff8f8;

      &:focus {
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.15);
      }
    }

    &--select {
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236c757d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 12px center;
      background-size: 16px;
      padding-right: 40px;
    }

    &--date {
      padding-right: 40px; // Make room for the calendar icon
      width: 100%;

      // Customize the date picker appearance
      &::-webkit-calendar-picker-indicator {
        opacity: 0; // Hide the default calendar icon
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        cursor: pointer;
      }
    }
  }

  &__date-container {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
  }

  &__calendar-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
      color: #ff6b35;
    }
  }

  &__error {
    font-size: 12px;
    color: #dc3545;
    margin-top: 6px;
    font-weight: 500;
  }

  &__button {
    margin: 0 16px;
  }

  // Media queries
  @media (max-width: 992px) {
    &__container {
      flex-direction: column;
    }

    &__avatar-section {
      width: 100%;
      margin-bottom: 24px;
    }
  }

  @media (max-width: 768px) {
    &__row {
      flex-direction: column;
      gap: 16px;
    }
  }
}
