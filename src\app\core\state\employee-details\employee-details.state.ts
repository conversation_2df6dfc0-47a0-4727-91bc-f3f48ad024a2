import { computed, inject } from '@angular/core';
import { patchState, signalStore, withComputed, withMethods, withState } from '@ngrx/signals';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, switchMap, tap, catchError, EMPTY, debounceTime, distinctUntilChanged } from 'rxjs';

// Import services and interfaces
import { EmployeeDetailsService } from '../../services/employee-details.service';
import {
  EmployeeDetails,
  EmployeeDetailsInput,
  EmployeeDocument,
  EmployeeSkill,
  EmployeeJobHistory,
  EmployeeDetailsResponse
} from '../../models/employee-extended.interface';

// Define the employee details state interface
export interface EmployeeDetailsState {
  // Employee Details
  employeeDetails: EmployeeDetails[];
  selectedEmployeeDetails: EmployeeDetails | null;

  // Related Data
  documents: EmployeeDocument[];
  skills: EmployeeSkill[];
  jobHistory: EmployeeJobHistory[];

  // Loading States
  isLoading: boolean;
  isLoadingDetails: boolean;
  isLoadingDocuments: boolean;
  isLoadingSkills: boolean;
  isLoadingJobHistory: boolean;

  // Error States
  error: string | null;
  detailsError: string | null;
  documentsError: string | null;
  skillsError: string | null;
  jobHistoryError: string | null;

  // Pagination
  page: number;
  pageSize: number;
  totalCount: number;

  // Search and Filters
  searchTerm: string;
  ordering: string;

  // Current Employee Context
  currentEmployeeId: number | null;
}

// Initial state
const initialState: EmployeeDetailsState = {
  employeeDetails: [],
  selectedEmployeeDetails: null,
  documents: [],
  skills: [],
  jobHistory: [],
  isLoading: false,
  isLoadingDetails: false,
  isLoadingDocuments: false,
  isLoadingSkills: false,
  isLoadingJobHistory: false,
  error: null,
  detailsError: null,
  documentsError: null,
  skillsError: null,
  jobHistoryError: null,
  page: 1,
  pageSize: 10,
  totalCount: 0,
  searchTerm: '',
  ordering: '-created_at',
  currentEmployeeId: null
};

export const EmployeeDetailsStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withComputed((store) => ({
    // Computed properties for better data access
    hasEmployeeDetails: computed(() => store.employeeDetails().length > 0),
    hasDocuments: computed(() => store.documents().length > 0),
    hasSkills: computed(() => store.skills().length > 0),
    hasJobHistory: computed(() => store.jobHistory().length > 0),

    // Loading state combinations
    isAnyLoading: computed(() =>
      store.isLoading() ||
      store.isLoadingDetails() ||
      store.isLoadingDocuments() ||
      store.isLoadingSkills() ||
      store.isLoadingJobHistory()
    ),

    // Error state combinations
    hasAnyError: computed(() =>
      !!store.error() ||
      !!store.detailsError() ||
      !!store.documentsError() ||
      !!store.skillsError() ||
      !!store.jobHistoryError()
    ),

    // Pagination info
    hasNextPage: computed(() => {
      const { page, pageSize, totalCount } = store;
      return page() * pageSize() < totalCount();
    }),

    hasPreviousPage: computed(() => store.page() > 1),

    totalPages: computed(() => {
      const { pageSize, totalCount } = store;
      return Math.ceil(totalCount() / pageSize());
    }),

    // Skills by category
    skillsByCategory: computed(() => {
      const skills = store.skills();
      return skills.reduce((acc, skill) => {
        const category = skill.skill_category || 'other';
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(skill);
        return acc;
      }, {} as Record<string, EmployeeSkill[]>);
    }),

    // Documents by type
    documentsByType: computed(() => {
      const documents = store.documents();
      return documents.reduce((acc, doc) => {
        const type = doc.document_type;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(doc);
        return acc;
      }, {} as Record<string, EmployeeDocument[]>);
    })
  })),
  withMethods((store, employeeDetailsService = inject(EmployeeDetailsService)) => ({
    // Load employee details list
    loadEmployeeDetails: rxMethod<{ page?: number; pageSize?: number; searchTerm?: string; ordering?: string }>(
      pipe(
        debounceTime(300),
        distinctUntilChanged(),
        tap(() => patchState(store, { isLoading: true, error: null })),
        switchMap(({ page = 1, pageSize = 10, searchTerm = '', ordering = '-created_at' }) => {
          // Update state with new parameters
          patchState(store, { page, pageSize, searchTerm, ordering });

          return employeeDetailsService.getEmployeeDetails(page, pageSize, searchTerm, ordering).pipe(
            tap((response) => {
              patchState(store, {
                employeeDetails: response.results,
                totalCount: response.count,
                isLoading: false,
                error: null
              });
            }),
            catchError((error) => {
              patchState(store, {
                isLoading: false,
                error: typeof error === 'string' ? error : 'Failed to load employee details'
              });
              return EMPTY;
            })
          );
        })
      )
    ),

    // Load specific employee details by ID
    loadEmployeeDetailsById: rxMethod<number>(
      pipe(
        tap(() => patchState(store, { isLoadingDetails: true, detailsError: null })),
        switchMap((id) => {
          return employeeDetailsService.getEmployeeDetailsById(id).pipe(
            tap((details) => {
              patchState(store, {
                selectedEmployeeDetails: details,
                isLoadingDetails: false,
                detailsError: null
              });
            }),
            catchError((error) => {
              patchState(store, {
                isLoadingDetails: false,
                detailsError: typeof error === 'string' ? error : 'Failed to load employee details'
              });
              return EMPTY;
            })
          );
        })
      )
    ),

    // Load employee details by employee ID
    loadEmployeeDetailsByEmployeeId: rxMethod<number>(
      pipe(
        tap((employeeId) => patchState(store, {
          isLoadingDetails: true,
          detailsError: null,
          currentEmployeeId: employeeId
        })),
        switchMap((employeeId) => {
          return employeeDetailsService.getEmployeeDetailsByEmployeeId(employeeId).pipe(
            tap((details) => {
              patchState(store, {
                selectedEmployeeDetails: details,
                isLoadingDetails: false,
                detailsError: null
              });
            }),
            catchError((error) => {
              patchState(store, {
                isLoadingDetails: false,
                detailsError: typeof error === 'string' ? error : 'Failed to load employee details'
              });
              return EMPTY;
            })
          );
        })
      )
    ),

    // Create employee details
    createEmployeeDetails: rxMethod<EmployeeDetailsInput>(
      pipe(
        tap(() => patchState(store, { isLoadingDetails: true, detailsError: null })),
        switchMap((detailsInput) => {
          return employeeDetailsService.createEmployeeDetails(detailsInput).pipe(
            tap((details) => {
              patchState(store, {
                selectedEmployeeDetails: details,
                employeeDetails: [...store.employeeDetails(), details],
                isLoadingDetails: false,
                detailsError: null
              });
            }),
            catchError((error) => {
              patchState(store, {
                isLoadingDetails: false,
                detailsError: typeof error === 'string' ? error : 'Failed to create employee details'
              });
              return EMPTY;
            })
          );
        })
      )
    ),

    // Update employee details
    updateEmployeeDetails: rxMethod<{ id: number; details: EmployeeDetailsInput }>(
      pipe(
        tap(() => patchState(store, { isLoadingDetails: true, detailsError: null })),
        switchMap(({ id, details }) => {
          return employeeDetailsService.updateEmployeeDetails(id, details).pipe(
            tap((updatedDetails) => {
              const updatedList = store.employeeDetails().map(item =>
                item.id === id ? updatedDetails : item
              );

              patchState(store, {
                selectedEmployeeDetails: updatedDetails,
                employeeDetails: updatedList,
                isLoadingDetails: false,
                detailsError: null
              });
            }),
            catchError((error) => {
              patchState(store, {
                isLoadingDetails: false,
                detailsError: typeof error === 'string' ? error : 'Failed to update employee details'
              });
              return EMPTY;
            })
          );
        })
      )
    ),

    // Load employee documents
    loadEmployeeDocuments: rxMethod<number>(
      pipe(
        tap(() => patchState(store, { isLoadingDocuments: true, documentsError: null })),
        switchMap((employeeId) => {
          return employeeDetailsService.getEmployeeDocuments(employeeId).pipe(
            tap((documents) => {
              patchState(store, {
                documents,
                isLoadingDocuments: false,
                documentsError: null
              });
            }),
            catchError((error) => {
              patchState(store, {
                isLoadingDocuments: false,
                documentsError: typeof error === 'string' ? error : 'Failed to load documents'
              });
              return EMPTY;
            })
          );
        })
      )
    ),

    // Load employee skills
    loadEmployeeSkills: rxMethod<number>(
      pipe(
        tap(() => patchState(store, { isLoadingSkills: true, skillsError: null })),
        switchMap((employeeId) => {
          return employeeDetailsService.getEmployeeSkills(employeeId).pipe(
            tap((skills) => {
              patchState(store, {
                skills,
                isLoadingSkills: false,
                skillsError: null
              });
            }),
            catchError((error) => {
              patchState(store, {
                isLoadingSkills: false,
                skillsError: typeof error === 'string' ? error : 'Failed to load skills'
              });
              return EMPTY;
            })
          );
        })
      )
    ),

    // Load employee job history
    loadEmployeeJobHistory: rxMethod<number>(
      pipe(
        tap(() => patchState(store, { isLoadingJobHistory: true, jobHistoryError: null })),
        switchMap((employeeId) => {
          return employeeDetailsService.getEmployeeJobHistory(employeeId).pipe(
            tap((jobHistory) => {
              patchState(store, {
                jobHistory,
                isLoadingJobHistory: false,
                jobHistoryError: null
              });
            }),
            catchError((error) => {
              patchState(store, {
                isLoadingJobHistory: false,
                jobHistoryError: typeof error === 'string' ? error : 'Failed to load job history'
              });
              return EMPTY;
            })
          );
        })
      )
    ),

    // Load all employee related data
    loadAllEmployeeData: (employeeId: number) => {
      // For now, just set the current employee ID
      console.log('Loading employee data for ID:', employeeId);
      patchState(store, { currentEmployeeId: employeeId });
    },

    // Utility methods
    setSearchTerm: (searchTerm: string) => {
      patchState(store, { searchTerm });
    },

    setOrdering: (ordering: string) => {
      patchState(store, { ordering });
    },

    clearErrors: () => {
      patchState(store, {
        error: null,
        detailsError: null,
        documentsError: null,
        skillsError: null,
        jobHistoryError: null
      });
    },

    clearSelectedEmployee: () => {
      patchState(store, {
        selectedEmployeeDetails: null,
        documents: [],
        skills: [],
        jobHistory: [],
        currentEmployeeId: null
      });
    }
  }))
);
