import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

// Import atomic components
import { ButtonComponent } from '../../atoms/button/button.component';

// Import interfaces
import { EmployeeHike } from '../../../../core/services/employee-management.service';

@Component({
  selector: 'app-hike-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonComponent
  ],
  templateUrl: './hike-form.component.html',
  styleUrls: ['./hike-form.component.scss']
})
export class HikeFormComponent implements OnInit, OnDestroy {
  @Input() employeeId: number | null = null;
  @Input() hikeData: EmployeeHike | null = null;
  @Input() isEdit: boolean = false;
  @Input() currentSalary: number = 0;
  @Output() formSubmit = new EventEmitter<Partial<EmployeeHike>>();
  @Output() formCancel = new EventEmitter<void>();

  private destroy$ = new Subject<void>();
  private fb = inject(FormBuilder);

  hikeForm: FormGroup;
  isSubmitting = false;

  // Hike calculation options
  calculationMethod: 'percentage' | 'amount' = 'percentage';

  constructor() {
    this.hikeForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.hikeData && this.isEdit) {
      this.populateForm(this.hikeData);
    } else {
      // Set current salary as previous salary for new hikes
      this.hikeForm.patchValue({
        previous_salary: this.currentSalary
      });
    }

    // Watch for changes to calculate hike values
    this.hikeForm.get('previous_salary')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => this.calculateHike());

    this.hikeForm.get('hike_percentage')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.calculationMethod === 'percentage') {
          this.calculateHikeFromPercentage();
        }
      });

    this.hikeForm.get('hike_amount')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.calculationMethod === 'amount') {
          this.calculateHikeFromAmount();
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      employee_id: [this.employeeId, [Validators.required]],
      previous_salary: [0, [Validators.required, Validators.min(0)]],
      hike_percentage: [0, [Validators.required, Validators.min(0), Validators.max(100)]],
      hike_amount: [0, [Validators.required, Validators.min(0)]],
      new_salary: [{ value: 0, disabled: true }],
      effective_date: [new Date().toISOString().split('T')[0], [Validators.required]],
      reason: ['', [Validators.required, Validators.maxLength(500)]],
      status: ['pending']
    });
  }

  private populateForm(hike: EmployeeHike): void {
    this.hikeForm.patchValue({
      employee_id: hike.employee_id,
      previous_salary: hike.previous_salary,
      hike_percentage: hike.hike_percentage,
      hike_amount: hike.hike_amount,
      new_salary: hike.new_salary,
      effective_date: hike.effective_date,
      reason: hike.reason,
      status: hike.status
    });
  }

  setCalculationMethod(method: 'percentage' | 'amount'): void {
    this.calculationMethod = method;
    
    if (method === 'percentage') {
      this.hikeForm.get('hike_percentage')?.enable();
      this.hikeForm.get('hike_amount')?.disable();
      this.calculateHikeFromPercentage();
    } else {
      this.hikeForm.get('hike_amount')?.enable();
      this.hikeForm.get('hike_percentage')?.disable();
      this.calculateHikeFromAmount();
    }
  }

  private calculateHike(): void {
    if (this.calculationMethod === 'percentage') {
      this.calculateHikeFromPercentage();
    } else {
      this.calculateHikeFromAmount();
    }
  }

  private calculateHikeFromPercentage(): void {
    const previousSalary = this.hikeForm.get('previous_salary')?.value || 0;
    const hikePercentage = this.hikeForm.get('hike_percentage')?.value || 0;
    
    const hikeAmount = (previousSalary * hikePercentage) / 100;
    const newSalary = previousSalary + hikeAmount;
    
    this.hikeForm.get('hike_amount')?.setValue(hikeAmount, { emitEvent: false });
    this.hikeForm.get('new_salary')?.setValue(newSalary);
  }

  private calculateHikeFromAmount(): void {
    const previousSalary = this.hikeForm.get('previous_salary')?.value || 0;
    const hikeAmount = this.hikeForm.get('hike_amount')?.value || 0;
    
    const hikePercentage = previousSalary > 0 ? (hikeAmount / previousSalary) * 100 : 0;
    const newSalary = previousSalary + hikeAmount;
    
    this.hikeForm.get('hike_percentage')?.setValue(hikePercentage, { emitEvent: false });
    this.hikeForm.get('new_salary')?.setValue(newSalary);
  }

  onSubmit(): void {
    if (this.hikeForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      
      const formValue = this.hikeForm.getRawValue();
      const hikeData: Partial<EmployeeHike> = {
        employee_id: formValue.employee_id,
        previous_salary: formValue.previous_salary,
        hike_percentage: formValue.hike_percentage,
        hike_amount: formValue.hike_amount,
        new_salary: formValue.new_salary,
        effective_date: formValue.effective_date,
        reason: formValue.reason,
        status: formValue.status
      };

      this.formSubmit.emit(hikeData);
    }
  }

  onCancel(): void {
    this.formCancel.emit();
  }

  // Getter methods for template
  get previousSalaryControl() {
    return this.hikeForm.get('previous_salary');
  }

  get hikePercentageControl() {
    return this.hikeForm.get('hike_percentage');
  }

  get hikeAmountControl() {
    return this.hikeForm.get('hike_amount');
  }

  get newSalaryControl() {
    return this.hikeForm.get('new_salary');
  }

  get effectiveDateControl() {
    return this.hikeForm.get('effective_date');
  }

  get reasonControl() {
    return this.hikeForm.get('reason');
  }

  // Validation helper methods
  isFieldInvalid(fieldName: string): boolean {
    const field = this.hikeForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.hikeForm.get(fieldName);
    if (field && field.errors) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} is required`;
      }
      if (field.errors['min']) {
        return `${this.getFieldLabel(fieldName)} must be greater than or equal to ${field.errors['min'].min}`;
      }
      if (field.errors['max']) {
        return `${this.getFieldLabel(fieldName)} must be less than or equal to ${field.errors['max'].max}`;
      }
      if (field.errors['maxlength']) {
        return `${this.getFieldLabel(fieldName)} must be less than ${field.errors['maxlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      previous_salary: 'Previous Salary',
      hike_percentage: 'Hike Percentage',
      hike_amount: 'Hike Amount',
      effective_date: 'Effective Date',
      reason: 'Reason'
    };
    return labels[fieldName] || fieldName;
  }
}
