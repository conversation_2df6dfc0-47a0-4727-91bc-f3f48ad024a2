@use 'sass:color';
@import '../../../../../styles/variables/_colors';

// Variables
$primary-color: #4f46e5;
$primary-light: #eef2ff;
$secondary-color: #ff6b35;
$text-dark: #111827;
$text-medium: #4b5563;
$text-light: #6b7280;
$border-color: #e5e7eb;
$background-light: #f9fafb;
$success-color: #10b981;
$warning-color: #f59e0b;
$danger-color: #ef4444;
$white: #ffffff;
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$border-radius-sm: 8px;
$border-radius-md: 12px;
$border-radius-lg: 16px;

// Main container
.employees {
  padding: 24px;

  // Header section
  &__header {
    margin-bottom: 32px;
  }

  // Title section
  &__title-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  // Title row with buttons
  &__title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    min-height: 40px; // Ensure consistent height

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
      min-height: auto;
    }
  }

  &__page-title {
    font-size: 28px;
    font-weight: 700;
    color: $text-dark;
    margin: 0;
  }

  // Breadcrumb
  &__breadcrumb {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: $text-light;
  }

  &__breadcrumb-item {
    display: flex;
    align-items: center;

    &--active {
      color: $text-dark;
      font-weight: 500;
    }
  }

  &__breadcrumb-separator {
    margin: 0 8px;
  }

  // Actions section
  &__actions {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-shrink: 0;

    .btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      border: none;
      border-radius: $border-radius-sm;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      position: relative;
      overflow: hidden;
      min-width: 120px;
      justify-content: center;

      i {
        font-size: 14px;
        transition: transform 0.3s ease;
      }

      span {
        font-weight: 600;
        letter-spacing: 0.025em;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

        i {
          transform: scale(1.1);
        }
      }

      &:active {
        transform: translateY(0);
        transition: transform 0.1s ease;
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3);
      }

      // Primary button (Add Employee) - Orange theme
      &.btn-primary {
        background: $primary;
        color: white;
        box-shadow: 0 2px 8px rgba($primary, 0.3);

        &:hover {
          background: color.adjust($primary, $lightness: -5%);
          box-shadow: 0 4px 12px rgba($primary, 0.4);
        }

        &:active {
          background: color.adjust($primary, $lightness: -10%);
        }
      }

      // Export button (secondary style) - Gray theme
      &.btn-export {
        background: $white;
        color: $text-secondary;
        border: 1px solid $border-light;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          background: $gray-100;
          color: $text-primary;
          border-color: $border-medium;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }

        &:active {
          background: $gray-200;
        }
      }

      // Loading state
      &.loading {
        pointer-events: none;
        opacity: 0.7;

        i {
          animation: spin 1s linear infinite;
        }
      }

      // Disabled state
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: none !important;
      }
    }
  }

  // Table container
  &__table-container {
    background-color: $white;
    border-radius: $border-radius-lg;
    box-shadow: $shadow-md;
    overflow: hidden;
    margin-top: 24px;
  }

  // Responsive styles
  @media (max-width: 768px) {
    padding: 16px;

    &__title-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    &__actions {
      width: 100%;
      flex-wrap: wrap;
      gap: 12px;

      .btn {
        flex: 1;
        min-width: 140px;
        padding: 14px 16px;
        font-size: 13px;

        &:first-child,
        &.btn-primary {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Additional button hover effects
.employees__actions .btn {
  &:hover {
    &:before {
      animation: shimmer 0.6s ease-in-out;
    }
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
