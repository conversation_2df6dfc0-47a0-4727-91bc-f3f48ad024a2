<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>HR Teamwork Illustration</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="gradient-orange">
            <stop stop-color="#FF8A65" offset="0%"></stop>
            <stop stop-color="#FF6B35" offset="50%"></stop>
            <stop stop-color="#E85A2A" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="gradient-dark">
            <stop stop-color="#495057" offset="0%"></stop>
            <stop stop-color="#343A40" offset="50%"></stop>
            <stop stop-color="#212529" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="gradient-light">
            <stop stop-color="#F8F9FA" offset="0%"></stop>
            <stop stop-color="#E9ECEF" offset="100%"></stop>
        </linearGradient>
        <filter x="-15.0%" y="-15.0%" width="130.0%" height="130.0%" filterUnits="objectBoundingBox" id="filter-blur">
            <feGaussianBlur stdDeviation="15" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <clipPath id="clip-path-circle">
            <circle cx="400" cy="300" r="250"></circle>
        </clipPath>
    </defs>
    <g id="HR-Teamwork-Illustration" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background Elements -->
        <rect id="Background" fill="#FFFFFF" x="0" y="0" width="800" height="600"></rect>
        
        <!-- Abstract Background Shapes -->
        <circle id="Glow-1" fill="url(#gradient-orange)" opacity="0.15" filter="url(#filter-blur)" cx="650" cy="150" r="100"></circle>
        <circle id="Glow-2" fill="url(#gradient-dark)" opacity="0.1" filter="url(#filter-blur)" cx="150" cy="450" r="120"></circle>
        <circle id="Glow-3" fill="url(#gradient-orange)" opacity="0.1" filter="url(#filter-blur)" cx="400" cy="500" r="80"></circle>
        
        <!-- Decorative Elements -->
        <path d="M0,300 C100,250 200,350 300,300 C400,250 500,350 600,300 C700,250 800,350 800,300" id="Wave-1" stroke="url(#gradient-orange)" stroke-width="2" opacity="0.3"></path>
        <path d="M0,330 C100,280 200,380 300,330 C400,280 500,380 600,330 C700,280 800,380 800,330" id="Wave-2" stroke="url(#gradient-orange)" stroke-width="2" opacity="0.3"></path>
        <path d="M0,360 C100,310 200,410 300,360 C400,310 500,410 600,360 C700,310 800,410 800,360" id="Wave-3" stroke="url(#gradient-orange)" stroke-width="2" opacity="0.3"></path>
        
        <!-- Main Illustration -->
        <g id="Main-Illustration" clip-path="url(#clip-path-circle)">
            <!-- Office Building Background -->
            <rect id="Office-Background" fill="url(#gradient-light)" x="150" y="100" width="500" height="400"></rect>
            <line x1="150" y1="200" x2="650" y2="200" id="Floor-Line-1" stroke="#DEE2E6" stroke-width="1"></line>
            <line x1="150" y1="300" x2="650" y2="300" id="Floor-Line-2" stroke="#DEE2E6" stroke-width="1"></line>
            <line x1="150" y1="400" x2="650" y2="400" id="Floor-Line-3" stroke="#DEE2E6" stroke-width="1"></line>
            
            <line x1="250" y1="100" x2="250" y2="500" id="Wall-Line-1" stroke="#DEE2E6" stroke-width="1"></line>
            <line x1="350" y1="100" x2="350" y2="500" id="Wall-Line-2" stroke="#DEE2E6" stroke-width="1"></line>
            <line x1="450" y1="100" x2="450" y2="500" id="Wall-Line-3" stroke="#DEE2E6" stroke-width="1"></line>
            <line x1="550" y1="100" x2="550" y2="500" id="Wall-Line-4" stroke="#DEE2E6" stroke-width="1"></line>
            
            <!-- Meeting Room -->
            <rect id="Meeting-Room" fill="#FFFFFF" stroke="#ADB5BD" stroke-width="2" x="200" y="150" width="200" height="150" rx="5"></rect>
            <rect id="Meeting-Table" fill="#6C757D" x="250" y="200" width="100" height="50" rx="5"></rect>
            
            <!-- People in Meeting -->
            <g id="Person-1" transform="translate(240, 190)">
                <circle id="Person-1-Head" fill="url(#gradient-dark)" cx="15" cy="15" r="15"></circle>
                <rect id="Person-1-Body" fill="url(#gradient-dark)" x="10" y="30" width="10" height="20" rx="5"></rect>
            </g>
            
            <g id="Person-2" transform="translate(270, 190)">
                <circle id="Person-2-Head" fill="url(#gradient-orange)" cx="15" cy="15" r="15"></circle>
                <rect id="Person-2-Body" fill="url(#gradient-orange)" x="10" y="30" width="10" height="20" rx="5"></rect>
            </g>
            
            <g id="Person-3" transform="translate(300, 190)">
                <circle id="Person-3-Head" fill="url(#gradient-dark)" cx="15" cy="15" r="15"></circle>
                <rect id="Person-3-Body" fill="url(#gradient-dark)" x="10" y="30" width="10" height="20" rx="5"></rect>
            </g>
            
            <g id="Person-4" transform="translate(330, 190)">
                <circle id="Person-4-Head" fill="url(#gradient-orange)" cx="15" cy="15" r="15"></circle>
                <rect id="Person-4-Body" fill="url(#gradient-orange)" x="10" y="30" width="10" height="20" rx="5"></rect>
            </g>
            
            <g id="Person-5" transform="translate(240, 240)">
                <circle id="Person-5-Head" fill="url(#gradient-orange)" cx="15" cy="15" r="15"></circle>
                <rect id="Person-5-Body" fill="url(#gradient-orange)" x="10" y="30" width="10" height="20" rx="5"></rect>
            </g>
            
            <g id="Person-6" transform="translate(330, 240)">
                <circle id="Person-6-Head" fill="url(#gradient-dark)" cx="15" cy="15" r="15"></circle>
                <rect id="Person-6-Body" fill="url(#gradient-dark)" x="10" y="30" width="10" height="20" rx="5"></rect>
            </g>
            
            <!-- Individual Workspaces -->
            <g id="Workspace-1" transform="translate(450, 150)">
                <rect id="Desk-1" fill="#6C757D" x="10" y="50" width="80" height="30" rx="5"></rect>
                <rect id="Monitor-1" fill="#343A40" x="30" y="20" width="40" height="30" rx="5"></rect>
                <rect id="Screen-1" fill="#F8F9FA" x="32" y="22" width="36" height="26" rx="3"></rect>
                <g id="Person-7" transform="translate(40, 80)">
                    <circle id="Person-7-Head" fill="url(#gradient-orange)" cx="10" cy="10" r="10"></circle>
                    <rect id="Person-7-Body" fill="url(#gradient-orange)" x="5" y="20" width="10" height="20" rx="5"></rect>
                </g>
            </g>
            
            <g id="Workspace-2" transform="translate(550, 150)">
                <rect id="Desk-2" fill="#6C757D" x="10" y="50" width="80" height="30" rx="5"></rect>
                <rect id="Monitor-2" fill="#343A40" x="30" y="20" width="40" height="30" rx="5"></rect>
                <rect id="Screen-2" fill="#F8F9FA" x="32" y="22" width="36" height="26" rx="3"></rect>
                <g id="Person-8" transform="translate(40, 80)">
                    <circle id="Person-8-Head" fill="url(#gradient-dark)" cx="10" cy="10" r="10"></circle>
                    <rect id="Person-8-Body" fill="url(#gradient-dark)" x="5" y="20" width="10" height="20" rx="5"></rect>
                </g>
            </g>
            
            <g id="Workspace-3" transform="translate(450, 250)">
                <rect id="Desk-3" fill="#6C757D" x="10" y="50" width="80" height="30" rx="5"></rect>
                <rect id="Monitor-3" fill="#343A40" x="30" y="20" width="40" height="30" rx="5"></rect>
                <rect id="Screen-3" fill="#F8F9FA" x="32" y="22" width="36" height="26" rx="3"></rect>
                <g id="Person-9" transform="translate(40, 80)">
                    <circle id="Person-9-Head" fill="url(#gradient-dark)" cx="10" cy="10" r="10"></circle>
                    <rect id="Person-9-Body" fill="url(#gradient-dark)" x="5" y="20" width="10" height="20" rx="5"></rect>
                </g>
            </g>
            
            <g id="Workspace-4" transform="translate(550, 250)">
                <rect id="Desk-4" fill="#6C757D" x="10" y="50" width="80" height="30" rx="5"></rect>
                <rect id="Monitor-4" fill="#343A40" x="30" y="20" width="40" height="30" rx="5"></rect>
                <rect id="Screen-4" fill="#F8F9FA" x="32" y="22" width="36" height="26" rx="3"></rect>
                <g id="Person-10" transform="translate(40, 80)">
                    <circle id="Person-10-Head" fill="url(#gradient-orange)" cx="10" cy="10" r="10"></circle>
                    <rect id="Person-10-Body" fill="url(#gradient-orange)" x="5" y="20" width="10" height="20" rx="5"></rect>
                </g>
            </g>
            
            <!-- Lounge Area -->
            <g id="Lounge" transform="translate(200, 350)">
                <rect id="Lounge-Area" fill="#FFFFFF" stroke="#ADB5BD" stroke-width="2" x="0" y="0" width="200" height="100" rx="5"></rect>
                <rect id="Couch-1" fill="url(#gradient-orange)" x="20" y="20" width="60" height="20" rx="5"></rect>
                <rect id="Couch-2" fill="url(#gradient-orange)" x="120" y="20" width="60" height="20" rx="5"></rect>
                <rect id="Coffee-Table" fill="#6C757D" x="85" y="30" width="30" height="30" rx="5"></rect>
                
                <g id="Person-11" transform="translate(30, 20)">
                    <circle id="Person-11-Head" fill="url(#gradient-dark)" cx="10" cy="10" r="10"></circle>
                    <rect id="Person-11-Body" fill="url(#gradient-dark)" x="5" y="20" width="10" height="15" rx="5"></rect>
                </g>
                
                <g id="Person-12" transform="translate(60, 20)">
                    <circle id="Person-12-Head" fill="url(#gradient-orange)" cx="10" cy="10" r="10"></circle>
                    <rect id="Person-12-Body" fill="url(#gradient-orange)" x="5" y="20" width="10" height="15" rx="5"></rect>
                </g>
                
                <g id="Person-13" transform="translate(130, 20)">
                    <circle id="Person-13-Head" fill="url(#gradient-dark)" cx="10" cy="10" r="10"></circle>
                    <rect id="Person-13-Body" fill="url(#gradient-dark)" x="5" y="20" width="10" height="15" rx="5"></rect>
                </g>
                
                <g id="Person-14" transform="translate(160, 20)">
                    <circle id="Person-14-Head" fill="url(#gradient-orange)" cx="10" cy="10" r="10"></circle>
                    <rect id="Person-14-Body" fill="url(#gradient-orange)" x="5" y="20" width="10" height="15" rx="5"></rect>
                </g>
            </g>
            
            <!-- HR Department -->
            <g id="HR-Department" transform="translate(450, 350)">
                <rect id="HR-Area" fill="#FFFFFF" stroke="#ADB5BD" stroke-width="2" x="0" y="0" width="180" height="100" rx="5"></rect>
                <text id="HR-Text" font-family="Arial-BoldMT, Arial" font-size="14" font-weight="bold" fill="#FF6B35">
                    <tspan x="75" y="20">HR</tspan>
                </text>
                
                <rect id="HR-Desk" fill="#6C757D" x="50" y="50" width="80" height="30" rx="5"></rect>
                <g id="HR-Person" transform="translate(80, 80)">
                    <circle id="HR-Person-Head" fill="url(#gradient-orange)" cx="10" cy="10" r="10"></circle>
                    <rect id="HR-Person-Body" fill="url(#gradient-orange)" x="5" y="20" width="10" height="20" rx="5"></rect>
                </g>
                
                <g id="Visitor-1" transform="translate(40, 70)">
                    <circle id="Visitor-1-Head" fill="url(#gradient-dark)" cx="8" cy="8" r="8"></circle>
                    <rect id="Visitor-1-Body" fill="url(#gradient-dark)" x="4" y="16" width="8" height="16" rx="4"></rect>
                </g>
                
                <g id="Visitor-2" transform="translate(120, 70)">
                    <circle id="Visitor-2-Head" fill="url(#gradient-dark)" cx="8" cy="8" r="8"></circle>
                    <rect id="Visitor-2-Body" fill="url(#gradient-dark)" x="4" y="16" width="8" height="16" rx="4"></rect>
                </g>
            </g>
        </g>
        
        <!-- Connecting Lines (Representing Collaboration) -->
        <path d="M300,200 C350,150 400,250 450,200" id="Connection-1" stroke="url(#gradient-orange)" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"></path>
        <path d="M300,250 C350,300 400,200 450,250" id="Connection-2" stroke="url(#gradient-orange)" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"></path>
        <path d="M300,350 C350,400 400,300 450,350" id="Connection-3" stroke="url(#gradient-orange)" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"></path>
        
        <!-- HR Shell Text -->
        <text id="HR-Shell" font-family="Arial-BoldMT, Arial" font-size="36" font-weight="bold" fill="url(#gradient-orange)">
            <tspan x="325" y="530">HR Shell</tspan>
        </text>
        <text id="Tagline" font-family="Arial, Arial" font-size="16" fill="#6C757D" text-anchor="middle">
            <tspan x="400" y="570">Connecting people, streamlining HR processes</tspan>
        </text>
    </g>
</svg>
