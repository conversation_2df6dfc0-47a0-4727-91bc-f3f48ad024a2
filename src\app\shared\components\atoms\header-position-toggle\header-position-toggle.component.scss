@import '../../../../../styles/variables/_colors';

.header-position-toggle {
  position: relative;
  display: inline-block;

  &__dropdown {
    position: relative;
  }

  &__trigger {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--theme-border, $gray-300);
    border-radius: 8px;
    background: var(--theme-surface, $white);
    color: var(--theme-on-surface, $gray-700);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;

    &:hover {
      background: var(--theme-surface-variant, $gray-50);
      border-color: var(--theme-primary, $primary);
    }

    &:focus {
      outline: none;
      border-color: var(--theme-primary, $primary);
      box-shadow: 0 0 0 2px rgba(var(--theme-primary-rgb, 59, 130, 246), 0.1);
    }
  }

  &__icon {
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
  }

  &__label {
    font-size: 0.875rem;
    font-weight: 500;
    flex: 1;
    text-align: left;
  }

  &__chevron {
    font-size: 0.75rem;
    transition: transform 0.2s ease;
  }

  &__menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 0.25rem;
    background: var(--theme-surface, $white);
    border: 1px solid var(--theme-border, $gray-300);
    border-radius: 12px;
    box-shadow: var(--theme-card-shadow, 0 8px 25px -8px rgba(0, 0, 0, 0.15));
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    overflow: hidden;
    min-width: 280px;
  }

  &:hover &__menu,
  &__trigger:focus + &__menu,
  &__menu:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  &:hover &__chevron {
    transform: rotate(180deg);
  }

  &__menu-header {
    padding: 1rem 1rem 0.5rem;
    border-bottom: 1px solid var(--theme-divider, $gray-100);
  }

  &__menu-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--theme-on-surface, $gray-900);
    margin: 0 0 0.25rem;
  }

  &__menu-subtitle {
    font-size: 0.75rem;
    color: var(--theme-on-surface, $gray-600);
    margin: 0;
  }

  &__options {
    padding: 0.5rem;
  }

  &__option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    background: transparent;
    color: var(--theme-on-surface, $gray-700);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;

    &:hover {
      background: var(--theme-surface-variant, $gray-50);
    }

    &:focus {
      outline: none;
      background: var(--theme-surface-variant, $gray-50);
    }

    &--active {
      background: rgba(var(--theme-primary-rgb, 59, 130, 246), 0.1);
      color: var(--theme-primary, $primary);

      .header-position-toggle__option-icon i {
        color: var(--theme-primary, $primary);
      }
    }

    &:not(:last-child) {
      margin-bottom: 0.25rem;
    }
  }

  &__option-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 6px;
    background: var(--theme-surface-variant, $gray-100);
    flex-shrink: 0;

    i {
      font-size: 0.875rem;
      color: var(--theme-on-surface, $gray-600);
    }
  }

  &__option-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
  }

  &__option-label {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.2;
  }

  &__option-description {
    font-size: 0.75rem;
    color: var(--theme-on-surface, $gray-500);
    line-height: 1.3;
  }

  &__option-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;

    i {
      font-size: 0.875rem;
      color: var(--theme-primary, $primary);
    }
  }

  // Compact version
  &--compact {
    .header-position-toggle__button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 8px;
      background: var(--theme-surface, $gray-100);
      color: var(--theme-on-surface, $gray-700);
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        background: var(--theme-surface-variant, $gray-200);
        transform: translateY(-1px);
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px var(--theme-primary, $primary);
      }
    }

    .header-position-toggle__tooltip {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-bottom: 0.5rem;
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      z-index: 1000;
    }

    &:hover .header-position-toggle__tooltip {
      opacity: 1;
      visibility: visible;
    }

    .header-position-toggle__tooltip-content {
      background: var(--theme-on-surface, $gray-900);
      color: var(--theme-surface, $white);
      padding: 0.5rem 0.75rem;
      border-radius: 6px;
      font-size: 0.75rem;
      white-space: nowrap;
      display: flex;
      flex-direction: column;
      gap: 0.125rem;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      &::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: var(--theme-on-surface, $gray-900);
      }

      strong {
        font-weight: 600;
      }

      span {
        opacity: 0.8;
      }
    }
  }
}

// Dark theme specific styles
:host-context(.theme-dark) {
  .header-position-toggle {
    &__trigger {
      background: var(--theme-surface, #{$gray-800});
      border-color: var(--theme-border, #{$gray-600});
      color: var(--theme-on-surface, #{$gray-200});

      &:hover {
        background: var(--theme-surface-variant, #{$gray-700});
      }
    }

    &__menu {
      background: var(--theme-surface, #{$gray-800});
      border-color: var(--theme-border, #{$gray-600});
    }

    &__menu-header {
      border-bottom-color: var(--theme-divider, #{$gray-700});
    }

    &__menu-title {
      color: var(--theme-on-surface, #{$gray-100});
    }

    &__menu-subtitle {
      color: var(--theme-on-surface, #{$gray-400});
    }

    &__option {
      color: var(--theme-on-surface, #{$gray-200});

      &:hover {
        background: var(--theme-surface-variant, #{$gray-700});
      }

      &:focus {
        background: var(--theme-surface-variant, #{$gray-700});
      }
    }

    &__option-icon {
      background: var(--theme-surface-variant, #{$gray-700});

      i {
        color: var(--theme-on-surface, #{$gray-300});
      }
    }

    &__option-description {
      color: var(--theme-on-surface, #{$gray-400});
    }

    &--compact {
      .header-position-toggle__button {
        background: var(--theme-surface, #{$gray-800});
        color: var(--theme-on-surface, #{$gray-200});

        &:hover {
          background: var(--theme-surface-variant, #{$gray-700});
        }
      }

      .header-position-toggle__tooltip-content {
        background: var(--theme-on-surface, #{$gray-100});
        color: var(--theme-surface, #{$gray-900});

        &::after {
          border-top-color: var(--theme-on-surface, #{$gray-100});
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .header-position-toggle {
    &__trigger {
      min-width: 120px;
      padding: 0.5rem;
    }

    &__menu {
      min-width: 260px;
    }
  }
}
