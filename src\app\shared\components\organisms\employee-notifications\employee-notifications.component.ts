import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-notifications',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="notifications-container">
      <div class="notifications-header">
        <h3 class="section-title">Notifications</h3>
        <div class="header-actions">
          <button class="action-btn">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>

      <div class="notifications-content">
        <div class="notification-item">
          <div class="notification-avatar">
            <img src="assets/images/avatars/avatar-4.svg" alt="Leo Martin">
          </div>
          <div class="notification-details">
            <div class="notification-message">
              <span class="user-name">Leo <PERSON></span> requested access to <span class="highlight">project resources</span>
            </div>
            <div class="notification-time">Today at 10:23 AM</div>
          </div>
          <div class="notification-actions">
            <button class="action-btn accept">
              <i class="fa fa-check"></i>
            </button>
            <button class="action-btn reject">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>

        <div class="notification-item">
          <div class="notification-avatar">
            <img src="assets/images/avatars/avatar-4.svg" alt="Leo Martin">
          </div>
          <div class="notification-details">
            <div class="notification-message">
              <span class="user-name">Leo Martin</span> requested access to <span class="highlight">server information</span>
            </div>
            <div class="notification-time">Today at 09:23 PM</div>
          </div>
          <div class="notification-actions">
            <button class="action-btn accept">
              <i class="fa fa-check"></i>
            </button>
            <button class="action-btn reject">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>

        <div class="notification-item">
          <div class="notification-avatar warning">
            <i class="fa fa-exclamation-triangle"></i>
          </div>
          <div class="notification-details">
            <div class="notification-message">
              Your password will expire in <span class="highlight">7 days</span>
            </div>
            <div class="notification-time">Today at 11:45 AM</div>
          </div>
          <div class="notification-actions">
            <button class="action-btn view">
              <span>Update</span>
            </button>
            <button class="action-btn dismiss">
              <span>Dismiss</span>
            </button>
          </div>
        </div>

        <div class="notification-item">
          <div class="notification-avatar">
            <img src="assets/images/avatars/avatar-4.svg" alt="Leo Martin">
          </div>
          <div class="notification-details">
            <div class="notification-message">
              <span class="user-name">Leo Martin</span> requested access to <span class="highlight">project resources</span>
            </div>
            <div class="notification-time">Today at 08:42 PM</div>
          </div>
          <div class="notification-actions">
            <button class="action-btn accept">
              <i class="fa fa-check"></i>
            </button>
            <button class="action-btn reject">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>

        <div class="notification-item">
          <div class="notification-avatar">
            <img src="assets/images/avatars/avatar-4.svg" alt="Leo Martin">
          </div>
          <div class="notification-details">
            <div class="notification-message">
              <span class="user-name">Leo Martin</span> requested access to <span class="highlight">project resources</span>
            </div>
            <div class="notification-time">Today at 06:32 PM</div>
          </div>
          <div class="notification-actions">
            <button class="action-btn accept">
              <i class="fa fa-check"></i>
            </button>
            <button class="action-btn reject">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .notifications-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .notifications-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e9ecef;
    }

    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }

    .action-btn {
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      font-size: 16px;
    }

    .notifications-content {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      overflow-y: auto;
    }

    .notification-item {
      display: flex;
      align-items: flex-start;
      padding: 12px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
    }

    .notification-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 12px;
      background-color: #e9ecef;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .notification-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .notification-avatar.warning {
      background-color: #fff3cd;
      color: #ffc107;
      font-size: 18px;
    }

    .notification-details {
      flex: 1;
      min-width: 0;
    }

    .notification-message {
      font-size: 14px;
      color: #343a40;
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .user-name {
      font-weight: 600;
    }

    .highlight {
      font-weight: 600;
      color: #ff6b35;
    }

    .notification-time {
      font-size: 12px;
      color: #adb5bd;
    }

    .notification-actions {
      display: flex;
      gap: 8px;
      margin-left: 12px;
    }

    .action-btn.accept {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #e8f5e9;
      color: #4caf50;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .action-btn.reject {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #ffebee;
      color: #f44336;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .action-btn.view, .action-btn.dismiss {
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }

    .action-btn.view {
      background-color: #e8f5e9;
      color: #4caf50;
    }

    .action-btn.dismiss {
      background-color: #f8f9fa;
      color: #6c757d;
    }
  `]
})
export class EmployeeNotificationsComponent {
  @Input() employee: any;
}
