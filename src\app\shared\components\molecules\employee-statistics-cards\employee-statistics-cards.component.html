<div class="employee-statistics">
  <div class="statistics-grid">
    <!-- Total Employees Card -->
    <div class="stat-card stat-card--total">
      <div class="stat-card__icon">
        <i class="fa fa-users"></i>
      </div>
      <div class="stat-card__content">
        <div class="stat-card__label">Total Employee</div>
        <div class="stat-card__value" *ngIf="!isLoading">{{ statistics.totalEmployees }}</div>
        <div class="stat-card__value stat-card__value--loading" *ngIf="isLoading">
          <i class="fa fa-spinner fa-spin"></i>
        </div>
      </div>
      <div class="stat-card__trend">
        <span class="trend-indicator trend-indicator--positive">
          <i class="fa fa-arrow-up"></i>
          +19.01%
        </span>
      </div>
    </div>

    <!-- Active Employees Card -->
    <div class="stat-card stat-card--active">
      <div class="stat-card__icon">
        <i class="fa fa-user-check"></i>
      </div>
      <div class="stat-card__content">
        <div class="stat-card__label">Active</div>
        <div class="stat-card__value" *ngIf="!isLoading">{{ statistics.activeEmployees }}</div>
        <div class="stat-card__value stat-card__value--loading" *ngIf="isLoading">
          <i class="fa fa-spinner fa-spin"></i>
        </div>
      </div>
      <div class="stat-card__trend">
        <span class="trend-indicator trend-indicator--positive">
          <i class="fa fa-arrow-up"></i>
          +19.01%
        </span>
      </div>
    </div>

    <!-- Inactive Employees Card -->
    <div class="stat-card stat-card--inactive">
      <div class="stat-card__icon">
        <i class="fa fa-user-times"></i>
      </div>
      <div class="stat-card__content">
        <div class="stat-card__label">InActive</div>
        <div class="stat-card__value" *ngIf="!isLoading">{{ statistics.onLeaveEmployees }}</div>
        <div class="stat-card__value stat-card__value--loading" *ngIf="isLoading">
          <i class="fa fa-spinner fa-spin"></i>
        </div>
      </div>
      <div class="stat-card__trend">
        <span class="trend-indicator trend-indicator--positive">
          <i class="fa fa-arrow-up"></i>
          +19.01%
        </span>
      </div>
    </div>

    <!-- New Joiners Card -->
    <div class="stat-card stat-card--new-joiners">
      <div class="stat-card__icon">
        <i class="fa fa-user-plus"></i>
      </div>
      <div class="stat-card__content">
        <div class="stat-card__label">New Joiners</div>
        <div class="stat-card__value" *ngIf="!isLoading">{{ statistics.newJoiners || 67 }}</div>
        <div class="stat-card__value stat-card__value--loading" *ngIf="isLoading">
          <i class="fa fa-spinner fa-spin"></i>
        </div>
      </div>
      <div class="stat-card__trend">
        <span class="trend-indicator trend-indicator--positive">
          <i class="fa fa-arrow-up"></i>
          +19.01%
        </span>
      </div>
    </div>
  </div>
</div>
