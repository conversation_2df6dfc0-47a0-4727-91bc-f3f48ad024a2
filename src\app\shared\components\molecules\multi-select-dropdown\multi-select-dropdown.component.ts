import { Component, Input, Output, EventEmitter, forwardRef, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NG_VALUE_ACCESSOR, ControlValueAccessor, ReactiveFormsModule, FormControl } from '@angular/forms';
import { IconComponent } from '../../atoms/icon/icon.component';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

export interface DropdownOption {
  value: string | number;
  label: string;
  icon?: string;
}

@Component({
  selector: 'app-multi-select-dropdown',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, IconComponent],
  templateUrl: './multi-select-dropdown.component.html',
  styleUrl: './multi-select-dropdown.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => MultiSelectDropdownComponent),
      multi: true
    }
  ]
})
export class MultiSelectDropdownComponent implements ControlValueAccessor, OnInit, OnDestroy {
  @Input() options: DropdownOption[] = [];
  @Input() placeholder: string = 'Select options';
  @Input() searchPlaceholder: string = 'Search...';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() invalid: boolean = false;
  @Input() showSearch: boolean = true;
  @Input() value: (string | number)[] = [];
  @Input() enableSelectAllLogic: boolean = true; // Enable/disable "All Departments" logic

  @Output() selectionChange = new EventEmitter<(string | number)[]>();
  @Output() searchChange = new EventEmitter<string>();

  isOpen: boolean = false;
  searchControl = new FormControl('');
  filteredOptions: DropdownOption[] = [];
  selectedValues: (string | number)[] = [];

  private destroy$ = new Subject<void>();

  // ControlValueAccessor implementation
  private onChange: (value: (string | number)[]) => void = () => {};
  private onTouched: () => void = () => {};

  ngOnInit(): void {
    this.filteredOptions = [...this.options];

    // Set initial selected values from the value input
    if (this.value && this.value.length > 0) {
      this.selectedValues = [...this.value];
    }

    // Set up search filtering
    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroy$),
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.filterOptions(searchTerm || '');
      this.searchChange.emit(searchTerm || '');
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Filter options based on search term
  filterOptions(searchTerm: string): void {
    if (!searchTerm.trim()) {
      this.filteredOptions = [...this.options];
      return;
    }

    const term = searchTerm.toLowerCase().trim();
    this.filteredOptions = this.options.filter(option =>
      option.label.toLowerCase().includes(term)
    );
  }

  // Get the label for the selected values
  get selectedLabel(): string {
    if (this.selectedValues.length === 0) {
      return this.placeholder;
    } else if (this.enableSelectAllLogic && this.selectedValues.includes('')) {
      // If "All Departments" is selected, show that regardless of other selections
      return 'All Departments';
    } else if (this.selectedValues.length === 1) {
      const selected = this.options.find(option => option.value === this.selectedValues[0]);
      return selected ? selected.label : this.placeholder;
    } else {
      return `${this.selectedValues.length} items selected`;
    }
  }

  // Toggle dropdown
  toggleDropdown(event: Event): void {
    if (this.disabled) return;

    event.stopPropagation();
    this.isOpen = !this.isOpen;

    if (this.isOpen) {
      // Reset search when opening
      this.searchControl.setValue('');
      this.filteredOptions = [...this.options];

      // Add click outside listener
      setTimeout(() => {
        document.addEventListener('click', this.closeDropdown);
      });
    }

    this.onTouched();
  }

  // Close dropdown
  closeDropdown = (): void => {
    this.isOpen = false;
    document.removeEventListener('click', this.closeDropdown);
  }

  // Check if an option is selected
  isSelected(value: string | number): boolean {
    return this.selectedValues.includes(value);
  }

  // Toggle option selection
  toggleOption(option: DropdownOption, event: Event): void {
    event.stopPropagation();

    // Special handling for "All Departments" option (empty string value) - only if enabled
    if (this.enableSelectAllLogic && option.value === '') {
      const isCurrentlySelected = this.selectedValues.includes('');

      if (isCurrentlySelected) {
        // If "All Departments" is currently selected, deselect everything
        this.selectedValues = [];
      } else {
        // If "All Departments" is not selected, select all options (including "All Departments")
        this.selectedValues = this.options.map(opt => opt.value);
      }

      this.onChange(this.selectedValues);
      this.selectionChange.emit(this.selectedValues);
      return;
    }

    // For regular options
    const index = this.selectedValues.indexOf(option.value);
    let newSelectedValues = [...this.selectedValues];

    if (index === -1) {
      // Add to selection
      newSelectedValues.push(option.value);
    } else {
      // Remove from selection
      newSelectedValues = newSelectedValues.filter(value => value !== option.value);
    }

    // Only apply "All Departments" logic if enabled
    if (this.enableSelectAllLogic) {
      // Check if all individual options are now selected
      const allIndividualOptions = this.options.filter(opt => opt.value !== '');
      const allIndividualSelected = allIndividualOptions.every(opt =>
        newSelectedValues.includes(opt.value)
      );

      // If all individual options are selected, also select "All Departments"
      if (allIndividualSelected && !newSelectedValues.includes('')) {
        newSelectedValues.unshift(''); // Add "All Departments" at the beginning
      }
      // If not all options are selected, remove "All Departments"
      else if (!allIndividualSelected && newSelectedValues.includes('')) {
        newSelectedValues = newSelectedValues.filter(value => value !== '');
      }
    }

    this.selectedValues = newSelectedValues;
    this.onChange(this.selectedValues);
    this.selectionChange.emit(this.selectedValues);
  }

  // Select all options
  selectAll(event: Event): void {
    event.stopPropagation();

    // Select all options including "All Departments"
    this.selectedValues = this.options.map(option => option.value);

    this.onChange(this.selectedValues);
    this.selectionChange.emit(this.selectedValues);
  }

  // Clear all selections
  clearAll(event: Event): void {
    event.stopPropagation();
    // Clear all selections - don't auto-select "All Departments"
    this.selectedValues = [];
    this.onChange(this.selectedValues);
    this.selectionChange.emit(this.selectedValues);
  }

  // ControlValueAccessor methods
  writeValue(value: (string | number)[]): void {
    this.selectedValues = value || [];
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
