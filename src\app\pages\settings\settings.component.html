<div class="settings-page">
  <div class="settings-header">
    <h1 class="settings-title">Settings</h1>
    <p class="settings-subtitle">Manage your account settings and preferences</p>
  </div>
  
  <div class="settings-container">
    <div class="settings-sidebar">
      <ul class="settings-nav">
        <li class="settings-nav-item" [class.active]="activeTab === 'account'">
          <button class="settings-nav-link" (click)="setActiveTab('account')">
            <i class="fa fa-user"></i>
            <span>Account</span>
          </button>
        </li>
        <li class="settings-nav-item" [class.active]="activeTab === 'password'">
          <button class="settings-nav-link" (click)="setActiveTab('password')">
            <i class="fa fa-lock"></i>
            <span>Password</span>
          </button>
        </li>
        <li class="settings-nav-item" [class.active]="activeTab === 'notifications'">
          <button class="settings-nav-link" (click)="setActiveTab('notifications')">
            <i class="fa fa-bell"></i>
            <span>Notifications</span>
          </button>
        </li>
        <li class="settings-nav-item" [class.active]="activeTab === 'appearance'">
          <button class="settings-nav-link" (click)="setActiveTab('appearance')">
            <i class="fa fa-palette"></i>
            <span>Appearance</span>
          </button>
        </li>
      </ul>
      
      <div class="settings-nav-footer">
        <button class="logout-button" (click)="logout()">
          <i class="fa fa-sign-out-alt"></i>
          <span>Logout</span>
        </button>
      </div>
    </div>
    
    <div class="settings-content">
      <!-- Success Message -->
      @if (successMessage) {
        <div class="alert alert-success">
          <i class="fa fa-check-circle"></i>
          {{ successMessage }}
          <button class="alert-close" (click)="clearMessages()">×</button>
        </div>
      }
      
      <!-- Error Message -->
      @if (errorMessage) {
        <div class="alert alert-error">
          <i class="fa fa-exclamation-circle"></i>
          {{ errorMessage }}
          <button class="alert-close" (click)="clearMessages()">×</button>
        </div>
      }
      
      <!-- Account Settings -->
      @if (activeTab === 'account') {
        <div class="settings-section">
          <h2 class="settings-section-title">Account Information</h2>
          
          <div class="account-info">
            <div class="account-avatar">
              <img src="assets/images/user-avatar.jpg" alt="User Avatar">
              <button class="avatar-upload">
                <i class="fa fa-camera"></i>
              </button>
            </div>
            
            <div class="account-details">
              <div class="account-name">{{ user()?.first_name }} {{ user()?.last_name }}</div>
              <div class="account-email">{{ user()?.email }}</div>
              @if (isAdmin()) {
                <div class="account-badge">Admin</div>
              }
            </div>
          </div>
          
          <div class="account-actions">
            <a routerLink="/app/profile" class="btn btn-primary">
              <i class="fa fa-edit"></i> Edit Profile
            </a>
            <button class="btn btn-danger">
              <i class="fa fa-trash"></i> Delete Account
            </button>
          </div>
        </div>
      }
      
      <!-- Password Settings -->
      @if (activeTab === 'password') {
        <div class="settings-section">
          <h2 class="settings-section-title">Change Password</h2>
          
          <form [formGroup]="passwordForm" (ngSubmit)="savePassword()" class="settings-form">
            <div class="form-group">
              <label for="currentPassword">Current Password</label>
              <div class="password-input">
                <input type="password" id="currentPassword" formControlName="currentPassword">
                <div class="form-error" *ngIf="passwordForm.get('currentPassword')?.invalid && passwordForm.get('currentPassword')?.touched">
                  Current password is required
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label for="newPassword">New Password</label>
              <div class="password-input">
                <input type="password" id="newPassword" formControlName="newPassword">
                <div class="form-error" *ngIf="passwordForm.get('newPassword')?.invalid && passwordForm.get('newPassword')?.touched">
                  Password must be at least 8 characters
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label for="confirmPassword">Confirm New Password</label>
              <div class="password-input">
                <input type="password" id="confirmPassword" formControlName="confirmPassword">
                <div class="form-error" *ngIf="passwordForm.get('confirmPassword')?.errors?.['passwordMismatch'] && passwordForm.get('confirmPassword')?.touched">
                  Passwords do not match
                </div>
              </div>
            </div>
            
            <div class="form-actions">
              <button type="submit" class="btn btn-primary" [disabled]="passwordForm.invalid || isSaving">
                <i class="fa" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
                {{ isSaving ? 'Saving...' : 'Update Password' }}
              </button>
            </div>
          </form>
        </div>
      }
      
      <!-- Notification Settings -->
      @if (activeTab === 'notifications') {
        <div class="settings-section">
          <h2 class="settings-section-title">Notification Preferences</h2>
          
          <form [formGroup]="notificationForm" (ngSubmit)="saveNotificationSettings()" class="settings-form">
            <div class="form-group checkbox-group">
              <div class="checkbox-control">
                <input type="checkbox" id="emailNotifications" formControlName="emailNotifications">
                <label for="emailNotifications">Email Notifications</label>
                <p class="checkbox-description">Receive notifications via email</p>
              </div>
            </div>
            
            <div class="form-group checkbox-group">
              <div class="checkbox-control">
                <input type="checkbox" id="pushNotifications" formControlName="pushNotifications">
                <label for="pushNotifications">Push Notifications</label>
                <p class="checkbox-description">Receive notifications in the browser</p>
              </div>
            </div>
            
            <div class="form-group checkbox-group">
              <div class="checkbox-control">
                <input type="checkbox" id="emailUpdates" formControlName="emailUpdates">
                <label for="emailUpdates">Email Updates</label>
                <p class="checkbox-description">Receive updates about system changes</p>
              </div>
            </div>
            
            <div class="form-group checkbox-group">
              <div class="checkbox-control">
                <input type="checkbox" id="emailMarketing" formControlName="emailMarketing">
                <label for="emailMarketing">Marketing Emails</label>
                <p class="checkbox-description">Receive marketing and promotional emails</p>
              </div>
            </div>
            
            <div class="form-group checkbox-group">
              <div class="checkbox-control">
                <input type="checkbox" id="smsNotifications" formControlName="smsNotifications">
                <label for="smsNotifications">SMS Notifications</label>
                <p class="checkbox-description">Receive notifications via SMS</p>
              </div>
            </div>
            
            <div class="form-actions">
              <button type="submit" class="btn btn-primary" [disabled]="isSaving">
                <i class="fa" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
                {{ isSaving ? 'Saving...' : 'Save Preferences' }}
              </button>
            </div>
          </form>
        </div>
      }
      
      <!-- Appearance Settings -->
      @if (activeTab === 'appearance') {
        <div class="settings-section">
          <h2 class="settings-section-title">Appearance Settings</h2>
          
          <form [formGroup]="appearanceForm" (ngSubmit)="saveAppearanceSettings()" class="settings-form">
            <div class="form-group">
              <label>Theme</label>
              <div class="radio-group">
                <div class="radio-control">
                  <input type="radio" id="lightTheme" formControlName="theme" value="light">
                  <label for="lightTheme">Light</label>
                </div>
                <div class="radio-control">
                  <input type="radio" id="darkTheme" formControlName="theme" value="dark">
                  <label for="darkTheme">Dark</label>
                </div>
                <div class="radio-control">
                  <input type="radio" id="systemTheme" formControlName="theme" value="system">
                  <label for="systemTheme">System Default</label>
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label>Font Size</label>
              <div class="radio-group">
                <div class="radio-control">
                  <input type="radio" id="smallFont" formControlName="fontSize" value="small">
                  <label for="smallFont">Small</label>
                </div>
                <div class="radio-control">
                  <input type="radio" id="mediumFont" formControlName="fontSize" value="medium">
                  <label for="mediumFont">Medium</label>
                </div>
                <div class="radio-control">
                  <input type="radio" id="largeFont" formControlName="fontSize" value="large">
                  <label for="largeFont">Large</label>
                </div>
              </div>
            </div>
            
            <div class="form-group checkbox-group">
              <div class="checkbox-control">
                <input type="checkbox" id="compactMode" formControlName="compactMode">
                <label for="compactMode">Compact Mode</label>
                <p class="checkbox-description">Reduce spacing in the interface</p>
              </div>
            </div>
            
            <div class="form-group checkbox-group">
              <div class="checkbox-control">
                <input type="checkbox" id="animationsEnabled" formControlName="animationsEnabled">
                <label for="animationsEnabled">Enable Animations</label>
                <p class="checkbox-description">Show animations in the interface</p>
              </div>
            </div>
            
            <div class="form-actions">
              <button type="submit" class="btn btn-primary" [disabled]="isSaving">
                <i class="fa" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
                {{ isSaving ? 'Saving...' : 'Save Settings' }}
              </button>
            </div>
          </form>
        </div>
      }
    </div>
  </div>
</div>
