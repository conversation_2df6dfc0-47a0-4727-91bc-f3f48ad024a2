<header class="header">
  <div class="header__left">
    <button class="menu__toggle" (click)="toggleSideMenu()" title="{{ menuCollapsed ? 'Expand menu' : 'Collapse menu' }}">
      <i class="fa" [ngClass]="menuCollapsed ? 'fa-angle-right' : 'fa-angle-left'"></i>
    </button>
    <div class="search__bar">
      <input type="text" placeholder="Search in HRMS" class="search__input">
      <button class="search__button">
        <i class="fa fa-search"></i>
      </button>
    </div>
  </div>
  <div class="header__actions">
    <!-- Theme Toggle -->
    <div class="header__action-item">
      <app-theme-toggle></app-theme-toggle>
    </div>

    <!-- Header Position Toggle -->
    <div class="header__action-item header__action-item--settings">
      <app-header-position-toggle class="header-position-toggle--compact"></app-header-position-toggle>
    </div>

    <button class="action__button">
      <i class="fa fa-expand"></i>
    </button>
    <button class="action__button">
      <i class="fa fa-th-large"></i>
    </button>
    <button class="action__button action__button--notification">
      <i class="fa fa-bell"></i>
      <span class="notification__badge">3</span>
    </button>
    <button class="action__button">
      <i class="fa fa-envelope"></i>
    </button>
    <div class="user__profile" (click)="toggleUserMenu()">
      <img src="assets/images/user-avatar.jpg" [alt]="fullName()" class="user__avatar">
      <div class="user__info" *ngIf="!menuCollapsed">
        <span class="user__name">{{ fullName() }}</span>
        <span class="user__role" *ngIf="isAdmin()">Admin</span>
      </div>

      <!-- User dropdown menu -->
      <div class="user__menu" *ngIf="userMenuOpen">
        <div class="user__menu-header">
          <span class="user__menu-name">{{ fullName() }}</span>
          <span class="user__menu-email">{{ user()?.email }}</span>
        </div>
        <ul class="user__menu-list">
          <li class="user__menu-item">
            <a routerLink="/app/profile" class="user__menu-link">
              <i class="fa fa-user"></i> My Profile
            </a>
          </li>
          <li class="user__menu-item">
            <a routerLink="/app/settings" class="user__menu-link">
              <i class="fa fa-cog"></i> Settings
            </a>
          </li>
          <li class="user__menu-item user__menu-item--theme">
            <div class="user__menu-theme-section">
              <span class="user__menu-section-title">
                <i class="fa fa-palette"></i> Appearance
              </span>
              <div class="user__menu-theme-controls">
                <app-theme-toggle class="theme-toggle--dropdown"></app-theme-toggle>
              </div>
            </div>
          </li>
          <li class="user__menu-item user__menu-item--layout">
            <div class="user__menu-layout-section">
              <span class="user__menu-section-title">
                <i class="fa fa-layout"></i> Layout
              </span>
              <div class="user__menu-layout-controls">
                <app-header-position-toggle></app-header-position-toggle>
              </div>
            </div>
          </li>
          <li class="user__menu-divider"></li>
          <li class="user__menu-item">
            <button class="user__menu-button" (click)="logout()">
              <i class="fa fa-sign-out-alt"></i> Logout
            </button>
          </li>
        </ul>
      </div>
    </div>
  </div>
</header>
