<div
  class="file-uploader"
  [class.file-uploader--has-file]="previewUrl"
  [class.file-uploader--dragging]="isDragging"
  (dragover)="onDragOver($event)"
  (dragleave)="onDragLeave($event)"
  (drop)="onDrop($event)">

  <div class="file-uploader__content" (click)="triggerFileInput($event)">
    <div class="file-uploader__preview" *ngIf="previewUrl">
      <img [src]="previewUrl" alt="Preview" class="file-uploader__preview-image">
      <button type="button" class="file-uploader__remove-btn" (click)="removeFile($event)">
        <app-icon name="fa-times" size="sm"></app-icon>
      </button>
    </div>

    <div class="file-uploader__placeholder" *ngIf="!previewUrl">
      <div class="file-uploader__icon">
        <app-icon name="fa-cloud-upload-alt" size="lg"></app-icon>
      </div>
      <div class="file-uploader__text">
        <p class="file-uploader__primary-text">{{ placeholder }}</p>
        <p class="file-uploader__secondary-text">
          Drag & drop or
          <span class="file-uploader__browse-text">browse</span>
        </p>
        <p class="file-uploader__file-types">{{ acceptedFileTypes }}</p>
      </div>
    </div>
  </div>

  <input
    #fileInput
    type="file"
    class="file-uploader__input"
    [accept]="accept"
    (change)="onFileSelected($event)"
  >
</div>
