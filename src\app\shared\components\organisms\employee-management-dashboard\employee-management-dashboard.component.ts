import { Component, Input, OnInit, OnDestroy, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';

// Import atomic and molecular components
import { ButtonComponent } from '../../atoms/button/button.component';
import { DataTableContainerComponent } from '../../molecules/data-table-container/data-table-container.component';

// Import stores and interfaces
import { EmployeeManagementStore } from '../../../../core/state/employee-management/employee-management.state';
import {
  EmployeeSalary,
  EmployeeHike,
  EmployeeOKR,
  EmployeePerformance,
  EmployeeLeave,
  EmployeeAttendance,
  EmployeeTraining,
  EmployeeAsset
} from '../../../../core/services/employee-management.service';

export type ManagementSection = 'salary' | 'hike' | 'okr' | 'performance' | 'leave' | 'attendance' | 'training' | 'asset' | 'overview';

@Component({
  selector: 'app-employee-management-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    DataTableContainerComponent
  ],
  templateUrl: './employee-management-dashboard.component.html',
  styleUrls: ['./employee-management-dashboard.component.scss']
})
export class EmployeeManagementDashboardComponent implements OnInit, OnDestroy {
  @Input() selectedEmployeeId: number | null = null;
  @Input() initialSection: ManagementSection = 'overview';

  private destroy$ = new Subject<void>();
  private employeeManagementStore = inject(EmployeeManagementStore);

  // Current active section
  activeSection: ManagementSection = 'overview';

  // Section configurations
  sections = [
    { key: 'overview', label: 'Overview', icon: 'dashboard' },
    { key: 'salary', label: 'Salary', icon: 'attach_money' },
    { key: 'hike', label: 'Hikes', icon: 'trending_up' },
    { key: 'okr', label: 'OKRs', icon: 'flag' },
    { key: 'performance', label: 'Performance', icon: 'assessment' },
    { key: 'leave', label: 'Leaves', icon: 'event_busy' },
    { key: 'attendance', label: 'Attendance', icon: 'schedule' },
    { key: 'training', label: 'Training', icon: 'school' },
    { key: 'asset', label: 'Assets', icon: 'devices' }
  ] as const;

  // Store signals
  salaries = this.employeeManagementStore.salaries;
  salariesLoading = this.employeeManagementStore.salariesLoading;
  salariesError = this.employeeManagementStore.salariesError;

  hikes = this.employeeManagementStore.hikes;
  hikesLoading = this.employeeManagementStore.hikesLoading;
  hikesError = this.employeeManagementStore.hikesError;
  pendingHikes = this.employeeManagementStore.pendingHikes;

  okrs = this.employeeManagementStore.okrs;
  okrsLoading = this.employeeManagementStore.okrsLoading;
  okrsError = this.employeeManagementStore.okrsError;
  activeOKRs = this.employeeManagementStore.activeOKRs;

  leaves = this.employeeManagementStore.leaves;
  leavesLoading = this.employeeManagementStore.leavesLoading;
  leavesError = this.employeeManagementStore.leavesError;
  pendingLeaves = this.employeeManagementStore.pendingLeaves;

  attendance = this.employeeManagementStore.attendance;
  attendanceLoading = this.employeeManagementStore.attendanceLoading;
  attendanceError = this.employeeManagementStore.attendanceError;

  trainings = this.employeeManagementStore.trainings;
  trainingsLoading = this.employeeManagementStore.trainingsLoading;
  trainingsError = this.employeeManagementStore.trainingsError;

  assets = this.employeeManagementStore.assets;
  assetsLoading = this.employeeManagementStore.assetsLoading;
  assetsError = this.employeeManagementStore.assetsError;
  assignedAssets = this.employeeManagementStore.assignedAssets;

  statistics = this.employeeManagementStore.statistics;
  statisticsLoading = this.employeeManagementStore.statisticsLoading;

  constructor() {
    // Effect to watch for employee ID changes
    effect(() => {
      const employeeId = this.selectedEmployeeId;
      if (employeeId) {
        this.employeeManagementStore.setSelectedEmployeeId(employeeId);
        this.loadDataForActiveSection();
      }
    });
  }

  ngOnInit(): void {
    this.activeSection = this.initialSection;

    if (this.selectedEmployeeId) {
      this.employeeManagementStore.setSelectedEmployeeId(this.selectedEmployeeId);
    }

    this.loadDataForActiveSection();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Section navigation
  setActiveSection(section: ManagementSection): void {
    this.activeSection = section;
    this.loadDataForActiveSection();
  }

  // Load data based on active section
  private loadDataForActiveSection(): void {
    const employeeId = this.selectedEmployeeId;

    switch (this.activeSection) {
      case 'overview':
        this.loadOverviewData(employeeId);
        break;
      case 'salary':
        this.employeeManagementStore.loadSalaries({ employeeId: employeeId || undefined });
        break;
      case 'hike':
        this.employeeManagementStore.loadHikes({ employeeId: employeeId || undefined });
        break;
      case 'okr':
        this.employeeManagementStore.loadOKRs({ employeeId: employeeId || undefined });
        break;
      case 'leave':
        this.employeeManagementStore.loadLeaves({ employeeId: employeeId || undefined });
        break;
      case 'attendance':
        this.employeeManagementStore.loadAttendance({ employeeId: employeeId || undefined });
        break;
      case 'training':
        this.employeeManagementStore.loadTrainings({ employeeId: employeeId || undefined });
        break;
      case 'asset':
        this.employeeManagementStore.loadAssets({ employeeId: employeeId || undefined });
        break;
    }
  }

  private loadOverviewData(employeeId: number | null): void {
    if (!employeeId) {
      this.employeeManagementStore.loadStatistics();
    } else {
      // Load summary data for specific employee
      this.employeeManagementStore.loadSalaries({ employeeId, pageSize: 5 });
      this.employeeManagementStore.loadHikes({ employeeId, pageSize: 5 });
      this.employeeManagementStore.loadOKRs({ employeeId, pageSize: 5 });
      this.employeeManagementStore.loadLeaves({ employeeId, pageSize: 5 });
    }
  }

  // Table configurations for different sections
  getSalaryTableConfig() {
    return {
      columns: [
        { key: 'basic_salary', label: 'Basic Salary', type: 'text' as const },
        { key: 'allowances', label: 'Allowances', type: 'text' as const },
        { key: 'deductions', label: 'Deductions', type: 'text' as const },
        { key: 'gross_salary', label: 'Gross Salary', type: 'text' as const },
        { key: 'net_salary', label: 'Net Salary', type: 'text' as const },
        { key: 'effective_date', label: 'Effective Date', type: 'date' as const }
      ],
      actions: [
        { key: 'edit', label: 'Edit', icon: 'edit', color: 'primary' },
        { key: 'delete', label: 'Delete', icon: 'delete', color: 'warn' }
      ]
    };
  }

  getHikeTableConfig() {
    return {
      columns: [
        { key: 'previous_salary', label: 'Previous Salary', type: 'text' as const },
        { key: 'new_salary', label: 'New Salary', type: 'text' as const },
        { key: 'hike_percentage', label: 'Hike %', type: 'text' as const },
        { key: 'hike_amount', label: 'Hike Amount', type: 'text' as const },
        { key: 'effective_date', label: 'Effective Date', type: 'date' as const },
        { key: 'status', label: 'Status', type: 'badge' as const },
        { key: 'reason', label: 'Reason', type: 'text' as const }
      ],
      actions: [
        { key: 'approve', label: 'Approve', icon: 'check', color: 'primary', condition: (item: EmployeeHike) => item.status === 'pending' },
        { key: 'reject', label: 'Reject', icon: 'close', color: 'warn', condition: (item: EmployeeHike) => item.status === 'pending' },
        { key: 'edit', label: 'Edit', icon: 'edit', color: 'primary', condition: (item: EmployeeHike) => item.status === 'pending' }
      ]
    };
  }

  getOKRTableConfig() {
    return {
      columns: [
        { key: 'title', label: 'Title', type: 'text' as const },
        { key: 'quarter', label: 'Quarter', type: 'text' as const },
        { key: 'year', label: 'Year', type: 'text' as const },
        { key: 'status', label: 'Status', type: 'badge' as const },
        { key: 'progress', label: 'Progress', type: 'text' as const },
        { key: 'score', label: 'Score', type: 'text' as const }
      ],
      actions: [
        { key: 'view', label: 'View', icon: 'visibility', color: 'primary' },
        { key: 'edit', label: 'Edit', icon: 'edit', color: 'primary' },
        { key: 'update-progress', label: 'Update Progress', icon: 'trending_up', color: 'accent' }
      ]
    };
  }

  getLeaveTableConfig() {
    return {
      columns: [
        { key: 'leave_type', label: 'Leave Type', type: 'text' as const },
        { key: 'start_date', label: 'Start Date', type: 'date' as const },
        { key: 'end_date', label: 'End Date', type: 'date' as const },
        { key: 'days_requested', label: 'Days', type: 'text' as const },
        { key: 'reason', label: 'Reason', type: 'text' as const },
        { key: 'status', label: 'Status', type: 'badge' as const }
      ],
      actions: [
        { key: 'approve', label: 'Approve', icon: 'check', color: 'primary', condition: (item: EmployeeLeave) => item.status === 'pending' },
        { key: 'reject', label: 'Reject', icon: 'close', color: 'warn', condition: (item: EmployeeLeave) => item.status === 'pending' },
        { key: 'view', label: 'View', icon: 'visibility', color: 'primary' }
      ]
    };
  }

  getAttendanceTableConfig() {
    return {
      columns: [
        { key: 'date', label: 'Date', type: 'date' as const },
        { key: 'check_in_time', label: 'Check In', type: 'text' as const },
        { key: 'check_out_time', label: 'Check Out', type: 'text' as const },
        { key: 'total_hours', label: 'Total Hours', type: 'text' as const },
        { key: 'status', label: 'Status', type: 'badge' as const }
      ],
      actions: [
        { key: 'edit', label: 'Edit', icon: 'edit', color: 'primary' },
        { key: 'view', label: 'View', icon: 'visibility', color: 'primary' }
      ]
    };
  }

  getTrainingTableConfig() {
    return {
      columns: [
        { key: 'training_name', label: 'Training Name', type: 'text' as const },
        { key: 'training_type', label: 'Type', type: 'text' as const },
        { key: 'start_date', label: 'Start Date', type: 'date' as const },
        { key: 'end_date', label: 'End Date', type: 'date' as const },
        { key: 'status', label: 'Status', type: 'badge' as const },
        { key: 'completion_percentage', label: 'Progress', type: 'text' as const }
      ],
      actions: [
        { key: 'view', label: 'View', icon: 'visibility', color: 'primary' },
        { key: 'certificate', label: 'Certificate', icon: 'file_download', color: 'accent', condition: (item: EmployeeTraining) => item.status === 'completed' && item.certificate_url }
      ]
    };
  }

  getAssetTableConfig() {
    return {
      columns: [
        { key: 'asset_name', label: 'Asset Name', type: 'text' as const },
        { key: 'asset_type', label: 'Type', type: 'text' as const },
        { key: 'asset_id', label: 'Asset ID', type: 'text' as const },
        { key: 'assigned_date', label: 'Assigned Date', type: 'date' as const },
        { key: 'return_date', label: 'Return Date', type: 'date' as const },
        { key: 'status', label: 'Status', type: 'badge' as const },
        { key: 'condition', label: 'Condition', type: 'text' as const }
      ],
      actions: [
        { key: 'return', label: 'Return', icon: 'keyboard_return', color: 'primary', condition: (item: EmployeeAsset) => item.status === 'assigned' },
        { key: 'edit', label: 'Edit', icon: 'edit', color: 'primary' },
        { key: 'view', label: 'View', icon: 'visibility', color: 'primary' }
      ]
    };
  }

  // Action handlers
  onTableAction(action: string, item: any): void {
    switch (action) {
      case 'approve':
        this.handleApprove(item);
        break;
      case 'reject':
        this.handleReject(item);
        break;
      case 'edit':
        this.handleEdit(item);
        break;
      case 'view':
        this.handleView(item);
        break;
      case 'update-progress':
        this.handleUpdateProgress(item);
        break;
      case 'return':
        this.handleReturnAsset(item);
        break;
      case 'certificate':
        this.handleDownloadCertificate(item);
        break;
      default:
        console.log('Unhandled action:', action, item);
    }
  }

  private handleApprove(item: any): void {
    if (this.activeSection === 'hike') {
      this.employeeManagementStore.approveHike(item.id);
    } else if (this.activeSection === 'leave') {
      this.employeeManagementStore.approveLeave(item.id);
    }
  }

  private handleReject(item: any): void {
    // Implementation for reject actions
    console.log('Reject:', item);
  }

  private handleEdit(item: any): void {
    // Implementation for edit actions
    console.log('Edit:', item);
  }

  private handleView(item: any): void {
    // Implementation for view actions
    console.log('View:', item);
  }

  private handleUpdateProgress(item: EmployeeOKR): void {
    // Implementation for updating OKR progress
    console.log('Update progress:', item);
  }

  private handleReturnAsset(item: EmployeeAsset): void {
    // Implementation for returning assets
    console.log('Return asset:', item);
  }

  private handleDownloadCertificate(item: EmployeeTraining): void {
    if (item.certificate_url) {
      window.open(item.certificate_url, '_blank');
    }
  }

  // Add new item handlers
  onAddSalary(): void {
    console.log('Add new salary');
  }

  onAddHike(): void {
    console.log('Add new hike');
  }

  onAddOKR(): void {
    console.log('Add new OKR');
  }

  onAddLeave(): void {
    console.log('Add new leave');
  }

  onAddTraining(): void {
    console.log('Add new training');
  }

  onAddAsset(): void {
    console.log('Add new asset');
  }

  // Generic add new handler
  onAddNew(): void {
    switch (this.activeSection) {
      case 'salary':
        this.onAddSalary();
        break;
      case 'hike':
        this.onAddHike();
        break;
      case 'okr':
        this.onAddOKR();
        break;
      case 'leave':
        this.onAddLeave();
        break;
      case 'training':
        this.onAddTraining();
        break;
      case 'asset':
        this.onAddAsset();
        break;
    }
  }

  // Helper method to get section label
  getSectionLabel(sectionKey: ManagementSection): string {
    const section = this.sections.find(s => s.key === sectionKey);
    return section ? section.label : '';
  }
}
