import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ModalComponent } from '../../atoms/modal/modal.component';
import { ButtonComponent } from '../../atoms/button/button.component';
import { DropdownOption } from '../../atoms/dropdown-select/dropdown-select.component';
import { FileUploadComponent } from '../../atoms/file-upload/file-upload.component';
import { SearchableDropdownComponent } from '../../molecules/searchable-dropdown/searchable-dropdown.component';
import { ModalService } from '../../../services/modal.service';
import { ToastService } from '../../../services/toast.service';
import { DepartmentsStore, EmployeesStore, EmployeeInput, Employee } from '../../../../core/state';
import { Subject, takeUntil } from 'rxjs';
import { toObservable } from '@angular/core/rxjs-interop';
import { Department } from '../../../../core/models/department.interface';

@Component({
  selector: 'app-add-employee-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ModalComponent,
    ButtonComponent,
    FileUploadComponent,
    SearchableDropdownComponent
  ],
  templateUrl: './add-employee-modal.component.html',
  styleUrl: './add-employee-modal.component.scss'
})
export class AddEmployeeModalComponent implements OnInit, OnDestroy {
  private fb = inject(FormBuilder);
  private modalService = inject(ModalService);
  private toastService = inject(ToastService);
  private employeesStore = inject(EmployeesStore);
  private departmentsStore = inject(DepartmentsStore);

  employeeForm!: FormGroup;
  isSubmitting = false;
  avatarFile: File | null = null;

  // Edit mode properties
  isEditMode = false;
  employeeId: number | null = null;

  // Department options
  departments: DropdownOption[] = [];

  // For cleanup
  private destroy$ = new Subject<void>();

  // Create an observable from the departments signal
  private departments$ = toObservable(inject(DepartmentsStore).departments);

  ngOnInit(): void {
    this.initForm();
    this.loadDepartments();
    this.listenForModalOpen();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load departments for the dropdown
   */
  private loadDepartments(): void {
    // Load all departments
    this.departmentsStore.loadAllDepartments();

    // Subscribe to the departments observable
    this.departments$
      .pipe(takeUntil(this.destroy$))
      .subscribe((departments: Department[]) => {
        // Map departments to dropdown options
        this.departments = departments.map((dept: Department) => ({
          value: dept.id, // Using ID as value to match the backend model
          label: dept.name
        }));
      });
  }

  /**
   * Initialize the form with validation
   */
  private initForm(): void {
    this.employeeForm = this.fb.group({
      avatar: [null],
      first_name: ['', Validators.required],
      last_name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]], // Changed from phone_number to phone
      position: ['', Validators.required],
      department_id: [null, Validators.required], // Changed from department to department_id
      hire_date: ['', Validators.required] // Changed from start_date to hire_date
    });
  }

  /**
   * Handle avatar file selection
   * @param file The selected avatar file
   */
  onAvatarSelected(file: File): void {
    this.avatarFile = file;
    this.employeeForm.patchValue({ avatar: file });
  }

  /**
   * Handle avatar file removal
   */
  onAvatarRemoved(): void {
    this.avatarFile = null;
    this.employeeForm.patchValue({ avatar: null });
  }

  /**
   * Check if a field is invalid and has been touched
   * @param field The form field name
   * @returns True if the field is invalid and touched
   */
  isFieldInvalid(field: string): boolean {
    const formControl = this.employeeForm.get(field);
    return !!formControl && formControl.invalid && (formControl.dirty || formControl.touched);
  }

  /**
   * Listen for modal open events to handle edit mode
   */
  private listenForModalOpen(): void {
    this.modalService.getData<any>('add-employee-modal')
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        if (data && data.isEdit && data.employee) {
          this.isEditMode = true;
          this.employeeId = data.employee.id;
          this.populateFormForEdit(data.employee);
        } else {
          this.isEditMode = false;
          this.employeeId = null;
          this.resetForm();
        }
      });
  }

  /**
   * Populate the form with employee data for editing
   */
  private populateFormForEdit(employee: Employee): void {
    // Find the department ID from the department name
    let departmentId = null;
    if (employee.department) {
      const department = this.departments.find(d => d.label === employee.department);
      if (department) {
        departmentId = department.value;
      }
    }

    // Format the hire date to YYYY-MM-DD for the form
    const hireDate = employee.hire_date ? new Date(employee.hire_date).toISOString().split('T')[0] : '';

    // Update the form with employee data
    this.employeeForm.patchValue({
      first_name: employee.first_name,
      last_name: employee.last_name,
      email: employee.email,
      phone: employee.phone || '',
      position: employee.position || '',
      department_id: departmentId,
      hire_date: hireDate
    });

    // If there's an avatar URL, we don't set the file input
    // The user will need to upload a new file if they want to change it
  }

  /**
   * Submit the form
   */
  onSubmit(): void {
    if (this.employeeForm.invalid) {
      // Mark all fields as touched to show validation errors
      Object.keys(this.employeeForm.controls).forEach(key => {
        this.employeeForm.get(key)?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;

    // Create a copy of the form value
    const employeeData = { ...this.employeeForm.value };

    // Add the avatar file directly to the employee data
    // The backend API will handle the file upload using multipart/form-data
    if (this.avatarFile) {
      employeeData.avatar = this.avatarFile;
    }

    let errorWatcher: number;

    if (this.isEditMode && this.employeeId) {
      // Update existing employee
      this.employeesStore.updateEmployee({
        id: this.employeeId,
        data: employeeData as EmployeeInput
      });

      // Set up a watcher for the store state
      errorWatcher = window.setInterval(() => {
        const currentError = this.employeesStore.error();
        if (currentError) {
          clearInterval(errorWatcher);
          this.toastService.error('Failed to update employee: ' + currentError);
          this.isSubmitting = false;
        }

        // Check if the selected employee was updated (success case)
        const selectedEmployee = this.employeesStore.selectedEmployee();
        if (selectedEmployee && selectedEmployee.id === this.employeeId && !this.employeesStore.isLoading()) {
          clearInterval(errorWatcher);
          this.toastService.success('Employee updated successfully');
          this.closeModal();
          this.resetForm();
          this.isSubmitting = false;
        }
      }, 100);
    } else {
      // Create new employee
      this.employeesStore.createEmployee(employeeData as EmployeeInput);

      // Set up a watcher for the store state
      errorWatcher = window.setInterval(() => {
        const currentError = this.employeesStore.error();
        if (currentError) {
          clearInterval(errorWatcher);
          this.toastService.error('Failed to add employee: ' + currentError);
          this.isSubmitting = false;
        }

        // Check if employees were updated (success case)
        const employees = this.employeesStore.employees();
        if (employees && employees.length > 0 && !this.employeesStore.isLoading()) {
          clearInterval(errorWatcher);
          this.toastService.success('Employee added successfully');
          this.closeModal();
          this.resetForm();
          this.isSubmitting = false;
        }
      }, 100);
    }

    // Clean up watcher after 5 seconds to prevent memory leaks
    setTimeout(() => {
      clearInterval(errorWatcher);
      if (this.isSubmitting) {
        this.isSubmitting = false;
        this.toastService.error('Request timed out. Please try again.');
      }
    }, 5000);
  }

  /**
   * Close the modal
   */
  closeModal(): void {
    this.modalService.close('add-employee-modal');
  }

  /**
   * Reset the form
   */
  private resetForm(): void {
    this.employeeForm.reset();
    Object.keys(this.employeeForm.controls).forEach(key => {
      this.employeeForm.get(key)?.setErrors(null);
    });
  }

  /**
   * Open the date picker by triggering a click on the date input
   * @param inputId The ID of the date input element
   */
  openDatePicker(inputId: string): void {
    const dateInput = document.getElementById(inputId) as HTMLInputElement;
    if (dateInput) {
      dateInput.showPicker();
    }
  }
}
