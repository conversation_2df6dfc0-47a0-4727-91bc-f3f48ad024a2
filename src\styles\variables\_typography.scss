// Typography Variables

// Font Family
$font-family-base: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
$font-family-heading: $font-family-base;

// Font Weights
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Font Sizes
$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-md: 1.125rem;   // 18px
$font-size-lg: 1.25rem;    // 20px
$font-size-xl: 1.5rem;     // 24px
$font-size-2xl: 1.75rem;   // 28px
$font-size-3xl: 2rem;      // 32px
$font-size-4xl: 2.5rem;    // 40px
$font-size-5xl: 3rem;      // 48px

// Line Heights
$line-height-tight: 1.2;
$line-height-base: 1.5;
$line-height-loose: 1.8;

// Letter Spacing
$letter-spacing-tight: -0.025em;
$letter-spacing-normal: 0;
$letter-spacing-wide: 0.025em;

// Heading Styles
@mixin heading-1 {
  font-family: $font-family-heading;
  font-size: $font-size-4xl;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
}

@mixin heading-2 {
  font-family: $font-family-heading;
  font-size: $font-size-3xl;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
}

@mixin heading-3 {
  font-family: $font-family-heading;
  font-size: $font-size-2xl;
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
}

@mixin heading-4 {
  font-family: $font-family-heading;
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
}

@mixin heading-5 {
  font-family: $font-family-heading;
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  line-height: $line-height-tight;
}

@mixin heading-6 {
  font-family: $font-family-heading;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  line-height: $line-height-tight;
}

// Text Styles
@mixin text-xs {
  font-size: $font-size-xs;
  line-height: $line-height-base;
}

@mixin text-sm {
  font-size: $font-size-sm;
  line-height: $line-height-base;
}

@mixin text-base {
  font-size: $font-size-base;
  line-height: $line-height-base;
}

@mixin text-lg {
  font-size: $font-size-lg;
  line-height: $line-height-base;
}

@mixin text-xl {
  font-size: $font-size-xl;
  line-height: $line-height-base;
}


