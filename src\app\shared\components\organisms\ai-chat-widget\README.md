# AI Assistant Chat Widget

A comprehensive AI-powered chat widget for the Angular HRMS application that provides intelligent assistance to users across all pages.

## Features

### 🎯 **Core Functionality**
- **Floating Chat Interface**: Bottom-right positioned widget with smooth animations
- **Real-time AI Conversations**: Powered by `/api/v1/ai/chat/` endpoint
- **Persistent Chat History**: Maintains conversation history during session with localStorage support
- **Smart Notifications**: Visual indicators for new messages when widget is collapsed
- **Responsive Design**: Optimized for desktop and mobile devices

### 🎨 **UI/UX Features**
- **Enterprise-Grade Design**: Professional, polished appearance suitable for business environments
- **Enhanced Visual Hierarchy**: Improved layout structure for better conversation flow
- **Premium Animations**: Sophisticated transitions with backdrop blur and gradient overlays
- **Professional Branding**: "SmartHR Assistant" with brain icon for trustworthy AI representation
- **Advanced Loading States**: Enhanced typing indicators and professional loading animations
- **Comprehensive Error Handling**: User-friendly error messages with retry functionality
- **Full Accessibility**: ARIA labels, keyboard navigation, and screen reader support

### ⌨️ **Interaction Features**
- **Keyboard Shortcuts**: 
  - `Enter` to send message
  - `Shift+Enter` for new line
  - `Escape` to close widget
- **Auto-scroll**: Automatically scrolls to latest messages
- **Message Timestamps**: Relative time display (e.g., "2m ago", "Just now")
- **Suggestion Chips**: Quick-start conversation topics

## Technical Implementation

### **Component Architecture**
```
ai-chat-widget/
├── ai-chat-widget.component.ts     # Main component logic
├── ai-chat-widget.component.html   # Template with chat interface
├── ai-chat-widget.component.scss   # Comprehensive styling
└── README.md                       # This documentation
```

### **State Management**
- **NgRx Signal Store**: `AiChatStore` for reactive state management
- **Persistent Storage**: localStorage integration for chat history
- **Real-time Updates**: Reactive signals for UI updates

### **API Integration**
```typescript
// Endpoint Configuration
POST /api/v1/ai/chat/
Content-Type: application/json

// Request Payload
{
  "question": "string" // Required field
}

// Response Format
{
  "answer": "string",
  "timestamp": "string",
  "conversation_id": "string"
}
```

### **Error Handling**
- **Network Errors**: Graceful handling of connectivity issues
- **API Failures**: User-friendly error messages with retry options
- **Validation**: Input validation with character limits (1000 chars)
- **Timeout Handling**: Proper handling of slow API responses

## Usage

### **Integration**
The widget is globally integrated into the main layout component and appears on all authenticated pages:

```html
<!-- Already integrated in main-layout.component.html -->
<app-ai-chat-widget></app-ai-chat-widget>
```

### **User Flow**
1. **Initial State**: Floating chat bubble in bottom-right corner
2. **Expand**: Click bubble to open chat interface
3. **Welcome**: Greeting message with suggestion chips
4. **Conversation**: Type questions and receive AI responses
5. **Minimize**: Click minimize button to collapse widget
6. **Persistence**: Chat history maintained across page navigation

### **Example Interactions**
```
User: "What is the company leave policy?"
AI: "Our company offers 20 days of annual leave..."

User: "How do I submit an expense report?"
AI: "To submit an expense report, please follow these steps..."
```

## Configuration

### **Widget Configuration**
```typescript
interface ChatWidgetConfig {
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  theme: 'light' | 'dark' | 'auto';
  enableNotifications: boolean;
  enablePersistence: boolean;
  maxMessages: number;
}
```

### **Default Settings**
- **Position**: Bottom-right corner
- **Theme**: Auto (follows system preference)
- **Notifications**: Enabled
- **Persistence**: Enabled
- **Max Messages**: 100 (auto-cleanup)

## Styling

### **Design System**
- **Colors**: Uses SCSS variables from `color.scss`
- **Typography**: Consistent with application font system
- **Spacing**: 16px border radius, consistent padding/margins
- **Shadows**: Subtle drop shadows for depth
- **Animations**: Smooth transitions with reduced motion support

### **Responsive Breakpoints**
- **Desktop**: 420px width, 640px height (enhanced for better usability)
- **Tablet**: Full width, 75vh height
- **Mobile**: Full width, 85vh height with bottom positioning

### **Accessibility Features**
- **ARIA Labels**: Proper labeling for screen readers
- **Keyboard Navigation**: Full keyboard accessibility
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects user motion preferences

## Development

### **Dependencies**
- **Angular**: Reactive Forms, Common Module
- **NgRx Signals**: State management
- **RxJS**: Reactive programming
- **SCSS**: Styling with variables

### **Key Methods**
```typescript
// Component Methods
toggleWidget()          // Expand/collapse widget
sendMessage()          // Send user message to AI
clearChat()            // Clear conversation history
retryLastMessage()     // Retry failed message

// Store Methods
addMessage()           // Add message to history
updateMessage()        // Update existing message
loadPersistedState()   // Load from localStorage
```

### **Performance Optimizations**
- **Lazy Loading**: Component loaded only when needed
- **Virtual Scrolling**: Efficient message list rendering
- **Debounced Input**: Prevents excessive API calls
- **Memory Management**: Automatic cleanup of old messages

## Troubleshooting

### **Common Issues**
1. **API Connection**: Verify `environment.apiUrl` configuration
2. **CORS Issues**: Ensure backend allows frontend domain
3. **Authentication**: Check if user is properly authenticated
4. **LocalStorage**: Verify browser supports localStorage

### **Debug Mode**
Enable console logging by setting development mode:
```typescript
// Comprehensive logging available in development
console.log('AI Chat Debug:', message);
```

## Future Enhancements

### **Planned Features**
- **Voice Input**: Speech-to-text integration
- **File Attachments**: Support for document uploads
- **Rich Text**: Markdown rendering in messages
- **Multi-language**: Internationalization support
- **Analytics**: Usage tracking and insights

### **Performance Improvements**
- **WebSocket**: Real-time communication
- **Caching**: Intelligent response caching
- **Offline Mode**: Basic functionality without internet
- **Progressive Loading**: Incremental message loading

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**Maintainer**: Development Team
