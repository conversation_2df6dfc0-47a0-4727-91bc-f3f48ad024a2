<a [routerLink]="hasSubmenu ? null : route" [routerLinkActive]="'active'" class="menu-item"
   [ngClass]="{'active': active, 'expanded': submenuExpanded}" (click)="onItemClick($event)">
  @if (icon) {
    <div class="menu-icon-container">
      <app-icon [name]="icon" size="md"></app-icon>
    </div>
  }
  <span class="menu-label">{{ label }}</span>
  @if (badge) {
    <span class="menu-badge">{{ badge }}</span>
  }
  @if (hasSubmenu) {
    <i class="fa fa-chevron-right menu-chevron" [ngClass]="{'rotate-down': submenuExpanded}"></i>
  }
</a>
