import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ModalOptions {
  id: string;
  data?: any;
}

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  private modalsSubject = new BehaviorSubject<Record<string, boolean>>({});
  public modals$ = this.modalsSubject.asObservable();

  private modalDataSubject = new BehaviorSubject<Record<string, any>>({});
  public modalData$ = this.modalDataSubject.asObservable();

  /**
   * Open a modal
   * @param options Modal options
   */
  open(options: ModalOptions): void {
    const currentModals = this.modalsSubject.getValue();
    const currentData = this.modalDataSubject.getValue();
    
    this.modalsSubject.next({
      ...currentModals,
      [options.id]: true
    });
    
    if (options.data) {
      this.modalDataSubject.next({
        ...currentData,
        [options.id]: options.data
      });
    }
  }

  /**
   * Close a modal
   * @param id Modal ID
   */
  close(id: string): void {
    const currentModals = this.modalsSubject.getValue();
    const { [id]: removed, ...rest } = currentModals;
    this.modalsSubject.next(rest);
    
    // Clean up data
    const currentData = this.modalDataSubject.getValue();
    if (currentData[id]) {
      const { [id]: removedData, ...restData } = currentData;
      this.modalDataSubject.next(restData);
    }
  }

  /**
   * Check if a modal is open
   * @param id Modal ID
   * @returns Observable of boolean indicating if the modal is open
   */
  isOpen(id: string): Observable<boolean> {
    return new Observable<boolean>(observer => {
      this.modals$.subscribe(modals => {
        observer.next(!!modals[id]);
      });
    });
  }

  /**
   * Get modal data
   * @param id Modal ID
   * @returns Observable of modal data
   */
  getData<T>(id: string): Observable<T | undefined> {
    return new Observable<T | undefined>(observer => {
      this.modalData$.subscribe(data => {
        observer.next(data[id] as T);
      });
    });
  }
}
