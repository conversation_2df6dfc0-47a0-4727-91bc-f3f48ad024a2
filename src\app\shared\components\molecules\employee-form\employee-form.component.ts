import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

// Import atomic components
import { ButtonComponent } from '../../atoms/button/button.component';

// Import interfaces
import { Employee } from '../../../../core/state/employees/employees.state';
import { EmployeeFormData, Designation } from '../../../../core/models/employee-extended.interface';
import { Department } from '../../../../core/models/department.interface';

@Component({
  selector: 'app-employee-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonComponent
  ],
  templateUrl: './employee-form.component.html',
  styleUrls: ['./employee-form.component.scss']
})
export class EmployeeFormComponent implements OnInit, OnDestroy {
  @Input() employee: Employee | null = null;
  @Input() departments: Department[] = [];
  @Input() designations: Designation[] = [];
  @Input() isLoading: boolean = false;
  @Input() isEdit: boolean = false;
  @Input() showAdvancedFields: boolean = true;

  @Output() formSubmit = new EventEmitter<EmployeeFormData>();
  @Output() formCancel = new EventEmitter<void>();

  private fb = inject(FormBuilder);
  private destroy$ = new Subject<void>();

  employeeForm!: FormGroup;

  // Gender options
  genderOptions = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'other', label: 'Other' }
  ];

  // Status options
  statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'on_leave', label: 'On Leave' },
    { value: 'terminated', label: 'Terminated' }
  ];

  ngOnInit(): void {
    this.initializeForm();

    if (this.employee && this.isEdit) {
      this.populateForm(this.employee);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.employeeForm = this.fb.group({
      first_name: ['', [Validators.required, Validators.minLength(2)]],
      last_name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.pattern(/^[\+]?[1-9][\d]{0,15}$/)]],
      date_of_birth: [''],
      gender: [''],
      department_id: ['', Validators.required],
      designation_id: [''],
      status: ['active', Validators.required],
      profile_picture: ['']
    });
  }

  private populateForm(employee: Employee): void {
    this.employeeForm.patchValue({
      first_name: employee.first_name,
      last_name: employee.last_name,
      email: employee.email,
      phone: employee.phone || '',
      date_of_birth: employee.date_of_birth || '',
      gender: employee.gender || '',
      department_id: (typeof employee.department === 'object' ? employee.department?.id : employee.department_id) || '',
      designation_id: employee.designation?.id || '',
      status: employee.status || 'active',
      profile_picture: employee.profile_picture_url || ''
    });
  }

  onSubmit(): void {
    if (this.employeeForm.valid) {
      const formData: EmployeeFormData = this.employeeForm.value;
      this.formSubmit.emit(formData);
    } else {
      this.markFormGroupTouched(this.employeeForm);
    }
  }

  onCancel(): void {
    this.formCancel.emit();
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string | null {
    const field = this.employeeForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${this.getFieldLabel(fieldName)} is required`;
      if (field.errors['email']) return 'Please enter a valid email address';
      if (field.errors['minlength']) return `${this.getFieldLabel(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;
      if (field.errors['pattern']) return `Please enter a valid ${this.getFieldLabel(fieldName).toLowerCase()}`;
    }
    return null;
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      'first_name': 'First Name',
      'last_name': 'Last Name',
      'email': 'Email',
      'phone': 'Phone Number',
      'date_of_birth': 'Date of Birth',
      'gender': 'Gender',
      'department_id': 'Department',
      'designation_id': 'Designation',
      'status': 'Status'
    };
    return labels[fieldName] || fieldName;
  }

  get isFormValid(): boolean {
    return this.employeeForm.valid;
  }

  get hasUnsavedChanges(): boolean {
    return this.employeeForm.dirty;
  }

  get maxBirthDate(): string {
    // Maximum age of 100 years
    const maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() - 16); // Minimum working age
    return maxDate.toISOString().split('T')[0];
  }

  get minBirthDate(): string {
    // Minimum age of 16 years
    const minDate = new Date();
    minDate.setFullYear(minDate.getFullYear() - 100); // Maximum age
    return minDate.toISOString().split('T')[0];
  }
}
