import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'preferences',
    pathMatch: 'full'
  },
  {
    path: 'preferences',
    loadComponent: () => import('./preferences/preferences.component').then(m => m.PreferencesComponent),
    title: 'Preferences - SmartHR'
  },
  {
    path: 'profile',
    loadComponent: () => import('./profile/profile-settings.component').then(m => m.ProfileSettingsComponent),
    title: 'Profile Settings - SmartHR'
  },
  {
    path: 'security',
    loadComponent: () => import('./security/security-settings.component').then(m => m.SecuritySettingsComponent),
    title: 'Security Settings - SmartHR'
  },
  {
    path: 'notifications',
    loadComponent: () => import('./notifications/notification-settings.component').then(m => m.NotificationSettingsComponent),
    title: 'Notification Settings - SmartHR'
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SettingsRoutingModule { }
