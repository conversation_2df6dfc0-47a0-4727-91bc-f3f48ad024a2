@import '../../../../../styles/variables/_colors';

.side-menu {
  display: flex;
  flex-direction: column;
  width: 260px;
  height: 100vh;
  background-color: $sidebar-bg;
  border-right: 1px solid $border-color;
  transition: width 0.2s ease;
  overflow-x: hidden;
  overflow-y: auto;

  // Hide scrollbar by default
  &::-webkit-scrollbar {
    width: 6px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 4px;
  }

  // Show scrollbar on hover
  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: rgba($gray-400, 0.5);
    }

    scrollbar-color: rgba($gray-400, 0.5) transparent;
  }

  // Firefox scrollbar
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;

  // MS Edge and IE
  -ms-overflow-style: -ms-autohiding-scrollbar;

  &.collapsed {
    width: 60px;

    .title, .menu-group-title {
      display: none;
    }

    .menu-group-header {
      display: none;
    }

    .menu-group-toggle {
      display: none;
    }

    .side-menu-content {
      padding: 0;
    }

    .menu-group {
      margin-bottom: 0;
    }

    .menu-group-items {
      max-height: none !important;
    }

    .menu-label, .menu-badge {
      display: none;
    }

    .menu-item {
      justify-content: center;
      padding: 15px 0;
    }

    .menu-icon-container {
      margin-right: 0;
    }

    .user-profile {
      justify-content: center;
      padding: 15px 0;

      .user-info {
        display: none;
      }

      .user-avatar {
        margin-right: 0;
      }
    }
  }

  .side-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    min-height: 64px;
    position: relative;
    border-bottom: 1px solid $border-color;
    margin-bottom: 0;

    .logo-container {
      display: flex;
      align-items: center;

      .logo {
        width: 32px;
        height: 32px;
        margin-right: 10px;
      }

      .title {
        font-size: 18px;
        font-weight: $font-weight-semibold;
        color: $gray-800;

        span {
          color: $danger;
        }
      }
    }

    .collapse-toggle {
      background-color: rgba($sidebar-hover-bg, 0.1);
      border: none;
      cursor: pointer;
      padding: 5px;
      color: $gray-600;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 4px;
      transition: all 0.2s ease;
      font-size: 16px;
      z-index: 10;

      &:hover {
        background-color: rgba($sidebar-hover-bg, 0.2);
        color: $gray-800;
      }
    }
  }

  &.collapsed {
    .side-menu-header {
      padding: 15px 10px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid $border-color;
      min-height: 60px;

      .logo {
        margin-left: 5px;
      }
    }

    .expand-toggle {
      background: none;
      border: none;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 16px;
      color: $gray-600;
      padding: 0;
      margin-right: 5px;

      &:hover {
        color: $gray-800;
      }
    }
  }

  // User Profile
  .user-profile {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid $border-color;
    transition: all 0.2s ease;

    .user-avatar {
      position: relative;
      margin-right: 12px;

      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
      }

      .user-badge {
        position: absolute;
        bottom: -2px;
        right: -2px;
        background-color: $primary;
        color: $white;
        font-size: 8px;
        padding: 2px 4px;
        border-radius: 10px;
        font-weight: $font-weight-semibold;
        border: 2px solid $white;
      }
    }

    .user-info {
      flex: 1;
      overflow: hidden;

      .user-name {
        font-size: 14px;
        font-weight: $font-weight-medium;
        color: $gray-800;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .user-email {
        font-size: 12px;
        color: $gray-600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .side-menu-content {
    padding: 0;
    flex: 1;

    // Style for the menu groups
    app-menu-group {
      .menu-group-header {
        margin-bottom: 4px;
      }
    }

    .menu-item-container {
      display: flex;
      flex-direction: column;
      position: relative;

      .submenu {
        padding-left: 0;
        overflow: hidden;
        transition: all 0.5s ease;
        max-height: 0;
        opacity: 0;
        margin-left: 0;
        border-left: none;

        &::before {
          content: '';
          position: absolute;
          left: 36px;
          top: 44px; // Height of the main menu item
          bottom: 0;
          width: 1px;
          background-color: $gray-300;
          height: calc(100% - 44px);
        }

        &.expanded {
          max-height: 500px; // Large enough to accommodate all submenu items
          opacity: 1;
        }

        .submenu-item {
          margin: 0;
          position: relative;
          padding-left: 36px;

          &.active {
            &::before {
              content: '';
              position: absolute;
              left: 36px;
              top: 0;
              bottom: 0;
              width: 3px;
              background-color: $warning;
            }

            .submenu-link {
              color: $warning;
              font-weight: $font-weight-medium;
            }
          }

          .submenu-link {
            display: flex;
            align-items: center;
            height: 40px;
            color: $gray-600;
            text-decoration: none;
            font-size: 14px;
            font-weight: $font-weight-regular;
            transition: color 0.2s ease;
            padding-left: 16px;

            &:hover {
              color: $gray-800;
            }
          }
        }
      }
    }
  }
}



