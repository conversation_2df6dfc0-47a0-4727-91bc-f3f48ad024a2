@import '../../../../../styles/variables/colors';

.date-input {
  position: relative;
  width: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  &__label {
    display: block;
    font-weight: 500;
    color: $gray-700;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;

    &--required {
      color: $gray-800;
    }
  }

  &__required {
    color: $danger;
    margin-left: 0.25rem;
  }

  &__container {
    position: relative;
    width: 100%;

    &--disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &--error {
      .date-input__input {
        border-color: $danger;
        box-shadow: 0 0 0 3px rgba($danger, 0.1);
      }
    }
  }

  &__input {
    width: 100%;
    padding: 0.75rem 1rem;
    background: $white;
    border: 1px solid $gray-300;
    border-radius: 12px;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: $gray-900;
    transition: all 0.2s ease;
    min-height: 2.75rem;

    &:hover:not(:disabled) {
      border-color: $gray-400;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.1);
    }

    &:disabled {
      background-color: $gray-100;
      color: $gray-500;
      cursor: not-allowed;
    }
  }

  &__age {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba($primary, 0.05);
    border-radius: 8px;
    font-size: 0.75rem;
  }

  &__age-label {
    color: $gray-600;
    font-weight: 500;
  }

  &__age-value {
    color: $primary;
    font-weight: 600;
  }

  &__error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    color: $danger;
    font-size: 0.75rem;
    line-height: 1rem;

    svg {
      flex-shrink: 0;
    }
  }

  // Size variants
  &--sm {
    .date-input__input {
      padding: 0.5rem 0.75rem;
      font-size: 0.75rem;
      min-height: 2.25rem;
    }

    .date-input__label {
      font-size: 0.75rem;
      margin-bottom: 0.25rem;
    }
  }

  &--lg {
    .date-input__input {
      padding: 1rem 1.25rem;
      font-size: 1rem;
      min-height: 3.25rem;
    }

    .date-input__label {
      font-size: 1rem;
      margin-bottom: 0.75rem;
    }
  }

  // Variant styles
  &--outline {
    .date-input__input {
      background: transparent;
      border: 2px solid $gray-300;

      &:hover:not(:disabled) {
        border-color: $primary;
      }

      &:focus {
        border-color: $primary;
        box-shadow: 0 0 0 3px rgba($primary, 0.1);
      }
    }
  }

  &--filled {
    .date-input__input {
      background: $gray-50;
      border: 1px solid transparent;

      &:hover:not(:disabled) {
        background: $gray-100;
      }

      &:focus {
        background: $white;
        border-color: $primary;
        box-shadow: 0 0 0 3px rgba($primary, 0.1);
      }
    }
  }
}
