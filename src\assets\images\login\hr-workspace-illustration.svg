<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>HR Workspace Illustration</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FF8A65" offset="0%"></stop>
            <stop stop-color="#FF6B35" offset="50%"></stop>
            <stop stop-color="#E85A2A" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#495057" offset="0%"></stop>
            <stop stop-color="#343A40" offset="50%"></stop>
            <stop stop-color="#212529" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-3">
            <stop stop-color="#F8F9FA" offset="0%"></stop>
            <stop stop-color="#E9ECEF" offset="100%"></stop>
        </linearGradient>
        <filter x="-10.0%" y="-10.0%" width="120.0%" height="120.0%" filterUnits="objectBoundingBox" id="filter-blur">
            <feGaussianBlur stdDeviation="10" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <pattern id="pattern-dots" width="20" height="20" patternUnits="userSpaceOnUse">
            <circle cx="3" cy="3" r="2" fill="#FF6B35" opacity="0.3"></circle>
        </pattern>
    </defs>
    <g id="HR-Workspace-Illustration" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background Elements -->
        <rect id="Background" fill="#FFFFFF" x="0" y="0" width="800" height="600"></rect>
        <rect id="Pattern-Background" fill="url(#pattern-dots)" x="0" y="0" width="800" height="600" opacity="0.5"></rect>
        
        <!-- Abstract Shapes -->
        <circle id="Circle-1" fill="url(#linearGradient-1)" opacity="0.2" filter="url(#filter-blur)" cx="650" cy="150" r="100"></circle>
        <circle id="Circle-2" fill="url(#linearGradient-2)" opacity="0.1" filter="url(#filter-blur)" cx="150" cy="450" r="120"></circle>
        <circle id="Circle-3" fill="url(#linearGradient-1)" opacity="0.1" filter="url(#filter-blur)" cx="400" cy="500" r="80"></circle>
        
        <!-- Main Illustration Elements -->
        <g id="Workspace" transform="translate(100, 50)">
            <!-- Office Desk -->
            <rect id="Desk" fill="url(#linearGradient-2)" x="100" y="350" width="500" height="20" rx="5"></rect>
            <rect id="Desk-Leg-Left" fill="url(#linearGradient-2)" x="150" y="370" width="20" height="80" rx="5"></rect>
            <rect id="Desk-Leg-Right" fill="url(#linearGradient-2)" x="530" y="370" width="20" height="80" rx="5"></rect>
            
            <!-- Computer Monitor -->
            <g id="Monitor" transform="translate(300, 180)">
                <rect id="Monitor-Stand" fill="#343A40" x="45" y="120" width="30" height="50" rx="5"></rect>
                <rect id="Monitor-Base" fill="#343A40" x="25" y="170" width="70" height="10" rx="5"></rect>
                <rect id="Monitor-Frame" fill="#212529" x="0" y="0" width="120" height="120" rx="10"></rect>
                <rect id="Monitor-Screen" fill="#F8F9FA" x="5" y="5" width="110" height="110" rx="5"></rect>
                
                <!-- Screen Content -->
                <rect id="Header" fill="#FF6B35" x="5" y="5" width="110" height="20" rx="5 5 0 0"></rect>
                <circle id="Close-Button" fill="#FFFFFF" opacity="0.5" cx="15" cy="15" r="3"></circle>
                <circle id="Minimize-Button" fill="#FFFFFF" opacity="0.5" cx="25" cy="15" r="3"></circle>
                <circle id="Maximize-Button" fill="#FFFFFF" opacity="0.5" cx="35" cy="15" r="3"></circle>
                
                <rect id="Sidebar" fill="#E9ECEF" x="5" y="25" width="25" height="90"></rect>
                <rect id="Sidebar-Item-1" fill="#FF6B35" x="5" y="35" width="25" height="10" opacity="0.7"></rect>
                <rect id="Sidebar-Item-2" fill="#ADB5BD" x="5" y="55" width="25" height="10" opacity="0.3"></rect>
                <rect id="Sidebar-Item-3" fill="#ADB5BD" x="5" y="75" width="25" height="10" opacity="0.3"></rect>
                <rect id="Sidebar-Item-4" fill="#ADB5BD" x="5" y="95" width="25" height="10" opacity="0.3"></rect>
                
                <rect id="Content-Card-1" fill="#FFFFFF" stroke="#DEE2E6" stroke-width="1" x="40" y="35" width="65" height="25" rx="3"></rect>
                <rect id="Content-Card-2" fill="#FFFFFF" stroke="#DEE2E6" stroke-width="1" x="40" y="70" width="65" height="25" rx="3"></rect>
                <rect id="Content-Card-3" fill="#FFFFFF" stroke="#DEE2E6" stroke-width="1" x="40" y="105" width="65" height="10" rx="3"></rect>
            </g>
            
            <!-- Laptop -->
            <g id="Laptop" transform="translate(150, 250)">
                <rect id="Laptop-Base" fill="#343A40" x="0" y="100" width="120" height="10" rx="5"></rect>
                <path d="M10,10 L110,10 C115.5,10 120,14.5 120,20 L120,100 L0,100 L0,20 C0,14.5 4.5,10 10,10 Z" id="Laptop-Screen" fill="#212529"></path>
                <path d="M10,15 L110,15 C112.8,15 115,17.2 115,20 L115,95 L5,95 L5,20 C5,17.2 7.2,15 10,15 Z" id="Laptop-Display" fill="#F8F9FA"></path>
                
                <!-- Laptop Screen Content -->
                <rect id="Laptop-Header" fill="#FF6B35" x="5" y="15" width="110" height="15" rx="5 5 0 0"></rect>
                <rect id="Laptop-Content-1" fill="#E9ECEF" x="15" y="40" width="90" height="10" rx="2"></rect>
                <rect id="Laptop-Content-2" fill="#E9ECEF" x="15" y="60" width="90" height="10" rx="2"></rect>
                <rect id="Laptop-Content-3" fill="#E9ECEF" x="15" y="80" width="50" height="10" rx="2"></rect>
            </g>
            
            <!-- Coffee Cup -->
            <g id="Coffee-Cup" transform="translate(450, 300)">
                <path d="M10,0 L50,0 C55.5,0 60,4.5 60,10 L60,40 C60,45.5 55.5,50 50,50 L10,50 C4.5,50 0,45.5 0,40 L0,10 C0,4.5 4.5,0 10,0 Z" id="Cup" fill="#FFFFFF" stroke="#DEE2E6" stroke-width="2"></path>
                <path d="M60,10 C65.5,10 70,14.5 70,20 L70,25 C70,30.5 65.5,35 60,35 L60,10 Z" id="Handle" fill="#FFFFFF" stroke="#DEE2E6" stroke-width="2"></path>
                <path d="M10,5 L50,5 C52.8,5 55,7.2 55,10 L55,15 L5,15 L5,10 C5,7.2 7.2,5 10,5 Z" id="Coffee" fill="url(#linearGradient-1)"></path>
            </g>
            
            <!-- Plant -->
            <g id="Plant" transform="translate(500, 270)">
                <rect id="Pot" fill="#6C757D" x="10" y="50" width="40" height="30" rx="5"></rect>
                <rect id="Soil" fill="#495057" x="10" y="50" width="40" height="10" rx="5 5 0 0"></rect>
                <path d="M30,0 C40,15 45,30 30,50 C15,30 20,15 30,0 Z" id="Leaf-1" fill="#28A745"></path>
                <path d="M20,10 C25,25 15,40 5,45 C15,25 15,15 20,10 Z" id="Leaf-2" fill="#28A745"></path>
                <path d="M40,10 C35,25 45,40 55,45 C45,25 45,15 40,10 Z" id="Leaf-3" fill="#28A745"></path>
            </g>
            
            <!-- Notebook -->
            <g id="Notebook" transform="translate(200, 320)">
                <rect id="Notebook-Base" fill="#F8F9FA" stroke="#DEE2E6" stroke-width="2" x="0" y="0" width="70" height="90" rx="5"></rect>
                <line x1="0" y1="15" x2="70" y2="15" id="Line-1" stroke="#DEE2E6" stroke-width="1"></line>
                <line x1="0" y1="30" x2="70" y2="30" id="Line-2" stroke="#DEE2E6" stroke-width="1"></line>
                <line x1="0" y1="45" x2="70" y2="45" id="Line-3" stroke="#DEE2E6" stroke-width="1"></line>
                <line x1="0" y1="60" x2="70" y2="60" id="Line-4" stroke="#DEE2E6" stroke-width="1"></line>
                <line x1="0" y1="75" x2="70" y2="75" id="Line-5" stroke="#DEE2E6" stroke-width="1"></line>
            </g>
            
            <!-- Pen -->
            <g id="Pen" transform="translate(250, 340) rotate(45)">
                <rect id="Pen-Body" fill="#FF6B35" x="0" y="0" width="60" height="8" rx="4"></rect>
                <path d="M0,0 L15,4 L0,8 Z" id="Pen-Tip" fill="#343A40" transform="translate(0, 4) rotate(180) translate(0, -4)"></path>
            </g>
        </g>
        
        <!-- HR Shell Text -->
        <text id="HR-Shell" font-family="Arial-BoldMT, Arial" font-size="36" font-weight="bold" fill="#FF6B35">
            <tspan x="325" y="530">HR Shell</tspan>
        </text>
        <text id="Tagline" font-family="Arial, Arial" font-size="16" fill="#6C757D" text-anchor="middle">
            <tspan x="400" y="570">Empowering your workforce management</tspan>
        </text>
    </g>
</svg>
