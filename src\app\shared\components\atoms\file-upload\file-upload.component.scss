// File Upload Component - BEM Structure
// Block: file-uploader

.file-uploader {
  $root: &;
  width: 100%;
  min-height: 200px;
  border: 2px dashed #ced4da;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
  overflow: hidden;
  cursor: pointer;

  // States
  &:hover {
    border-color: #adb5bd;
    background: linear-gradient(to bottom, #f1f3f5, #e2e6ea);
  }

  &--dragging {
    border-color: #ff6b35;
    background: linear-gradient(to bottom, #fff8f5, #ffede5);
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
  }

  &--has-file {
    border-style: solid;
    border-color: #28a745;
    background: linear-gradient(to bottom, #f2f9f5, #e7f5ec);
  }

  // Elements
  &__content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(to bottom, #f1f3f5, #e2e6ea);
    }

    &:active {
      background: linear-gradient(to bottom, #e9ecef, #dee2e6);
    }
  }

  &__placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  &__icon {
    font-size: 36px;
    color: #6c757d;
    margin-bottom: 16px;
  }

  &__primary-text {
    font-size: 16px;
    font-weight: 500;
    color: #495057;
    margin: 0 0 8px 0;
  }

  &__secondary-text {
    font-size: 14px;
    color: #6c757d;
    margin: 0 0 4px 0;
  }

  &__browse-text {
    color: #ff6b35;
    cursor: pointer;
    font-weight: 500;
    text-decoration: underline;
  }

  &__file-types {
    font-size: 12px;
    color: #adb5bd;
    margin: 4px 0 0 0;
  }

  &__input {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
  }

  &__preview {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    &-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #dc3545;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 1);
      color: #bd2130;
    }
  }
}
