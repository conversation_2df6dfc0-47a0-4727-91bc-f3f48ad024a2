import { Component, Input, Output, EventEmitter, HostListener, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModalService } from '../../../services/modal.service';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-modal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './modal.component.html',
  styleUrl: './modal.component.scss',
  animations: [
    trigger('overlayAnimation', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('150ms ease-out', style({ opacity: 1 }))
      ]),
      transition(':leave', [
        animate('150ms ease-in', style({ opacity: 0 }))
      ])
    ]),
    trigger('modalAnimation', [
      transition(':enter', [
        style({ transform: 'scale(0.9)', opacity: 0 }),
        animate('200ms ease-out', style({ transform: 'scale(1)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ transform: 'scale(0.9)', opacity: 0 }))
      ])
    ])
  ]
})
export class ModalComponent {
  private modalService = inject(ModalService);

  @Input() id!: string;
  @Input() title: string = '';
  @Input() size: 'small' | 'medium' | 'large' | 'full' = 'medium';
  @Input() closeOnOverlayClick: boolean = true;
  @Input() showFooter: boolean = true;
  @Input() set open(value: boolean) {
    if (value) {
      this.modalService.open({ id: this.id });
    } else {
      this.modalService.close(this.id);
    }
  }

  @Output() closed = new EventEmitter<void>();

  isOpen: boolean = false;

  ngOnInit(): void {
    if (!this.id) {
      throw new Error('Modal must have an id');
    }

    this.modalService.isOpen(this.id).subscribe(isOpen => {
      this.isOpen = isOpen;
    });
  }

  close(): void {
    this.modalService.close(this.id);
    this.closed.emit();
  }

  @HostListener('document:keydown.escape')
  onEscapeKey(): void {
    if (this.isOpen) {
      this.close();
    }
  }
}
