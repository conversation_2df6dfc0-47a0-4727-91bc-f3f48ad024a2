<button
  class="btn"
  [ngClass]="[
    'btn-' + variant,
    size ? 'btn-' + size : '',
    block ? 'btn-block' : '',
    iconOnly ? 'btn-icon-only' : ''
  ]"
  [disabled]="disabled"
  (click)="onClick.emit($event)">
  <app-icon *ngIf="icon" [name]="icon" [size]="iconSize"></app-icon>
  <span *ngIf="!iconOnly" class="btn-text">{{ label }}</span>
  <app-icon *ngIf="iconRight && !iconOnly" [name]="iconRight" [size]="iconSize"></app-icon>
</button>
