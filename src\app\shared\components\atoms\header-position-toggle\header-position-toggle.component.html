<!-- Header Position Dropdown -->
<div class="header-position-toggle">
  <div class="header-position-toggle__dropdown">
    <button 
      type="button"
      class="header-position-toggle__trigger"
      [title]="'Header position: ' + getHeaderPositionDisplayName(headerPosition())"
      [attr.aria-label]="'Header position: ' + getHeaderPositionDisplayName(headerPosition())">
      <i [class]="getCurrentHeaderPositionIcon()" class="header-position-toggle__icon"></i>
      <span class="header-position-toggle__label">{{ getHeaderPositionDisplayName(headerPosition()) }}</span>
      <i class="fas fa-chevron-down header-position-toggle__chevron"></i>
    </button>
    
    <div class="header-position-toggle__menu">
      <div class="header-position-toggle__menu-header">
        <h4 class="header-position-toggle__menu-title">Header Position</h4>
        <p class="header-position-toggle__menu-subtitle">Choose how the header behaves during scroll</p>
      </div>
      
      <div class="header-position-toggle__options">
        <button
          *ngFor="let position of getAvailableHeaderPositions()"
          type="button"
          class="header-position-toggle__option"
          [class.header-position-toggle__option--active]="isHeaderPositionActive(position.value)"
          (click)="setHeaderPosition(position.value)"
          [attr.aria-label]="'Set header position to ' + position.label">
          <div class="header-position-toggle__option-icon">
            <i [class]="position.icon"></i>
          </div>
          <div class="header-position-toggle__option-content">
            <span class="header-position-toggle__option-label">{{ position.label }}</span>
            <span class="header-position-toggle__option-description">{{ position.description }}</span>
          </div>
          <div class="header-position-toggle__option-check">
            <i 
              *ngIf="isHeaderPositionActive(position.value)"
              class="fas fa-check"></i>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Compact Icon-Only Version -->
<div class="header-position-toggle header-position-toggle--compact">
  <button 
    type="button"
    class="header-position-toggle__button"
    [title]="getCurrentHeaderPositionDescription()"
    [attr.aria-label]="'Header position: ' + getHeaderPositionDisplayName(headerPosition())">
    <i [class]="getCurrentHeaderPositionIcon()" class="header-position-toggle__icon"></i>
  </button>
  
  <div class="header-position-toggle__tooltip">
    <div class="header-position-toggle__tooltip-content">
      <strong>{{ getHeaderPositionDisplayName(headerPosition()) }}</strong>
      <span>{{ getCurrentHeaderPositionDescription() }}</span>
    </div>
  </div>
</div>
