<?xml version="1.0" encoding="UTF-8"?>
<svg width="300px" height="80px" viewBox="0 0 300 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>HR Shell Logo</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="gradient-orange">
            <stop stop-color="#FF8A65" offset="0%"></stop>
            <stop stop-color="#FF6B35" offset="50%"></stop>
            <stop stop-color="#E85A2A" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="gradient-dark">
            <stop stop-color="#495057" offset="0%"></stop>
            <stop stop-color="#343A40" offset="50%"></stop>
            <stop stop-color="#212529" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="HR-Shell-Logo" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Shell Icon -->
        <g id="Shell-Icon" transform="translate(15, 10)">
            <path d="M30,0 C46.5685,0 60,13.4315 60,30 C60,46.5685 46.5685,60 30,60 C13.4315,60 0,46.5685 0,30 C0,13.4315 13.4315,0 30,0 Z" id="Outer-Shell" fill="url(#gradient-dark)" opacity="0.1"></path>
            <path d="M30,5 C43.8071,5 55,16.1929 55,30 C55,43.8071 43.8071,55 30,55 C16.1929,55 5,43.8071 5,30 C5,16.1929 16.1929,5 30,5 Z" id="Middle-Shell" fill="url(#gradient-dark)" opacity="0.2"></path>
            <path d="M30,10 C41.0457,10 50,18.9543 50,30 C50,41.0457 41.0457,50 30,50 C18.9543,50 10,41.0457 10,30 C10,18.9543 18.9543,10 30,10 Z" id="Inner-Shell" fill="url(#gradient-dark)" opacity="0.3"></path>
            <path d="M30,15 C38.2843,15 45,21.7157 45,30 C45,38.2843 38.2843,45 30,45 C21.7157,45 15,38.2843 15,30 C15,21.7157 21.7157,15 30,15 Z" id="Core-Shell" fill="url(#gradient-orange)"></path>
            
            <!-- HR Text in Shell -->
            <text id="HR-Text" font-family="Arial-BoldMT, Arial" font-size="16" font-weight="bold" fill="#FFFFFF" text-anchor="middle">
                <tspan x="30" y="35">HR</tspan>
            </text>
        </g>
        
        <!-- Text Part -->
        <g id="Text" transform="translate(85, 25)">
            <text id="HR" font-family="Arial-BoldMT, Arial" font-size="30" font-weight="bold" fill="#343A40">
                <tspan x="0" y="30">HR</tspan>
            </text>
            <text id="Shell" font-family="Arial-BoldMT, Arial" font-size="30" font-weight="bold" fill="url(#gradient-orange)">
                <tspan x="50" y="30">Shell</tspan>
            </text>
        </g>
        
        <!-- Decorative Elements -->
        <g id="Decorative-Elements" transform="translate(85, 40)" opacity="0.6">
            <path d="M0,15 C10,5 20,25 30,15 C40,5 50,25 60,15 C70,5 80,25 90,15 C100,5 110,25 120,15" id="Wave" stroke="url(#gradient-orange)" stroke-width="1.5" stroke-linecap="round"></path>
        </g>
    </g>
</svg>
