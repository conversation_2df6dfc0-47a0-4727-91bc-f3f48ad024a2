import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../atoms/button/button.component';
import { DropdownSelectComponent, DropdownOption } from '../../atoms/dropdown-select/dropdown-select.component';
import { DateRangePickerComponent } from '../../atoms/date-range-picker/date-range-picker.component';
import { SearchInputComponent } from '../../atoms/search-input/search-input.component';

@Component({
  selector: 'app-table-actions',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    DropdownSelectComponent,
    DateRangePickerComponent,
    SearchInputComponent
  ],
  template: `
    <div class="table-actions">
      <div class="table-title">{{ title }}</div>

      <div class="filters">
        <app-date-range-picker
          *ngIf="showDateFilter"
          [startDate]="startDate"
          [endDate]="endDate"
          (dateRangeChange)="onDateRangeChange($event)">
        </app-date-range-picker>

        <app-dropdown-select
          *ngFor="let filter of filters"
          [options]="filter.options"
          [selectedValue]="filter.selectedValue"
          [placeholder]="filter.placeholder"
          (selectionChange)="onFilterChange(filter.name, $event)">
        </app-dropdown-select>
      </div>

      <div class="actions">
        <app-search-input
          *ngIf="showSearch"
          [placeholder]="searchPlaceholder"
          (search)="onSearch($event)">
        </app-search-input>

        <app-button
          *ngIf="showExport"
          [label]="exportLabel"
          [icon]="exportIcon"
          [variant]="'secondary'"
          (onClick)="onExport()">
        </app-button>

        <app-button
          *ngIf="showAddButton"
          [label]="addButtonLabel"
          [icon]="addButtonIcon"
          [variant]="'primary'"
          (onClick)="onAddClick()">
        </app-button>
      </div>
    </div>
  `,
  styles: [`
    .table-actions {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      gap: 16px;
    }

    .table-title {
      font-size: 18px;
      font-weight: 600;
      color: #343a40;
    }

    .filters {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }

    .actions {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-left: auto;
    }

    @media (max-width: 768px) {
      .table-actions {
        flex-direction: column;
        align-items: stretch;
      }

      .filters, .actions {
        width: 100%;
      }

      .actions {
        margin-left: 0;
      }
    }
  `]
})
export class TableActionsComponent {
  @Input() title: string = '';
  @Input() showSearch: boolean = true;
  @Input() searchPlaceholder: string = 'Search...';
  @Input() showExport: boolean = false;
  @Input() exportLabel: string = 'Export';
  @Input() exportIcon: string = 'fa fa-download';
  @Input() showAddButton: boolean = false;
  @Input() addButtonLabel: string = 'Add';
  @Input() addButtonIcon: string = 'fa fa-plus';
  @Input() showDateFilter: boolean = false;
  @Input() startDate: Date | null = null;
  @Input() endDate: Date | null = null;
  @Input() filters: {
    name: string;
    options: DropdownOption[];
    selectedValue: string | number;
    placeholder: string;
  }[] = [];

  @Output() search = new EventEmitter<string>();
  @Output() export = new EventEmitter<void>();
  @Output() addClick = new EventEmitter<void>();
  @Output() dateRangeChange = new EventEmitter<{start: Date, end: Date}>();
  @Output() filterChange = new EventEmitter<{name: string, value: string | number}>();

  onSearch(query: string): void {
    this.search.emit(query);
  }

  onExport(): void {
    this.export.emit();
  }

  onAddClick(): void {
    this.addClick.emit();
  }

  onDateRangeChange(dateRange: {start: Date, end: Date}): void {
    this.dateRangeChange.emit(dateRange);
  }

  onFilterChange(name: string, value: string | number): void {
    this.filterChange.emit({name, value});
  }
}
