import { inject } from '@angular/core';
import { Router, CanActivateFn, CanActivateChildFn, UrlTree } from '@angular/router';
import { Observable, of } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { JwtService } from '../services/jwt.service';

const checkAuth = (url: string): Observable<boolean | UrlTree> => {
  const authService = inject(AuthService);
  const jwtService = inject(JwtService);
  const router = inject(Router);

  const token = authService.getAccessToken();

  // No token available
  if (!token) {
    router.navigate(['/login'], { queryParams: { returnUrl: url } });
    return of(false);
  }

  // Check if token is expired
  if (jwtService.isTokenExpired(token)) {
    // Only try to refresh if we have a refresh token
    const refreshToken = authService.getRefreshToken();
    if (!refreshToken) {
      router.navigate(['/login'], { queryParams: { returnUrl: url } });
      return of(false);
    }

    // Try to refresh the token
    return authService.refreshToken().pipe(
      map(newToken => {
        if (!newToken) {
          router.navigate(['/login'], { queryParams: { returnUrl: url } });
          return false;
        }
        return true;
      }),
      catchError(() => {
        router.navigate(['/login'], { queryParams: { returnUrl: url } });
        return of(false);
      })
    );
  }

  // Token is valid, allow access
  return of(true);
};

export const authGuard: CanActivateFn = (route, state) => {
  return checkAuth(state.url);
};

export const authGuardChild: CanActivateChildFn = (childRoute, state) => {
  return checkAuth(state.url);
};
