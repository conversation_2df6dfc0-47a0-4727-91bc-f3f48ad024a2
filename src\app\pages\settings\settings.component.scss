@use 'sass:color';
@import '../../../styles/variables/_colors';

.settings-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

// Settings Header
.settings-header {
  margin-bottom: 2rem;
}

.settings-title {
  font-size: 1.75rem;
  font-weight: $font-weight-bold;
  color: $gray-900;
  margin-bottom: 0.5rem;
}

.settings-subtitle {
  color: $gray-600;
  font-size: 1rem;
}

// Settings Container
.settings-container {
  display: flex;
  background-color: $white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba($black, 0.05);
  overflow: hidden;

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

// Settings Sidebar
.settings-sidebar {
  width: 250px;
  background-color: $gray-100;
  border-right: 1px solid $gray-200;
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid $gray-200;
  }
}

.settings-nav {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
}

.settings-nav-item {
  margin: 0;

  &.active {
    .settings-nav-link {
      background-color: rgba($primary, 0.1);
      color: $primary;
      font-weight: $font-weight-semibold;
      border-left-color: $primary;
    }
  }
}

.settings-nav-link {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: $gray-700;
  text-decoration: none;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
  background: none;
  border-top: none;
  border-right: none;
  border-bottom: none;
  width: 100%;
  text-align: left;
  cursor: pointer;

  i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
    font-size: 1rem;
  }

  &:hover {
    background-color: rgba($gray-200, 0.5);
    color: $gray-900;
  }
}

.settings-nav-footer {
  padding: 1rem;
  border-top: 1px solid $gray-200;
}

.logout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.75rem;
  background-color: $white;
  color: $danger;
  border: 1px solid $gray-300;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: $font-weight-medium;
  cursor: pointer;
  transition: all 0.2s ease;

  i {
    margin-right: 0.5rem;
  }

  &:hover {
    background-color: rgba($danger, 0.1);
    border-color: $danger;
  }
}

// Settings Content
.settings-content {
  flex: 1;
  padding: 2rem;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
}

// Settings Section
.settings-section {
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.settings-section-title {
  font-size: 1.25rem;
  font-weight: $font-weight-semibold;
  color: $gray-800;
  margin: 0 0 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid $gray-200;
}

// Account Info
.account-info {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;

  @media (max-width: 576px) {
    flex-direction: column;
    align-items: flex-start;
  }
}

.account-avatar {
  position: relative;
  margin-right: 1.5rem;

  img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
  }

  @media (max-width: 576px) {
    margin-right: 0;
    margin-bottom: 1rem;
  }
}

.avatar-upload {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 28px;
  height: 28px;
  background-color: $primary;
  color: $white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;

  &:hover {
    background-color: color.adjust($primary, $lightness: -10%);
  }
}

.account-details {
  flex: 1;
}

.account-name {
  font-size: 1.25rem;
  font-weight: $font-weight-semibold;
  color: $gray-900;
  margin-bottom: 0.25rem;
}

.account-email {
  color: $gray-600;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.account-badge {
  display: inline-block;
  background-color: $primary;
  color: $white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: $font-weight-semibold;
}

.account-actions {
  display: flex;
  gap: 1rem;

  @media (max-width: 576px) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

// Forms
.settings-form {
  max-width: 600px;
}

.form-group {
  margin-bottom: 1.5rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }
}

.password-input {
  position: relative;

  input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid $gray-300;
    border-radius: 4px;
    font-size: 1rem;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba($primary, 0.1);
    }
  }
}

.form-error {
  color: $danger;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.form-actions {
  margin-top: 2rem;
}

// Checkbox and Radio Groups
.checkbox-group, .radio-group {
  margin-bottom: 1rem;
}

.checkbox-control, .radio-control {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;

  input[type="checkbox"], input[type="radio"] {
    margin-top: 0.25rem;
    margin-right: 0.75rem;
  }

  label {
    margin-bottom: 0.25rem;
  }
}

.checkbox-description {
  font-size: 0.75rem;
  color: $gray-600;
  margin: 0 0 0 1.75rem;
}

// Buttons
.btn {
  padding: 0.75rem 1.25rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: $font-weight-medium;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  i {
    margin-right: 0.5rem;
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.btn-primary {
  background-color: $primary;
  color: $white;

  &:hover:not(:disabled) {
    background-color: color.adjust($primary, $lightness: -10%);
  }
}

.btn-danger {
  background-color: $white;
  color: $danger;
  border: 1px solid $danger;

  &:hover:not(:disabled) {
    background-color: rgba($danger, 0.1);
  }
}

// Alerts
.alert {
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  position: relative;

  i {
    margin-right: 0.75rem;
  }
}

.alert-success {
  background-color: rgba($success, 0.1);
  color: $success;
  border-left: 3px solid $success;
}

.alert-error {
  background-color: rgba($danger, 0.1);
  color: $danger;
  border-left: 3px solid $danger;
}

.alert-close {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;

  &:hover {
    opacity: 1;
  }
}


