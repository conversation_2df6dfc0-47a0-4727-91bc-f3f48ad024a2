import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-profile-settings',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="page-header">
      <div class="page-header__content">
        <div class="page-header__title">
          <h1>Profile Settings</h1>
          <p>Manage your personal information and account details</p>
        </div>
      </div>
    </div>

    <div class="settings-container">
      <div class="settings-card">
        <div class="settings-card__header">
          <h3>Personal Information</h3>
          <p>Update your personal details and contact information</p>
        </div>
        <div class="settings-card__content">
          <p>Profile settings content will be implemented here...</p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .page-header {
      background: var(--theme-card-background, #ffffff);
      border: 1px solid var(--theme-card-border, #e5e7eb);
      border-radius: 12px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .page-header__title h1 {
      font-size: 2rem;
      font-weight: 700;
      color: var(--theme-on-background, #111827);
      margin: 0 0 0.5rem;
    }

    .page-header__title p {
      color: var(--theme-on-surface, #6b7280);
      margin: 0;
    }

    .settings-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .settings-card {
      background: var(--theme-card-background, #ffffff);
      border: 1px solid var(--theme-card-border, #e5e7eb);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .settings-card__header {
      padding: 1.5rem 2rem;
      border-bottom: 1px solid var(--theme-divider, #f3f4f6);
      background: var(--theme-surface-variant, #f9fafb);
    }

    .settings-card__header h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--theme-on-background, #111827);
      margin: 0 0 0.5rem;
    }

    .settings-card__header p {
      color: var(--theme-on-surface, #6b7280);
      margin: 0;
      font-size: 0.875rem;
    }

    .settings-card__content {
      padding: 2rem;
    }
  `]
})
export class ProfileSettingsComponent { }
