import { Component, <PERSON><PERSON>nit, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StatsOverviewComponent, StatItem } from '../../molecules/stats-overview/stats-overview.component';
import { TableActionsComponent } from '../../molecules/table-actions/table-actions.component';
import { DataTableContainerComponent } from '../../molecules/data-table-container/data-table-container.component';
import { TableColumn } from '../../atoms/data-table/data-table.component';
import { DropdownOption } from '../../atoms/dropdown-select/dropdown-select.component';
import { ButtonComponent } from '../../atoms/button/button.component';
import { SearchInputComponent } from '../../atoms/search-input/search-input.component';
import { Router } from '@angular/router';
import { CompaniesStore, Company } from '../../../../core/state';

@Component({
  selector: 'app-companies-list',
  standalone: true,
  imports: [
    CommonModule,
    StatsOverviewComponent,
    TableActionsComponent,
    DataTableContainerComponent,
    SearchInputComponent
  ],
  template: `
    <div class="companies-container">
      <div class="header-section">
        <div class="title-section">
          <h1 class="page-title">Companies</h1>
          <div class="breadcrumb">
            <span class="breadcrumb-item"><i class="fa fa-home"></i></span>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-item">Application</span>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-item active">Companies List</span>
          </div>
        </div>

        <div class="actions-section">
          <div class="search-container">
            <app-search-input
              [placeholder]="'Search...'"
              (search)="onSearch($event)">
            </app-search-input>
          </div>

          <button class="export-btn" (click)="onExport()">
            <i class="fa fa-download"></i>
            <span>Export</span>
          </button>

          <button class="add-btn" (click)="onAddCompany()">
            <i class="fa fa-plus"></i>
            <span>Add Company</span>
          </button>
        </div>
      </div>

      <app-stats-overview [stats]="stats"></app-stats-overview>

      <div class="companies-list-container">
        <app-table-actions
          [title]="'Companies List'"
          [showSearch]="false"
          [showExport]="false"
          [showAddButton]="false"
          [showDateFilter]="true"
          [startDate]="startDate"
          [endDate]="endDate"
          [filters]="filters"
          (dateRangeChange)="onDateRangeChange($event)"
          (filterChange)="onFilterChange($event)">
        </app-table-actions>

        <app-data-table-container
          [columns]="columns"
          [data]="filteredCompanies"
          [selectable]="true"
          [showPagination]="true"
          [currentPage]="currentPage"
          [pageSize]="pageSize"
          [totalItems]="totalItems"
          (rowClick)="onRowClick($event)"
          (selectionChange)="onSelectionChange($event)"
          (pageChange)="onPageChange($event)"
          (pageSizeChange)="onPageSizeChange($event)">
        </app-data-table-container>
      </div>
    </div>
  `,
  styles: [`
    .companies-container {
      padding: 16px;
    }

    .header-section {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
    }

    .title-section {
      display: flex;
      flex-direction: column;
    }

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #343a40;
      margin: 0 0 8px 0;
    }

    .breadcrumb {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #6c757d;
    }

    .breadcrumb-item {
      display: flex;
      align-items: center;
    }

    .breadcrumb-separator {
      margin: 0 8px;
    }

    .breadcrumb-item.active {
      color: #343a40;
      font-weight: 500;
    }

    .actions-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .search-container {
      width: 240px;
    }

    .export-btn, .add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      border: none;
      gap: 8px;
    }

    .export-btn {
      background-color: #f8f9fa;
      color: #495057;
      border: 1px solid #dee2e6;
    }

    .export-btn:hover {
      background-color: #e9ecef;
    }

    .add-btn {
      background-color: #ff6b35;
      color: white;
    }

    .add-btn:hover {
      background-color: #e85a2a;
    }

    .companies-list-container {
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 16px;
    }

    @media (max-width: 768px) {
      .header-section {
        flex-direction: column;
      }

      .actions-section {
        margin-top: 16px;
        width: 100%;
        flex-wrap: wrap;
      }

      .search-container {
        width: 100%;
        margin-bottom: 12px;
      }

      .export-btn, .add-btn {
        flex: 1;
      }
    }
  `]
})
export class CompaniesListComponent implements OnInit, OnDestroy {
  private companiesStore = inject(CompaniesStore);
  private router = inject(Router);

  // Access state from the store using signals
  companies = this.companiesStore.companies;
  isLoading = this.companiesStore.isLoading;
  error = this.companiesStore.error;
  companiesCount = this.companiesStore.companiesCount;

  // For cleanup
  private watchers: Array<any> = [];
  lastError: string | null = null;

  // Stats data
  get stats(): StatItem[] {
    const totalCompanies = this.companiesCount();
    const activeCompanies = Math.round(totalCompanies * 0.95); // Simulating active companies count
    const inactiveCompanies = totalCompanies - activeCompanies;

    return [
      {
        label: 'Total Companies',
        value: totalCompanies,
        icon: 'fa fa-building',
        backgroundColor: '#ff6b35',
        trendImageUrl: 'assets/images/trends/trend-up-orange.svg'
      },
      {
        label: 'Active Companies',
        value: activeCompanies,
        icon: 'fa fa-check-circle',
        backgroundColor: '#2ecc71',
        trendImageUrl: 'assets/images/trends/trend-up-green.svg'
      },
      {
        label: 'Inactive Companies',
        value: inactiveCompanies,
        icon: 'fa fa-times-circle',
        backgroundColor: '#e74c3c',
        trendImageUrl: 'assets/images/trends/trend-up-red.svg'
      },
      {
        label: 'Company Requests',
        value: Math.round(totalCompanies * 0.2), // Simulating company requests
        icon: 'fa fa-envelope',
        backgroundColor: '#3498db',
        trendImageUrl: 'assets/images/trends/trend-up-blue.svg'
      }
    ];
  }

  // Table columns
  columns: TableColumn[] = [
    { key: 'name', label: 'Company Name', sortable: true },
    { key: 'email', label: 'Email', sortable: true },
    { key: 'url', label: 'Account URL', sortable: true },
    { key: 'plan', label: 'Plan', sortable: true, type: 'badge' },
    { key: 'createdDate', label: 'Created Date', sortable: true, type: 'date' }
  ];

  // Filter options
  filters: {
    name: string;
    options: DropdownOption[];
    selectedValue: string | number;
    placeholder: string;
  }[] = [
    {
      name: 'plan',
      options: [
        { value: '', label: 'Select Plan' },
        { value: 'basic', label: 'Basic (Yearly)' },
        { value: 'advanced', label: 'Advanced (Monthly)' }
      ],
      selectedValue: '',
      placeholder: 'Select Plan'
    },
    {
      name: 'status',
      options: [
        { value: '', label: 'Select Status' },
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' }
      ],
      selectedValue: '',
      placeholder: 'Select Status'
    }
  ];

  // Date range
  startDate: Date = new Date(2025, 0, 5); // Jan 5, 2025
  endDate: Date = new Date(2025, 6, 5);   // Jul 5, 2025

  // Pagination
  currentPage: number = 1;
  pageSize: number = 10;
  totalItems: number = 0;

  // Filtered and selected companies
  filteredCompanies: Company[] = [];
  selectedCompanies: Company[] = [];

  // Search query
  searchQuery: string = '';

  ngOnInit(): void {
    // Load companies from the store
    this.companiesStore.loadCompanies();

    // Set up a watcher for companies changes
    const companiesWatcher = setInterval(() => {
      const currentCompanies = this.companies();
      if (currentCompanies && currentCompanies.length > 0) {
        this.totalItems = currentCompanies.length;
        this.applyFilters();
      }
    }, 100);

    this.watchers.push(companiesWatcher);

    // Set up a watcher for error changes
    const errorWatcher = setInterval(() => {
      const currentError = this.error();
      if (currentError && currentError !== this.lastError) {
        console.error('Error loading companies:', currentError);
        this.lastError = currentError;
        // You could show a toast or notification here
      }
    }, 100);

    this.watchers.push(errorWatcher);
  }

  ngOnDestroy(): void {
    // Clean up all watchers
    this.watchers.forEach(watcherId => clearInterval(watcherId));
  }

  applyFilters(): void {
    const companiesData = this.companies();
    if (!companiesData || companiesData.length === 0) {
      this.filteredCompanies = [];
      this.totalItems = 0;
      return;
    }

    let filtered = [...companiesData];

    // Apply search filter
    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(company =>
        company.name.toLowerCase().includes(query) ||
        company.email.toLowerCase().includes(query) ||
        (company.website && company.website.toLowerCase().includes(query))
      );
    }

    // Apply plan filter (assuming we have a plan field in our Company model)
    const planFilter = this.filters.find(f => f.name === 'plan');
    if (planFilter && planFilter.selectedValue) {
      filtered = filtered.filter(company => {
        // Adapt this to match your actual data model
        const industry = company.industry?.toLowerCase() || '';
        if (planFilter.selectedValue === 'basic') {
          return industry.includes('basic') || industry.includes('standard');
        } else if (planFilter.selectedValue === 'advanced') {
          return industry.includes('advanced') || industry.includes('premium');
        }
        return true;
      });
    }

    // Apply date range filter
    if (this.startDate && this.endDate) {
      filtered = filtered.filter(company => {
        const date = new Date(company.created_at);
        return date >= this.startDate && date <= this.endDate;
      });
    }

    this.totalItems = filtered.length;

    // Apply pagination
    const startIndex = (this.currentPage - 1) * this.pageSize;
    this.filteredCompanies = filtered.slice(startIndex, startIndex + this.pageSize);
  }

  onSearch(query: string): void {
    this.searchQuery = query;
    this.currentPage = 1; // Reset to first page
    this.applyFilters();
  }

  onExport(): void {
    console.log('Exporting companies data...');
    // Implement export functionality
  }

  onAddCompany(): void {
    // Navigate to company create page
    this.router.navigate(['/app/companies/create']);
  }

  onDateRangeChange(dateRange: {start: Date, end: Date}): void {
    this.startDate = dateRange.start;
    this.endDate = dateRange.end;
    this.applyFilters();
  }

  onFilterChange(filter: {name: string, value: string | number}): void {
    const filterIndex = this.filters.findIndex(f => f.name === filter.name);
    if (filterIndex !== -1) {
      this.filters[filterIndex].selectedValue = filter.value;
      this.currentPage = 1; // Reset to first page
      this.applyFilters();
    }
  }

  onRowClick(company: Company): void {
    // Select the company in the store
    this.companiesStore.selectCompany(company);

    // Navigate to company detail page
    this.router.navigate(['/app/companies', company.id]);
  }

  onSelectionChange(selection: Company[]): void {
    this.selectedCompanies = selection;
    console.log('Selected companies:', this.selectedCompanies);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.applyFilters();
  }

  onPageSizeChange(size: number): void {
    this.pageSize = size;
    this.currentPage = 1; // Reset to first page
    this.applyFilters();
  }
}
