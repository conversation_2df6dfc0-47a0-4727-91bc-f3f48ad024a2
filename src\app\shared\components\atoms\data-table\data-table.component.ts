import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  type?: 'text' | 'date' | 'badge' | 'image' | 'custom' | 'location' | 'actions';
  width?: string;
  align?: 'left' | 'center' | 'right';
  icon?: string;
  imageKey?: string; // Key for the image URL when type is 'image'
  actions?: {
    view?: boolean;
    edit?: boolean;
    delete?: boolean;
    custom?: string[];
  };
}

@Component({
  selector: 'app-data-table',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './data-table.component.html',
  styleUrls: ['./data-table.component.scss']
})
export class DataTableComponent implements OnChanges {
  @Input() columns: TableColumn[] = [];
  @Input() data: any[] = [];
  @Input() selectable: boolean = false;
  @Input() customTemplate: any;
  @Input() dynamicColumns: boolean = false; // Whether to generate columns dynamically from data
  @Input() excludeColumns: string[] = []; // Columns to exclude from dynamic generation
  @Input() columnOrder: string[] = []; // Order of columns for dynamic generation
  @Input() columnTypes: {[key: string]: string} = {}; // Types for dynamic columns
  @Input() columnLabels: {[key: string]: string} = {}; // Custom labels for dynamic columns

  // Generated columns based on data
  generatedColumns: TableColumn[] = [];

  /**
   * Lifecycle hook that is called when any data-bound property of a directive changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    // Generate columns when data changes
    if (changes['data'] && this.dynamicColumns) {
      this.generateColumns();
    }
  }

  @Output() rowClick = new EventEmitter<any>();
  @Output() selectionChange = new EventEmitter<any[]>();
  @Output() viewAction = new EventEmitter<any>();
  @Output() editAction = new EventEmitter<any>();
  @Output() deleteAction = new EventEmitter<any>();
  @Output() customAction = new EventEmitter<{action: string, row: any}>();

  selectedRows: any[] = [];
  sortColumn: string = '';
  sortDirection: 'asc' | 'desc' = 'asc';

  get allSelected(): boolean {
    return this.data.length > 0 && this.selectedRows.length === this.data.length;
  }

  toggleSelectAll(): void {
    if (this.allSelected) {
      this.selectedRows = [];
    } else {
      this.selectedRows = [...this.data];
    }
    this.selectionChange.emit(this.selectedRows);
  }

  toggleSelect(row: any, event?: Event): void {
    if (event) {
      event.stopPropagation(); // Prevent row click
    }

    const index = this.selectedRows.findIndex(r => r === row);
    if (index === -1) {
      this.selectedRows.push(row);
    } else {
      this.selectedRows.splice(index, 1);
    }
    this.selectionChange.emit(this.selectedRows);
  }

  isSelected(row: any): boolean {
    return this.selectedRows.includes(row);
  }



  sort(column: string): void {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }

    // Emit sort event or handle sorting internally
  }

  getBadgeType(value: string): string {
    // This is a simple example - customize based on your needs
    if (!value) return 'primary';

    const lowerValue = value.toLowerCase();
    if (lowerValue.includes('active') || lowerValue.includes('success') || lowerValue.includes('hired') || lowerValue.includes('employed') || lowerValue.includes('invited')) {
      return 'success';
    } else if (lowerValue.includes('pending') || lowerValue.includes('warning') || lowerValue.includes('on leave')) {
      return 'warning';
    } else if (lowerValue.includes('error') || lowerValue.includes('danger') || lowerValue.includes('terminated') || lowerValue.includes('absent')) {
      return 'danger';
    }
    return 'primary';
  }

  getCountryCode(location: string): string {
    if (!location) return 'unknown';

    // Extract country code from location string
    // This is a simple example - in a real app, you might use a more sophisticated approach
    const locationMap: {[key: string]: string} = {
      'stockholm': 'se',
      'sweden': 'se',
      'miami': 'us',
      'usa': 'us',
      'united states': 'us',
      'kyiv': 'ua',
      'ukraine': 'ua',
      'ottawa': 'ca',
      'canada': 'ca',
      'sao paulo': 'br',
      'brazil': 'br',
      'london': 'gb',
      'uk': 'gb',
      'united kingdom': 'gb'
    };

    const lowerLocation = location.toLowerCase();
    for (const key in locationMap) {
      if (lowerLocation.includes(key)) {
        return locationMap[key];
      }
    }

    return 'unknown';
  }

  getCountryFlag(location: string): string {
    // Return emoji flag based on country code
    const countryCode = this.getCountryCode(location);
    const flagMap: {[key: string]: string} = {
      'se': '🇸🇪',
      'us': '🇺🇸',
      'ua': '🇺🇦',
      'ca': '🇨🇦',
      'br': '🇧🇷',
      'gb': '🇬🇧',
      'unknown': '🏳️'
    };

    return flagMap[countryCode] || '🏳️';
  }

  /**
   * Handle view action click
   * @param event The click event
   * @param row The row data
   */
  onViewClick(event: Event, row: any): void {
    event.stopPropagation(); // Prevent row click
    this.viewAction.emit(row);
  }

  /**
   * Handle edit action click
   * @param event The click event
   * @param row The row data
   */
  onEditClick(event: Event, row: any): void {
    event.stopPropagation(); // Prevent row click
    this.editAction.emit(row);
  }

  /**
   * Handle delete action click
   * @param event The click event
   * @param row The row data
   */
  onDeleteClick(event: Event, row: any): void {
    event.stopPropagation(); // Prevent row click
    this.deleteAction.emit(row);
  }

  /**
   * Handle custom action click
   * @param event The click event
   * @param action The custom action name
   * @param row The row data
   */
  onCustomClick(event: Event, action: string, row: any): void {
    event.stopPropagation(); // Prevent row click
    this.customAction.emit({ action, row });
  }

  /**
   * Generate columns dynamically from the data
   * This should be called whenever the data changes
   */
  generateColumns(): void {
    if (!this.dynamicColumns || !this.data || this.data.length === 0) {
      return;
    }

    // Get the first item to extract keys
    const firstItem = this.data[0];
    const keys = Object.keys(firstItem);

    // Filter out excluded columns
    const filteredKeys = keys.filter(key => !this.excludeColumns.includes(key));

    // Sort keys based on columnOrder if provided
    let orderedKeys = [...filteredKeys];
    if (this.columnOrder && this.columnOrder.length > 0) {
      // First add keys that are in columnOrder
      orderedKeys = this.columnOrder.filter(key => filteredKeys.includes(key));

      // Then add any remaining keys that weren't in columnOrder
      const remainingKeys = filteredKeys.filter(key => !this.columnOrder.includes(key));
      orderedKeys = [...orderedKeys, ...remainingKeys];
    }

    // Generate columns
    this.generatedColumns = orderedKeys.map(key => {
      // Determine column type based on value or provided types
      let type: string = 'text';

      if (this.columnTypes[key]) {
        // Use provided type
        type = this.columnTypes[key];
      } else {
        // Try to infer type from value
        const value = firstItem[key];
        if (value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))) {
          type = 'date';
        } else if (typeof value === 'string' && (
          value.toLowerCase().includes('active') ||
          value.toLowerCase().includes('inactive') ||
          value.toLowerCase().includes('pending')
        )) {
          type = 'badge';
        } else if (typeof value === 'string' && (
          value.includes('http') &&
          (value.includes('.jpg') || value.includes('.png') || value.includes('.jpeg') || value.includes('/media/'))
        )) {
          type = 'image';
        }
      }

      // Generate label from key (convert snake_case to Title Case)
      const defaultLabel = key
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      // Use custom label if provided
      const label = this.columnLabels[key] || defaultLabel;

      // Create column object
      const column: TableColumn = {
        key,
        label,
        sortable: true,
        type: type as any
      };

      // Add imageKey for image columns if the key doesn't contain 'url'
      if (type === 'image' && !key.includes('url')) {
        // Look for a related URL field
        const urlKey = keys.find(k =>
          k.includes(key) && k.includes('url') ||
          k.includes('image') ||
          k.includes('avatar') ||
          k.includes('photo') ||
          k.includes('picture')
        );

        if (urlKey) {
          column.imageKey = urlKey;
        }
      }

      return column;
    });

    // Add actions column if not excluded
    if (!this.excludeColumns.includes('actions')) {
      this.generatedColumns.push({
        key: 'actions',
        label: 'Actions',
        type: 'actions',
        width: '120px',
        align: 'center',
        actions: {
          view: true,
          edit: true,
          delete: true
        }
      });
    }
  }
}
