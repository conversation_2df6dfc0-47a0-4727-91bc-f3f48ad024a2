<div class="menu-group">
  @if (!collapsed) {
    <div class="menu-group-header">
      <span class="menu-group-title">{{ title }}</span>
    </div>
  }
  <div class="menu-group-items" [ngClass]="{'expanded': true}">
    @for (item of items; track item.label) {
      <app-menu-item
        [icon]="item.icon"
        [label]="item.label"
        [route]="item.route"
        [active]="item.active || false"
        [badge]="item.badge || ''"
        [badgeType]="item.badgeType || 'primary'"
        [hasSubmenu]="item.hasSubmenu || false">
      </app-menu-item>
    }
  </div>
</div>
