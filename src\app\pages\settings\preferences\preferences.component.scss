@import '../../../../styles/variables/_colors';

// Page Header
.page-header {
  background: var(--theme-card-background, $white);
  border: 1px solid var(--theme-card-border, $gray-200);
  border-radius: var(--theme-radius-lg, 12px);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));

  &__content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }
  }

  &__title {
    h1 {
      font-size: 2rem;
      font-weight: 700;
      color: var(--theme-on-background, $gray-900);
      margin: 0 0 0.5rem;
    }

    p {
      color: var(--theme-on-surface, $gray-600);
      margin: 0;
      font-size: 1rem;
    }
  }

  &__actions {
    display: flex;
    gap: 1rem;
    flex-shrink: 0;
  }
}

// Preferences Container
.preferences-container {
  max-width: 1200px;
  margin: 0 auto;
}

// Preferences Form
.preferences-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

// Preferences Section
.preferences-section {
  background: var(--theme-card-background, $white);
  border: 1px solid var(--theme-card-border, $gray-200);
  border-radius: var(--theme-radius-lg, 12px);
  overflow: hidden;
  box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));

  &__header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--theme-divider, $gray-100);
    background: var(--theme-surface-variant, $gray-50);
  }

  &__title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--theme-on-background, $gray-900);
    margin: 0 0 0.5rem;

    i {
      color: var(--theme-primary, $primary);
      font-size: 1.125rem;
    }
  }

  &__description {
    color: var(--theme-on-surface, $gray-600);
    margin: 0;
    font-size: 0.875rem;
  }

  &__content {
    padding: 2rem;
  }
}

// Preference Group
.preference-group {
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }

  &__label {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: var(--theme-on-background, $gray-900);
    margin-bottom: 1rem;
  }
}

// Preference Options
.preference-options {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;

  @media (min-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

// Preference Option
.preference-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  border: 2px solid var(--theme-border, $gray-200);
  border-radius: var(--theme-radius-md, 8px);
  background: var(--theme-surface, $white);
  cursor: pointer;
  transition: all var(--theme-transition-normal, 250ms) ease;

  &:hover {
    border-color: var(--theme-primary, $primary);
    background: rgba(var(--theme-primary-rgb, 59, 130, 246), 0.05);
    transform: translateY(-1px);
    box-shadow: var(--theme-shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
  }

  &--active {
    border-color: var(--theme-primary, $primary);
    background: rgba(var(--theme-primary-rgb, 59, 130, 246), 0.1);
    box-shadow: var(--theme-shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));

    .preference-option__icon {
      background: var(--theme-primary, $primary);
      color: var(--theme-on-primary, $white);
    }
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: var(--theme-radius-md, 8px);
    background: var(--theme-surface-variant, $gray-100);
    color: var(--theme-on-surface, $gray-600);
    font-size: 1.25rem;
    flex-shrink: 0;
    transition: all var(--theme-transition-normal, 250ms) ease;
  }

  &__content {
    flex: 1;
  }

  &__title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--theme-on-background, $gray-900);
    margin: 0 0 0.25rem;
  }

  &__description {
    font-size: 0.875rem;
    color: var(--theme-on-surface, $gray-600);
    margin: 0;
    line-height: 1.4;
  }

  &__check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    flex-shrink: 0;

    i {
      color: var(--theme-primary, $primary);
      font-size: 1rem;
    }
  }
}

// Preference Toggle
.preference-toggle {
  .toggle-switch {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    user-select: none;

    &__input {
      position: absolute;
      opacity: 0;
      width: 0;
      height: 0;

      &:checked + .toggle-switch__slider {
        background: var(--theme-primary, $primary);

        &::before {
          transform: translateX(1.5rem);
        }
      }

      &:focus + .toggle-switch__slider {
        box-shadow: 0 0 0 2px rgba(var(--theme-primary-rgb, 59, 130, 246), 0.2);
      }
    }

    &__slider {
      position: relative;
      display: inline-block;
      width: 3rem;
      height: 1.5rem;
      background: var(--theme-border, $gray-300);
      border-radius: 1.5rem;
      transition: all var(--theme-transition-normal, 250ms) ease;

      &::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 1.25rem;
        height: 1.25rem;
        background: var(--theme-on-primary, $white);
        border-radius: 50%;
        transition: all var(--theme-transition-normal, 250ms) ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
      }
    }

    &__label {
      font-size: 0.875rem;
      color: var(--theme-on-surface, $gray-700);
    }
  }
}

// Settings Summary
.settings-summary {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));

  &__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--theme-surface-variant, $gray-50);
    border-radius: var(--theme-radius-md, 8px);
    border: 1px solid var(--theme-divider, $gray-100);
  }

  &__label {
    font-weight: 500;
    color: var(--theme-on-surface, $gray-700);
  }

  &__value {
    font-weight: 600;
    color: var(--theme-primary, $primary);
  }
}

// Layout Preview
.layout-preview {
  &__container {
    position: relative;
    width: 100%;
    height: 200px;
    border: 2px solid var(--theme-border, $gray-200);
    border-radius: var(--theme-radius-md, 8px);
    overflow: hidden;
    background: var(--theme-background, $white);
    transition: all var(--theme-transition-normal, 250ms) ease;

    &[data-theme="dark"] {
      background: var(--theme-background, #0f172a);
      border-color: var(--theme-border, #475569);
    }
  }

  &__header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: var(--theme-primary, $primary);
    color: var(--theme-on-primary, $white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 3;

    .layout-preview__container[data-header-position="static"] & {
      position: relative;
    }

    .layout-preview__container[data-header-position="sticky"] & {
      position: sticky;
    }
  }

  &__sidebar {
    position: absolute;
    top: 40px;
    left: 0;
    width: 60px;
    bottom: 0;
    background: var(--theme-surface, $gray-100);
    border-right: 1px solid var(--theme-border, $gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: var(--theme-on-surface, $gray-600);
    transition: all var(--theme-transition-normal, 250ms) ease;
    z-index: 2;

    &--collapsed {
      width: 20px;
    }

    &--right {
      left: auto;
      right: 0;
      border-right: none;
      border-left: 1px solid var(--theme-border, $gray-200);
    }

    .layout-preview__container[data-layout-type="horizontal"] & {
      top: 40px;
      left: 0;
      right: 0;
      width: auto;
      height: 30px;
      bottom: auto;
      border-right: none;
      border-bottom: 1px solid var(--theme-border, $gray-200);
    }

    .layout-preview__container[data-theme="dark"] & {
      background: var(--theme-surface, #1e293b);
      border-color: var(--theme-border, #475569);
    }
  }

  &__content {
    position: absolute;
    top: 40px;
    left: 60px;
    right: 0;
    bottom: 0;
    background: var(--theme-surface-variant, $gray-50);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: var(--theme-on-surface, $gray-600);
    transition: all var(--theme-transition-normal, 250ms) ease;

    .layout-preview__container[data-layout-type="horizontal"] & {
      top: 70px;
      left: 0;
    }

    .layout-preview__sidebar--collapsed + & {
      left: 20px;
    }

    .layout-preview__sidebar--right ~ & {
      left: 0;
      right: 60px;
    }

    .layout-preview__sidebar--right.layout-preview__sidebar--collapsed ~ & {
      right: 20px;
    }

    .layout-preview__container[data-theme="dark"] & {
      background: var(--theme-surface-variant, #334155);
    }
  }
}

// Button Styles
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: var(--theme-radius-md, 8px);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--theme-transition-normal, 250ms) ease;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--theme-primary-rgb, 59, 130, 246), 0.2);
  }

  &.btn-outline-secondary {
    color: var(--theme-on-surface, $gray-700);
    border-color: var(--theme-border, $gray-300);
    background: var(--theme-surface, $white);

    &:hover {
      background: var(--theme-surface-variant, $gray-50);
      border-color: var(--theme-primary, $primary);
      color: var(--theme-primary, $primary);
    }
  }
}

// Dark theme specific styles
:host-context(.theme-dark) {
  .page-header {
    background: var(--theme-card-background, #1e293b);
    border-color: var(--theme-card-border, #475569);
  }

  .preferences-section {
    background: var(--theme-card-background, #1e293b);
    border-color: var(--theme-card-border, #475569);

    &__header {
      background: var(--theme-surface-variant, #334155);
      border-bottom-color: var(--theme-divider, #475569);
    }
  }

  .preference-option {
    background: var(--theme-surface, #1e293b);
    border-color: var(--theme-border, #475569);

    &:hover {
      background: rgba(var(--theme-primary-rgb, 96, 165, 250), 0.1);
    }

    &--active {
      background: rgba(var(--theme-primary-rgb, 96, 165, 250), 0.15);
    }

    &__icon {
      background: var(--theme-surface-variant, #334155);
    }
  }

  .settings-summary__item {
    background: var(--theme-surface-variant, #334155);
    border-color: var(--theme-divider, #475569);
  }

  .btn.btn-outline-secondary {
    background: var(--theme-surface, #1e293b);
    border-color: var(--theme-border, #475569);
    color: var(--theme-on-surface, #e2e8f0);

    &:hover {
      background: var(--theme-surface-variant, #334155);
    }
  }
}
