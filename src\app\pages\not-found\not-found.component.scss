@import '../../../styles/variables/colors';

.landing-container {
  font-family: 'Poppins', sans-serif;
  color: #343a40;
  overflow-x: hidden;
}

/* Header Navigation */
.landing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 5%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.header-logo {
  display: flex;
  align-items: center;
}

.logo-link {
  display: block;
  text-decoration: none;
}

.header-logo-text {
  font-size: 1.8rem;
  font-weight: 700;
  color: #343a40;
  transition: transform 0.3s ease;
  display: inline-block;
}

.header-logo-text .highlight {
  color: #ff6b35;
}

.logo-link:hover .header-logo-text {
  transform: scale(1.05);
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.nav-link {
  color: #343a40;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: color 0.3s;
}

.nav-link:hover {
  color: #ff6b35;
}

.btn-login {
  padding: 0.6rem 1.5rem;
  background-color: transparent;
  color: #ff6b35;
  border: 2px solid #ff6b35;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
}

.btn-login:hover {
  background-color: #ff6b35;
  color: white;
}

.btn-signup {
  padding: 0.6rem 1.5rem;
  background-color: #ff6b35;
  color: white;
  border: 2px solid #ff6b35;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
}

.btn-signup:hover {
  background-color: #e85a2a;
  border-color: #e85a2a;
}

/* Error Section */
.error-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 8rem 5% 5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  text-align: center;
}

.error-content {
  max-width: 800px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.error-code {
  font-size: 12rem;
  font-weight: 800;
  color: #ff6b35;
  line-height: 1;
  margin-bottom: 1rem;
  text-shadow: 0 4px 8px rgba(255, 107, 53, 0.2);
}

.error-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  color: #343a40;
}

.error-description {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #6c757d;
  margin-bottom: 2.5rem;
  max-width: 600px;
}

.error-buttons {
  display: flex;
  gap: 1rem;
}

.btn-primary {
  padding: 0.8rem 2rem;
  background-color: #ff6b35;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary:hover {
  background-color: #e85a2a;
}

.btn-secondary {
  padding: 0.8rem 2rem;
  background-color: transparent;
  color: #ff6b35;
  border: 2px solid #ff6b35;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-secondary:hover {
  background-color: rgba(255, 107, 53, 0.1);
}

/* Footer */
.landing-footer {
  background-color: #212529;
  color: #adb5bd;
  padding: 4rem 5% 2rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-logo {
  flex: 1;
  min-width: 250px;
}

.footer-logo-text {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  margin: 0 0 1rem 0;
}

.footer-logo-text .highlight {
  color: #ff6b35;
}

.footer-logo p {
  margin: 0;
}

.footer-links {
  flex: 2;
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.link-group {
  flex: 1;
  min-width: 150px;
}

.link-group h4 {
  color: white;
  margin: 0 0 1rem 0;
}

.link-group ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.link-group li {
  margin-bottom: 0.5rem;
}

.link-group a {
  color: #adb5bd;
  text-decoration: none;
  transition: color 0.3s;
}

.link-group a:hover {
  color: white;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid #495057;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social a {
  color: #adb5bd;
  font-size: 1.2rem;
  transition: color 0.3s;
}

.footer-social a:hover {
  color: white;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .error-section {
    padding-top: 6rem;
    padding-bottom: 2rem;
  }

  .error-title {
    font-size: 2.5rem;
  }

  .error-code {
    font-size: 8rem;
  }
}

@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 2rem;
  }

  .error-buttons {
    flex-direction: column;
    width: 100%;
  }

  .btn-primary, .btn-secondary {
    width: 100%;
  }

  .error-code {
    font-size: 6rem;
  }

  .error-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .error-code {
    font-size: 5rem;
  }

  .error-title {
    font-size: 1.8rem;
  }

  .error-description {
    font-size: 1rem;
  }
}


