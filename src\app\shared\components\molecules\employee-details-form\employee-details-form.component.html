<form [formGroup]="employeeDetailsForm" (ngSubmit)="onSubmit()" class="employee-details-form">
  <!-- Section Navigation -->
  <div class="employee-details-form__navigation">
    <div class="employee-details-form__nav-tabs">
      <button
        type="button"
        *ngFor="let section of sections"
        class="employee-details-form__nav-tab"
        [class.employee-details-form__nav-tab--active]="activeSection === section.id"
        (click)="setActiveSection(section.id)">
        <span class="employee-details-form__nav-icon">{{ section.icon }}</span>
        <span class="employee-details-form__nav-label">{{ section.label }}</span>
      </button>
    </div>
  </div>

  <!-- Form Sections -->
  <div class="employee-details-form__content">
    
    <!-- Personal Information Section -->
    <div class="employee-details-form__section" *ngIf="activeSection === 'personal'">
      <h3 class="employee-details-form__section-title">Personal Information</h3>
      <div class="employee-details-form__grid employee-details-form__grid--2-col">
        
        <!-- Blood Group -->
        <div class="employee-details-form__field">
          <label for="blood_group" class="employee-details-form__label">Blood Group</label>
          <select
            id="blood_group"
            class="employee-details-form__select"
            formControlName="blood_group"
            [disabled]="isLoading">
            <option value="">Select blood group</option>
            <option *ngFor="let group of bloodGroups" [value]="group">{{ group }}</option>
          </select>
        </div>

        <!-- Marital Status -->
        <div class="employee-details-form__field">
          <app-marital-status-select
            formControlName="marital_status"
            label="Marital Status"
            placeholder="Select marital status"
            [disabled]="isLoading"
            [error]="getFieldError('marital_status')">
          </app-marital-status-select>
        </div>

        <!-- Nationality -->
        <div class="employee-details-form__field">
          <label for="nationality" class="employee-details-form__label">Nationality</label>
          <input
            id="nationality"
            type="text"
            class="employee-details-form__input"
            [class.employee-details-form__input--error]="getFieldError('nationality')"
            formControlName="nationality"
            placeholder="Enter nationality"
            [disabled]="isLoading">
          <div class="employee-details-form__error" *ngIf="getFieldError('nationality')">
            {{ getFieldError('nationality') }}
          </div>
        </div>
      </div>
    </div>

    <!-- Emergency Contact Section -->
    <div class="employee-details-form__section" *ngIf="activeSection === 'emergency'">
      <h3 class="employee-details-form__section-title">Emergency Contact Information</h3>
      <div class="employee-details-form__grid employee-details-form__grid--2-col">
        
        <!-- Emergency Contact Name -->
        <div class="employee-details-form__field">
          <label for="emergency_contact_name" class="employee-details-form__label">Contact Name</label>
          <input
            id="emergency_contact_name"
            type="text"
            class="employee-details-form__input"
            formControlName="emergency_contact_name"
            placeholder="Enter emergency contact name"
            [disabled]="isLoading">
        </div>

        <!-- Emergency Contact Phone -->
        <div class="employee-details-form__field">
          <label for="emergency_contact_phone" class="employee-details-form__label">Contact Phone</label>
          <input
            id="emergency_contact_phone"
            type="tel"
            class="employee-details-form__input"
            [class.employee-details-form__input--error]="getFieldError('emergency_contact_phone')"
            formControlName="emergency_contact_phone"
            placeholder="Enter emergency contact phone"
            [disabled]="isLoading">
          <div class="employee-details-form__error" *ngIf="getFieldError('emergency_contact_phone')">
            {{ getFieldError('emergency_contact_phone') }}
          </div>
        </div>

        <!-- Relationship -->
        <div class="employee-details-form__field">
          <label for="emergency_contact_relationship" class="employee-details-form__label">Relationship</label>
          <select
            id="emergency_contact_relationship"
            class="employee-details-form__select"
            formControlName="emergency_contact_relationship"
            [disabled]="isLoading">
            <option value="">Select relationship</option>
            <option *ngFor="let relationship of relationships" [value]="relationship">{{ relationship }}</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Government Documents Section -->
    <div class="employee-details-form__section" *ngIf="activeSection === 'documents'">
      <h3 class="employee-details-form__section-title">Government Documents</h3>
      <div class="employee-details-form__grid employee-details-form__grid--2-col">
        
        <!-- Passport Information -->
        <div class="employee-details-form__field">
          <label for="passport_number" class="employee-details-form__label">Passport Number</label>
          <input
            id="passport_number"
            type="text"
            class="employee-details-form__input"
            formControlName="passport_number"
            placeholder="Enter passport number"
            [disabled]="isLoading">
        </div>

        <div class="employee-details-form__field">
          <app-date-input
            formControlName="passport_expiry_date"
            label="Passport Expiry Date"
            placeholder="Select passport expiry date"
            [disabled]="isLoading"
            [error]="getFieldError('passport_expiry_date')"
            [min]="maxDate">
          </app-date-input>
        </div>

        <!-- Visa Information -->
        <div class="employee-details-form__field">
          <label for="visa_status" class="employee-details-form__label">Visa Status</label>
          <input
            id="visa_status"
            type="text"
            class="employee-details-form__input"
            formControlName="visa_status"
            placeholder="Enter visa status"
            [disabled]="isLoading">
        </div>

        <div class="employee-details-form__field">
          <app-date-input
            formControlName="visa_expiry_date"
            label="Visa Expiry Date"
            placeholder="Select visa expiry date"
            [disabled]="isLoading"
            [error]="getFieldError('visa_expiry_date')"
            [min]="maxDate">
          </app-date-input>
        </div>

        <!-- Work Permit -->
        <div class="employee-details-form__field">
          <label for="work_permit_number" class="employee-details-form__label">Work Permit Number</label>
          <input
            id="work_permit_number"
            type="text"
            class="employee-details-form__input"
            formControlName="work_permit_number"
            placeholder="Enter work permit number"
            [disabled]="isLoading">
        </div>

        <div class="employee-details-form__field">
          <app-date-input
            formControlName="work_permit_expiry_date"
            label="Work Permit Expiry Date"
            placeholder="Select work permit expiry date"
            [disabled]="isLoading"
            [error]="getFieldError('work_permit_expiry_date')"
            [min]="maxDate">
          </app-date-input>
        </div>

        <!-- Tax Information -->
        <div class="employee-details-form__field">
          <label for="social_security_number" class="employee-details-form__label">Social Security Number</label>
          <input
            id="social_security_number"
            type="text"
            class="employee-details-form__input"
            formControlName="social_security_number"
            placeholder="Enter SSN"
            [disabled]="isLoading">
        </div>

        <div class="employee-details-form__field">
          <label for="tax_id" class="employee-details-form__label">Tax ID</label>
          <input
            id="tax_id"
            type="text"
            class="employee-details-form__input"
            formControlName="tax_id"
            placeholder="Enter tax ID"
            [disabled]="isLoading">
        </div>
      </div>
    </div>

    <!-- Banking Information Section -->
    <div class="employee-details-form__section" *ngIf="activeSection === 'banking'">
      <h3 class="employee-details-form__section-title">Banking Information</h3>
      <div class="employee-details-form__grid employee-details-form__grid--2-col">
        
        <div class="employee-details-form__field">
          <label for="bank_name" class="employee-details-form__label">Bank Name</label>
          <input
            id="bank_name"
            type="text"
            class="employee-details-form__input"
            formControlName="bank_name"
            placeholder="Enter bank name"
            [disabled]="isLoading">
        </div>

        <div class="employee-details-form__field">
          <label for="bank_branch" class="employee-details-form__label">Bank Branch</label>
          <input
            id="bank_branch"
            type="text"
            class="employee-details-form__input"
            formControlName="bank_branch"
            placeholder="Enter bank branch"
            [disabled]="isLoading">
        </div>

        <div class="employee-details-form__field">
          <label for="bank_account_number" class="employee-details-form__label">Account Number</label>
          <input
            id="bank_account_number"
            type="text"
            class="employee-details-form__input"
            formControlName="bank_account_number"
            placeholder="Enter account number"
            [disabled]="isLoading">
        </div>

        <div class="employee-details-form__field">
          <label for="bank_routing_number" class="employee-details-form__label">Routing Number</label>
          <input
            id="bank_routing_number"
            type="text"
            class="employee-details-form__input"
            formControlName="bank_routing_number"
            placeholder="Enter routing number"
            [disabled]="isLoading">
        </div>
      </div>
    </div>

    <!-- Address Information Section -->
    <div class="employee-details-form__section" *ngIf="activeSection === 'address'">
      <h3 class="employee-details-form__section-title">Address Information</h3>
      
      <!-- Current Address -->
      <div class="employee-details-form__subsection">
        <h4 class="employee-details-form__subsection-title">Current Address</h4>
        <div class="employee-details-form__grid employee-details-form__grid--2-col">
          <div class="employee-details-form__field employee-details-form__field--full-width">
            <label for="current_address" class="employee-details-form__label">Address</label>
            <input
              id="current_address"
              type="text"
              class="employee-details-form__input"
              formControlName="current_address"
              placeholder="Enter current address"
              [disabled]="isLoading">
          </div>

          <div class="employee-details-form__field">
            <label for="current_city" class="employee-details-form__label">City</label>
            <input
              id="current_city"
              type="text"
              class="employee-details-form__input"
              formControlName="current_city"
              placeholder="Enter city"
              [disabled]="isLoading">
          </div>

          <div class="employee-details-form__field">
            <label for="current_state" class="employee-details-form__label">State</label>
            <input
              id="current_state"
              type="text"
              class="employee-details-form__input"
              formControlName="current_state"
              placeholder="Enter state"
              [disabled]="isLoading">
          </div>

          <div class="employee-details-form__field">
            <label for="current_zip" class="employee-details-form__label">ZIP Code</label>
            <input
              id="current_zip"
              type="text"
              class="employee-details-form__input"
              [class.employee-details-form__input--error]="getFieldError('current_zip')"
              formControlName="current_zip"
              placeholder="Enter ZIP code"
              [disabled]="isLoading">
            <div class="employee-details-form__error" *ngIf="getFieldError('current_zip')">
              {{ getFieldError('current_zip') }}
            </div>
          </div>

          <div class="employee-details-form__field">
            <label for="current_country" class="employee-details-form__label">Country</label>
            <input
              id="current_country"
              type="text"
              class="employee-details-form__input"
              formControlName="current_country"
              placeholder="Enter country"
              [disabled]="isLoading">
          </div>
        </div>
      </div>

      <!-- Address Copy Actions -->
      <div class="employee-details-form__address-actions">
        <app-button
          type="button"
          variant="secondary"
          size="sm"
          (click)="copyCurrentToPermanent()"
          [disabled]="isLoading">
          Copy Current to Permanent
        </app-button>
        <app-button
          type="button"
          variant="secondary"
          size="sm"
          (click)="copyPermanentToCurrent()"
          [disabled]="isLoading">
          Copy Permanent to Current
        </app-button>
      </div>

      <!-- Permanent Address -->
      <div class="employee-details-form__subsection">
        <h4 class="employee-details-form__subsection-title">Permanent Address</h4>
        <div class="employee-details-form__grid employee-details-form__grid--2-col">
          <div class="employee-details-form__field employee-details-form__field--full-width">
            <label for="permanent_address" class="employee-details-form__label">Address</label>
            <input
              id="permanent_address"
              type="text"
              class="employee-details-form__input"
              formControlName="permanent_address"
              placeholder="Enter permanent address"
              [disabled]="isLoading">
          </div>

          <div class="employee-details-form__field">
            <label for="permanent_city" class="employee-details-form__label">City</label>
            <input
              id="permanent_city"
              type="text"
              class="employee-details-form__input"
              formControlName="permanent_city"
              placeholder="Enter city"
              [disabled]="isLoading">
          </div>

          <div class="employee-details-form__field">
            <label for="permanent_state" class="employee-details-form__label">State</label>
            <input
              id="permanent_state"
              type="text"
              class="employee-details-form__input"
              formControlName="permanent_state"
              placeholder="Enter state"
              [disabled]="isLoading">
          </div>

          <div class="employee-details-form__field">
            <label for="permanent_zip" class="employee-details-form__label">ZIP Code</label>
            <input
              id="permanent_zip"
              type="text"
              class="employee-details-form__input"
              [class.employee-details-form__input--error]="getFieldError('permanent_zip')"
              formControlName="permanent_zip"
              placeholder="Enter ZIP code"
              [disabled]="isLoading">
            <div class="employee-details-form__error" *ngIf="getFieldError('permanent_zip')">
              {{ getFieldError('permanent_zip') }}
            </div>
          </div>

          <div class="employee-details-form__field">
            <label for="permanent_country" class="employee-details-form__label">Country</label>
            <input
              id="permanent_country"
              type="text"
              class="employee-details-form__input"
              formControlName="permanent_country"
              placeholder="Enter country"
              [disabled]="isLoading">
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Information Section -->
    <div class="employee-details-form__section" *ngIf="activeSection === 'additional'">
      <h3 class="employee-details-form__section-title">Additional Information</h3>
      <div class="employee-details-form__field">
        <label for="notes" class="employee-details-form__label">Notes</label>
        <textarea
          id="notes"
          class="employee-details-form__textarea"
          formControlName="notes"
          placeholder="Enter any additional notes or information"
          rows="6"
          [disabled]="isLoading">
        </textarea>
      </div>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="employee-details-form__actions">
    <app-button
      type="button"
      variant="secondary"
      [disabled]="isLoading"
      (click)="onCancel()">
      Cancel
    </app-button>

    <app-button
      type="button"
      variant="warning"
      [disabled]="isLoading"
      (click)="onReset()">
      Reset
    </app-button>

    <app-button
      type="submit"
      variant="primary"
      [disabled]="!isFormValid || isLoading">
      {{ isEdit ? 'Update Details' : 'Save Details' }}
    </app-button>
  </div>
</form>
