<div class="employee-page">
  <!-- Page Header -->
  <div class="employee-page__header">
    <div class="employee-page__header-content">
      <!-- Back Button -->
      <div class="employee-page__back" *ngIf="showBackButton">
        <app-button
          variant="secondary"
          (click)="onViewChange('list')">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m15 18-6-6 6-6"/>
          </svg>
          Back to List
        </app-button>
      </div>

      <!-- Title Section -->
      <div class="employee-page__title-section">
        <h1 class="employee-page__title">{{ pageTitle }}</h1>
        <p class="employee-page__subtitle">{{ pageSubtitle }}</p>
      </div>

      <!-- Actions -->
      <div class="employee-page__actions" *ngIf="showActions && currentView === 'list'">
        <app-button
          variant="secondary"
          (click)="onRefresh()"
          [disabled]="isLoading">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
            <path d="M21 3v5h-5"/>
            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
            <path d="M3 21v-5h5"/>
          </svg>
          Refresh
        </app-button>

        <app-button
          variant="primary"
          (click)="onCreateEmployee()"
          [disabled]="isLoading">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
            <circle cx="9" cy="7" r="4"/>
            <line x1="19" y1="8" x2="19" y2="14"/>
            <line x1="22" y1="11" x2="16" y2="11"/>
          </svg>
          Add Employee
        </app-button>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="employee-page__search" *ngIf="showSearch && currentView === 'list'">
      <app-search-input
        placeholder="Search employees by name, email, or department..."
        (searchChange)="onSearch($event)"
        [disabled]="isLoading">
      </app-search-input>
    </div>
  </div>

  <!-- Statistics Overview -->
  <div class="employee-page__stats" *ngIf="showStats && currentView === 'list'">
    <app-stats-overview
      [stats]="statsData"
      [loading]="isLoading">
    </app-stats-overview>
  </div>

  <!-- Error Message -->
  <div class="employee-page__error" *ngIf="error" role="alert">
    <div class="employee-page__error-content">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="10"/>
        <line x1="12" y1="8" x2="12" y2="12"/>
        <line x1="12" y1="16" x2="12.01" y2="16"/>
      </svg>
      <span>{{ error }}</span>
    </div>
  </div>

  <!-- Main Content -->
  <div class="employee-page__content">
    <!-- Employee List View -->
    <div class="employee-page__list" *ngIf="currentView === 'list'">
      <app-employees-list
        [employees]="employees"
        [isLoading]="isLoading"
        (employeeEdit)="onEditEmployee($event)"
        (employeeView)="onViewEmployee($event)"
        (employeeDelete)="onDeleteEmployee($event)"
        (employeeManageDetails)="onManageEmployeeDetails($event)">
      </app-employees-list>
    </div>

    <!-- Employee Form View (Create/Edit) -->
    <div class="employee-page__form" *ngIf="isFormView">
      <div class="employee-page__form-container">
        <app-employee-form
          [employee]="selectedEmployee"
          [departments]="departments"
          [designations]="designations"
          [isLoading]="isLoading"
          [isEdit]="isEditMode"
          [showAdvancedFields]="true"
          (formSubmit)="onFormSubmit($event)"
          (formCancel)="onFormCancel()">
        </app-employee-form>
      </div>
    </div>

    <!-- Employee Detail View -->
    <div class="employee-page__detail" *ngIf="currentView === 'detail' && selectedEmployee">
      <div class="employee-page__detail-container">
        <!-- Employee Profile Card -->
        <div class="employee-page__profile-card">
          <div class="employee-page__profile-header">
            <div class="employee-page__profile-avatar">
              <img 
                [src]="selectedEmployee.profile_picture_url || '/assets/images/default-avatar.png'" 
                [alt]="selectedEmployee.first_name + ' ' + selectedEmployee.last_name"
                class="employee-page__avatar-image">
            </div>
            <div class="employee-page__profile-info">
              <h2 class="employee-page__profile-name">
                {{ selectedEmployee.first_name }} {{ selectedEmployee.last_name }}
              </h2>
              <p class="employee-page__profile-title">
                {{ selectedEmployee.designation?.name || selectedEmployee.position }}
              </p>
              <p class="employee-page__profile-department">
                {{ selectedEmployee.department?.name || selectedEmployee.department_name }}
              </p>
            </div>
            <div class="employee-page__profile-actions">
              <app-button
                variant="secondary"
                (click)="onManageEmployeeDetails(selectedEmployee)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                Manage Details
              </app-button>
              <app-button
                variant="primary"
                (click)="onEditEmployee(selectedEmployee)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                  <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                </svg>
                Edit Employee
              </app-button>
            </div>
          </div>

          <!-- Employee Details -->
          <div class="employee-page__profile-details">
            <div class="employee-page__detail-grid">
              <div class="employee-page__detail-item" *ngIf="selectedEmployee.email">
                <label>Email</label>
                <span>{{ selectedEmployee.email }}</span>
              </div>
              <div class="employee-page__detail-item" *ngIf="selectedEmployee.phone">
                <label>Phone</label>
                <span>{{ selectedEmployee.phone }}</span>
              </div>
              <div class="employee-page__detail-item" *ngIf="selectedEmployee.date_of_birth">
                <label>Date of Birth</label>
                <span>{{ selectedEmployee.date_of_birth | date:'mediumDate' }}</span>
              </div>
              <div class="employee-page__detail-item" *ngIf="selectedEmployee.gender">
                <label>Gender</label>
                <span>{{ selectedEmployee.gender | titlecase }}</span>
              </div>
              <div class="employee-page__detail-item" *ngIf="selectedEmployee.status">
                <label>Status</label>
                <span class="employee-page__status-badge" [attr.data-status]="selectedEmployee.status">
                  {{ selectedEmployee.status | titlecase }}
                </span>
              </div>
              <div class="employee-page__detail-item" *ngIf="selectedEmployee.created_at">
                <label>Joined Date</label>
                <span>{{ selectedEmployee.created_at | date:'mediumDate' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Employee Details Manager View -->
    <div class="employee-page__details-manager" *ngIf="isDetailsManagerView && selectedEmployee">
      <app-employee-details-manager
        [employee]="selectedEmployee"
        [initialView]="'overview'"
        (viewChange)="onViewChange($event)"
        (close)="onViewChange('list')">
      </app-employee-details-manager>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="employee-page__loading" *ngIf="isLoading">
    <div class="employee-page__loading-spinner">
      <div class="employee-page__spinner"></div>
      <p>Loading...</p>
    </div>
  </div>
</div>
