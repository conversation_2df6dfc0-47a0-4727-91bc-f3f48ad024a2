import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'app-pagination',
  standalone: true,
  imports: [CommonModule, IconComponent],
  template: `
    <div class="pagination-container">
      <div class="pagination-info">
        <span>Row Per Page:</span>
        <select class="page-size-select" [value]="pageSize" (change)="onPageSizeChange($event)">
          <option *ngFor="let size of pageSizeOptions" [value]="size">{{ size }}</option>
        </select>
        <span class="entries-label">Entries</span>
      </div>

      <div class="pagination-controls">
        <button
          class="pagination-button"
          [disabled]="currentPage === 1"
          (click)="onPageChange(currentPage - 1)">
          <app-icon name="fa fa-chevron-left" size="sm"></app-icon>
        </button>

        <ng-container *ngFor="let page of visiblePages">
          <button
            *ngIf="page !== '...'"
            class="pagination-button"
            [ngClass]="{'active': currentPage === page}"
            (click)="onPageChange(+page)">
            {{ page }}
          </button>
          <span *ngIf="page === '...'" class="pagination-ellipsis">...</span>
        </ng-container>

        <button
          class="pagination-button"
          [disabled]="currentPage === totalPages"
          (click)="onPageChange(currentPage + 1)">
          <app-icon name="fa fa-chevron-right" size="sm"></app-icon>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .pagination-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      font-size: 14px;
      border-top: 1px solid #f0f0f0;
    }

    .pagination-info {
      display: flex;
      align-items: center;
      color: #4b5563;
      font-weight: 500;
    }

    .page-size-select {
      margin: 0 8px;
      padding: 6px 12px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      font-size: 14px;
      color: #4b5563;
      background-color: #f9fafb;
      cursor: pointer;
      transition: all 0.2s ease;
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%234b5563' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 10px center;
      padding-right: 30px;
    }

    .page-size-select:hover {
      border-color: #d1d5db;
      background-color: #f3f4f6;
    }

    .page-size-select:focus {
      outline: none;
      border-color: #4f46e5;
      box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
    }

    .entries-label {
      margin-left: 4px;
      color: #6b7280;
    }

    .pagination-controls {
      display: flex;
      align-items: center;
    }

    .pagination-button {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 36px;
      height: 36px;
      margin: 0 4px;
      padding: 0 8px;
      background-color: #fff;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      color: #4b5563;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .pagination-button:hover:not(:disabled):not(.active) {
      background-color: #f9fafb;
      border-color: #d1d5db;
      transform: translateY(-1px);
    }

    .pagination-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .pagination-button.active {
      background-color: #4f46e5;
      border-color: #4f46e5;
      color: #fff;
      box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
    }

    .pagination-ellipsis {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 32px;
      height: 32px;
      margin: 0 4px;
      color: #6b7280;
    }
  `]
})
export class PaginationComponent {
  @Input() currentPage: number = 1;
  @Input() pageSize: number = 10;
  @Input() totalItems: number = 0;
  @Input() pageSizeOptions: number[] = [10, 25, 50, 100];

  @Output() pageChange = new EventEmitter<number>();
  @Output() pageSizeChange = new EventEmitter<number>();

  get totalPages(): number {
    return Math.ceil(this.totalItems / this.pageSize);
  }

  get visiblePages(): (number | string)[] {
    const pages: (number | string)[] = [];

    if (this.totalPages <= 7) {
      // Show all pages if there are 7 or fewer
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (this.currentPage > 3) {
        pages.push('...');
      }

      // Show pages around current page
      const start = Math.max(2, this.currentPage - 1);
      const end = Math.min(this.totalPages - 1, this.currentPage + 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (this.currentPage < this.totalPages - 2) {
        pages.push('...');
      }

      // Always show last page
      if (this.totalPages > 1) {
        pages.push(this.totalPages);
      }
    }

    return pages;
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.pageChange.emit(page);
    }
  }

  onPageSizeChange(event: Event): void {
    const select = event.target as HTMLSelectElement;
    const newSize = parseInt(select.value, 10);
    if (newSize !== this.pageSize) {
      this.pageSizeChange.emit(newSize);
    }
  }
}
