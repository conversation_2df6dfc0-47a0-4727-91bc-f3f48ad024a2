<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>HR Modern Illustration</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="gradient-orange">
            <stop stop-color="#FF8A65" offset="0%"></stop>
            <stop stop-color="#FF6B35" offset="50%"></stop>
            <stop stop-color="#E85A2A" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="gradient-dark">
            <stop stop-color="#495057" offset="0%"></stop>
            <stop stop-color="#343A40" offset="50%"></stop>
            <stop stop-color="#212529" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="gradient-light">
            <stop stop-color="#F8F9FA" offset="0%"></stop>
            <stop stop-color="#E9ECEF" offset="100%"></stop>
        </linearGradient>
        <filter x="-15.0%" y="-15.0%" width="130.0%" height="130.0%" filterUnits="objectBoundingBox" id="filter-blur">
            <feGaussianBlur stdDeviation="15" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="HR-Modern-Illustration" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background Elements -->
        <rect id="Background" fill="#FFFFFF" x="0" y="0" width="800" height="600"></rect>
        
        <!-- Abstract Background Shapes -->
        <circle id="Blob-1" fill="url(#gradient-orange)" opacity="0.1" filter="url(#filter-blur)" cx="200" cy="200" r="150"></circle>
        <circle id="Blob-2" fill="url(#gradient-dark)" opacity="0.1" filter="url(#filter-blur)" cx="600" cy="400" r="180"></circle>
        <circle id="Blob-3" fill="url(#gradient-orange)" opacity="0.05" filter="url(#filter-blur)" cx="400" cy="300" r="250"></circle>
        
        <!-- Decorative Elements -->
        <path d="M0,200 C100,180 200,220 300,200 C400,180 500,220 600,200 C700,180 800,220 800,200" id="Wave-1" stroke="url(#gradient-orange)" stroke-width="2" opacity="0.2"></path>
        <path d="M0,400 C100,380 200,420 300,400 C400,380 500,420 600,400 C700,380 800,420 800,400" id="Wave-2" stroke="url(#gradient-orange)" stroke-width="2" opacity="0.2"></path>
        
        <!-- Main Illustration Elements -->
        <!-- Central Dashboard -->
        <g id="Dashboard" transform="translate(250, 100)">
            <rect id="Dashboard-Background" fill="#FFFFFF" x="0" y="0" width="300" height="400" rx="20" stroke="#E9ECEF" stroke-width="2"></rect>
            
            <!-- Dashboard Header -->
            <rect id="Header" fill="url(#gradient-orange)" x="0" y="0" width="300" height="60" rx="20 20 0 0"></rect>
            <text id="Dashboard-Title" font-family="Arial-BoldMT, Arial" font-size="18" font-weight="bold" fill="#FFFFFF">
                <tspan x="20" y="38">HR Management Dashboard</tspan>
            </text>
            
            <!-- Dashboard Content -->
            <g id="Dashboard-Content" transform="translate(20, 80)">
                <!-- Stats Row -->
                <g id="Stats-Row">
                    <rect id="Stat-1" fill="#F8F9FA" x="0" y="0" width="80" height="80" rx="10" stroke="#E9ECEF" stroke-width="1"></rect>
                    <rect id="Stat-2" fill="#F8F9FA" x="90" y="0" width="80" height="80" rx="10" stroke="#E9ECEF" stroke-width="1"></rect>
                    <rect id="Stat-3" fill="#F8F9FA" x="180" y="0" width="80" height="80" rx="10" stroke="#E9ECEF" stroke-width="1"></rect>
                    
                    <text id="Stat-1-Number" font-family="Arial-BoldMT, Arial" font-size="24" font-weight="bold" fill="url(#gradient-orange)">
                        <tspan x="30" y="40">42</tspan>
                    </text>
                    <text id="Stat-1-Label" font-family="Arial, Arial" font-size="12" fill="#6C757D">
                        <tspan x="20" y="60">Employees</tspan>
                    </text>
                    
                    <text id="Stat-2-Number" font-family="Arial-BoldMT, Arial" font-size="24" font-weight="bold" fill="url(#gradient-orange)">
                        <tspan x="120" y="40">8</tspan>
                    </text>
                    <text id="Stat-2-Label" font-family="Arial, Arial" font-size="12" fill="#6C757D">
                        <tspan x="110" y="60">Teams</tspan>
                    </text>
                    
                    <text id="Stat-3-Number" font-family="Arial-BoldMT, Arial" font-size="24" font-weight="bold" fill="url(#gradient-orange)">
                        <tspan x="210" y="40">95%</tspan>
                    </text>
                    <text id="Stat-3-Label" font-family="Arial, Arial" font-size="12" fill="#6C757D">
                        <tspan x="200" y="60">Satisfaction</tspan>
                    </text>
                </g>
                
                <!-- Chart Section -->
                <g id="Chart-Section" transform="translate(0, 100)">
                    <rect id="Chart-Background" fill="#F8F9FA" x="0" y="0" width="260" height="120" rx="10" stroke="#E9ECEF" stroke-width="1"></rect>
                    <text id="Chart-Title" font-family="Arial-BoldMT, Arial" font-size="14" font-weight="bold" fill="#343A40">
                        <tspan x="10" y="20">Performance Metrics</tspan>
                    </text>
                    
                    <!-- Chart Bars -->
                    <rect id="Chart-Bar-1" fill="url(#gradient-orange)" x="40" y="40" width="20" height="60" rx="3"></rect>
                    <rect id="Chart-Bar-2" fill="url(#gradient-orange)" x="70" y="50" width="20" height="50" rx="3"></rect>
                    <rect id="Chart-Bar-3" fill="url(#gradient-orange)" x="100" y="30" width="20" height="70" rx="3"></rect>
                    <rect id="Chart-Bar-4" fill="url(#gradient-orange)" x="130" y="45" width="20" height="55" rx="3"></rect>
                    <rect id="Chart-Bar-5" fill="url(#gradient-orange)" x="160" y="35" width="20" height="65" rx="3"></rect>
                    <rect id="Chart-Bar-6" fill="url(#gradient-orange)" x="190" y="55" width="20" height="45" rx="3"></rect>
                    <rect id="Chart-Bar-7" fill="url(#gradient-orange)" x="220" y="40" width="20" height="60" rx="3"></rect>
                </g>
                
                <!-- Table Section -->
                <g id="Table-Section" transform="translate(0, 240)">
                    <rect id="Table-Background" fill="#F8F9FA" x="0" y="0" width="260" height="120" rx="10" stroke="#E9ECEF" stroke-width="1"></rect>
                    <text id="Table-Title" font-family="Arial-BoldMT, Arial" font-size="14" font-weight="bold" fill="#343A40">
                        <tspan x="10" y="20">Recent Activities</tspan>
                    </text>
                    
                    <!-- Table Rows -->
                    <rect id="Table-Row-1" fill="#FFFFFF" x="10" y="30" width="240" height="20" rx="3"></rect>
                    <rect id="Table-Row-2" fill="#FFFFFF" x="10" y="55" width="240" height="20" rx="3"></rect>
                    <rect id="Table-Row-3" fill="#FFFFFF" x="10" y="80" width="240" height="20" rx="3"></rect>
                    <rect id="Table-Row-4" fill="#FFFFFF" x="10" y="105" width="240" height="20" rx="3"></rect>
                </g>
            </g>
        </g>
        
        <!-- Floating Elements -->
        <!-- Profile Cards -->
        <g id="Profile-Card-1" transform="translate(100, 150)">
            <rect id="Card-1-Background" fill="#FFFFFF" x="0" y="0" width="120" height="160" rx="15" stroke="#E9ECEF" stroke-width="2"></rect>
            <circle id="Card-1-Avatar" fill="url(#gradient-dark)" cx="60" cy="50" r="30"></circle>
            <rect id="Card-1-Name" fill="#F8F9FA" x="20" y="90" width="80" height="10" rx="5"></rect>
            <rect id="Card-1-Title" fill="#F8F9FA" x="30" y="110" width="60" height="8" rx="4"></rect>
            <rect id="Card-1-Info-1" fill="#F8F9FA" x="20" y="130" width="80" height="8" rx="4"></rect>
            <rect id="Card-1-Info-2" fill="#F8F9FA" x="20" y="145" width="60" height="8" rx="4"></rect>
        </g>
        
        <g id="Profile-Card-2" transform="translate(580, 200)">
            <rect id="Card-2-Background" fill="#FFFFFF" x="0" y="0" width="120" height="160" rx="15" stroke="#E9ECEF" stroke-width="2"></rect>
            <circle id="Card-2-Avatar" fill="url(#gradient-orange)" cx="60" cy="50" r="30"></circle>
            <rect id="Card-2-Name" fill="#F8F9FA" x="20" y="90" width="80" height="10" rx="5"></rect>
            <rect id="Card-2-Title" fill="#F8F9FA" x="30" y="110" width="60" height="8" rx="4"></rect>
            <rect id="Card-2-Info-1" fill="#F8F9FA" x="20" y="130" width="80" height="8" rx="4"></rect>
            <rect id="Card-2-Info-2" fill="#F8F9FA" x="20" y="145" width="60" height="8" rx="4"></rect>
        </g>
        
        <!-- Notification Icons -->
        <g id="Notification-1" transform="translate(150, 80)">
            <circle id="Notif-1-Background" fill="#FFFFFF" cx="25" cy="25" r="25" stroke="#E9ECEF" stroke-width="2"></circle>
            <rect id="Notif-1-Icon" fill="url(#gradient-orange)" x="15" y="15" width="20" height="20" rx="5"></rect>
        </g>
        
        <g id="Notification-2" transform="translate(600, 120)">
            <circle id="Notif-2-Background" fill="#FFFFFF" cx="25" cy="25" r="25" stroke="#E9ECEF" stroke-width="2"></circle>
            <rect id="Notif-2-Icon" fill="url(#gradient-dark)" x="15" y="15" width="20" height="20" rx="5"></rect>
        </g>
        
        <g id="Notification-3" transform="translate(180, 350)">
            <circle id="Notif-3-Background" fill="#FFFFFF" cx="25" cy="25" r="25" stroke="#E9ECEF" stroke-width="2"></circle>
            <rect id="Notif-3-Icon" fill="url(#gradient-orange)" x="15" y="15" width="20" height="20" rx="5"></rect>
        </g>
        
        <g id="Notification-4" transform="translate(580, 400)">
            <circle id="Notif-4-Background" fill="#FFFFFF" cx="25" cy="25" r="25" stroke="#E9ECEF" stroke-width="2"></circle>
            <rect id="Notif-4-Icon" fill="url(#gradient-dark)" x="15" y="15" width="20" height="20" rx="5"></rect>
        </g>
        
        <!-- Connection Lines -->
        <path d="M175,105 C200,150 250,120 250,160" id="Connection-1" stroke="url(#gradient-orange)" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"></path>
        <path d="M625,145 C600,180 550,150 550,160" id="Connection-2" stroke="url(#gradient-dark)" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"></path>
        <path d="M205,375 C230,400 250,380 250,400" id="Connection-3" stroke="url(#gradient-orange)" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"></path>
        <path d="M580,425 C550,450 530,430 550,400" id="Connection-4" stroke="url(#gradient-dark)" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"></path>
        
        <!-- HR Shell Text -->
        <text id="HR-Shell" font-family="Arial-BoldMT, Arial" font-size="36" font-weight="bold" fill="url(#gradient-orange)">
            <tspan x="325" y="530">HR Shell</tspan>
        </text>
        <text id="Tagline" font-family="Arial, Arial" font-size="16" fill="#6C757D" text-anchor="middle">
            <tspan x="400" y="570">Modern HR management for modern teams</tspan>
        </text>
    </g>
</svg>
