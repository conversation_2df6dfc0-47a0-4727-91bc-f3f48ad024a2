// AI Chat Interfaces

export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isLoading?: boolean;
  error?: boolean;
}

export interface ChatRequest {
  question: string;
}

export interface ChatResponse {
  answer: string;
  timestamp?: string;
  conversation_id?: string;
}

export interface ChatState {
  messages: ChatMessage[];
  isExpanded: boolean;
  isLoading: boolean;
  error: string | null;
  hasNewMessage: boolean;
  conversationId?: string;
}

export interface ChatWidgetConfig {
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  theme: 'light' | 'dark' | 'auto';
  enableNotifications: boolean;
  enablePersistence: boolean;
  maxMessages: number;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}
