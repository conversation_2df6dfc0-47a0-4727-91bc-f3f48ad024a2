<div class="company-detail">
  <div class="company-header">
    <div class="header-content">
      <button class="back-button" (click)="goBack()">
        <i class="fa fa-arrow-left"></i>
      </button>
      
      <div class="company-info">
        @if (isLoading()) {
          <div class="loading-skeleton">
            <div class="skeleton-title"></div>
            <div class="skeleton-subtitle"></div>
          </div>
        } @else if (company()) {
          <div class="company-logo" *ngIf="company()?.logo">
            <img [src]="company()?.logo" [alt]="company()?.name">
          </div>
          <div class="company-title">
            <h1>{{ company()?.name }}</h1>
            <div class="company-meta">
              <span class="company-industry" *ngIf="company()?.industry">{{ company()?.industry }}</span>
              <span class="company-location" *ngIf="company()?.city && company()?.country">
                {{ company()?.city }}, {{ company()?.country }}
              </span>
            </div>
          </div>
        } @else {
          <div class="error-state">
            <i class="fa fa-exclamation-circle"></i>
            <p>Company not found</p>
          </div>
        }
      </div>
      
      <div class="company-actions" *ngIf="company()">
        <button class="btn-edit" (click)="toggleEdit()" *ngIf="!isEditing">
          <i class="fa fa-edit"></i> Edit
        </button>
        <button class="btn-cancel" (click)="toggleEdit()" *ngIf="isEditing">
          <i class="fa fa-times"></i> Cancel
        </button>
        <button class="btn-save" (click)="saveCompany()" *ngIf="isEditing" [disabled]="isSaving">
          <i class="fa" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
          {{ isSaving ? 'Saving...' : 'Save' }}
        </button>
        <button class="btn-delete" (click)="deleteCompany()">
          <i class="fa fa-trash"></i> Delete
        </button>
      </div>
    </div>
  </div>
  
  @if (error()) {
    <div class="error-message">
      <i class="fa fa-exclamation-circle"></i>
      {{ error() }}
    </div>
  }
  
  @if (company()) {
    <div class="company-tabs">
      <ul class="tabs-list">
        <li class="tab-item" [class.active]="activeTab === 'details'">
          <button class="tab-link" (click)="setActiveTab('details')">
            <i class="fa fa-info-circle"></i> Details
          </button>
        </li>
        <li class="tab-item" [class.active]="activeTab === 'employees'">
          <button class="tab-link" (click)="setActiveTab('employees')">
            <i class="fa fa-users"></i> Employees
            <span class="tab-badge">{{ employeesCount() }}</span>
          </button>
        </li>
        <li class="tab-item" [class.active]="activeTab === 'documents'">
          <button class="tab-link" (click)="setActiveTab('documents')">
            <i class="fa fa-file"></i> Documents
          </button>
        </li>
        <li class="tab-item" [class.active]="activeTab === 'settings'">
          <button class="tab-link" (click)="setActiveTab('settings')">
            <i class="fa fa-cog"></i> Settings
          </button>
        </li>
      </ul>
    </div>
    
    <div class="company-content">
      <!-- Details Tab -->
      @if (activeTab === 'details') {
        <div class="tab-content">
          <form [formGroup]="companyForm" class="company-form">
            <div class="form-section">
              <h2 class="section-title">Basic Information</h2>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="name">Company Name</label>
                  <input 
                    type="text" 
                    id="name" 
                    formControlName="name"
                    [readonly]="!isEditing"
                    [class.readonly]="!isEditing"
                  >
                  <div class="form-error" *ngIf="companyForm.get('name')?.invalid && companyForm.get('name')?.touched">
                    Company name is required
                  </div>
                </div>
                
                <div class="form-group">
                  <label for="industry">Industry</label>
                  <input 
                    type="text" 
                    id="industry" 
                    formControlName="industry"
                    [readonly]="!isEditing"
                    [class.readonly]="!isEditing"
                  >
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="email">Email</label>
                  <input 
                    type="email" 
                    id="email" 
                    formControlName="email"
                    [readonly]="!isEditing"
                    [class.readonly]="!isEditing"
                  >
                  <div class="form-error" *ngIf="companyForm.get('email')?.invalid && companyForm.get('email')?.touched">
                    Please enter a valid email address
                  </div>
                </div>
                
                <div class="form-group">
                  <label for="phone">Phone</label>
                  <input 
                    type="tel" 
                    id="phone" 
                    formControlName="phone"
                    [readonly]="!isEditing"
                    [class.readonly]="!isEditing"
                  >
                </div>
              </div>
              
              <div class="form-group">
                <label for="website">Website</label>
                <input 
                  type="url" 
                  id="website" 
                  formControlName="website"
                  [readonly]="!isEditing"
                  [class.readonly]="!isEditing"
                >
              </div>
              
              <div class="form-group">
                <label for="description">Description</label>
                <textarea 
                  id="description" 
                  formControlName="description"
                  rows="4"
                  [readonly]="!isEditing"
                  [class.readonly]="!isEditing"
                ></textarea>
              </div>
            </div>
            
            <div class="form-section">
              <h2 class="section-title">Address</h2>
              
              <div class="form-group">
                <label for="address">Street Address</label>
                <input 
                  type="text" 
                  id="address" 
                  formControlName="address"
                  [readonly]="!isEditing"
                  [class.readonly]="!isEditing"
                >
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="city">City</label>
                  <input 
                    type="text" 
                    id="city" 
                    formControlName="city"
                    [readonly]="!isEditing"
                    [class.readonly]="!isEditing"
                  >
                </div>
                
                <div class="form-group">
                  <label for="state">State/Province</label>
                  <input 
                    type="text" 
                    id="state" 
                    formControlName="state"
                    [readonly]="!isEditing"
                    [class.readonly]="!isEditing"
                  >
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="zip">Postal/Zip Code</label>
                  <input 
                    type="text" 
                    id="zip" 
                    formControlName="zip"
                    [readonly]="!isEditing"
                    [class.readonly]="!isEditing"
                  >
                </div>
                
                <div class="form-group">
                  <label for="country">Country</label>
                  <input 
                    type="text" 
                    id="country" 
                    formControlName="country"
                    [readonly]="!isEditing"
                    [class.readonly]="!isEditing"
                  >
                </div>
              </div>
            </div>
          </form>
        </div>
      }
      
      <!-- Employees Tab -->
      @if (activeTab === 'employees') {
        <div class="tab-content">
          <div class="tab-header">
            <h2 class="tab-title">Employees</h2>
            <button class="btn-add">
              <i class="fa fa-plus"></i> Add Employee
            </button>
          </div>
          
          @if (employees().length === 0) {
            <div class="empty-state">
              <i class="fa fa-users"></i>
              <p>No employees found for this company</p>
              <button class="btn-add-first">Add First Employee</button>
            </div>
          } @else {
            <div class="employees-list">
              <!-- Employee list would go here -->
              <p>Employee list component would be displayed here</p>
            </div>
          }
        </div>
      }
      
      <!-- Other tabs would have placeholder content for now -->
      @if (activeTab === 'documents' || activeTab === 'settings') {
        <div class="tab-content">
          <div class="empty-state">
            <i class="fa" [ngClass]="activeTab === 'documents' ? 'fa-file' : 'fa-cog'"></i>
            <p>This feature is coming soon</p>
          </div>
        </div>
      }
    </div>
  }
</div>
