import { <PERSON>mpo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject, ViewChild, ElementRef, After<PERSON>iewChecked, HostListener, Renderer2 } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { AiChatStore } from '../../../../core/state/ai-chat/ai-chat.state';
import { ChatMessage } from '../../../../core/models/ai-chat.interface';
import { IconComponent } from '../../atoms/icon/icon.component';

@Component({
  selector: 'app-ai-chat-widget',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    IconComponent
  ],
  templateUrl: './ai-chat-widget.component.html',
  styleUrls: ['./ai-chat-widget.component.scss']
})
export class AiChatWidgetComponent implements <PERSON>Init, OnDestroy, AfterViewChecked {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;
  @ViewChild('chatWidget') chatWidget!: ElementRef;

  private readonly fb = inject(FormBuilder);
  private readonly chatStore = inject(AiChatStore);
  private readonly renderer = inject(Renderer2);
  private readonly destroy$ = new Subject<void>();

  // Form for message input
  messageForm!: FormGroup;

  // Store signals
  messages = this.chatStore.messages;
  isExpanded = this.chatStore.isExpanded;
  isLoading = this.chatStore.isLoading;
  error = this.chatStore.error;
  hasNewMessage = this.chatStore.hasNewMessage;

  // Component state
  private shouldScrollToBottom = false;
  private lastMessageCount = 0;

  // Drag and drop state
  isDragging = false;
  private dragStartX = 0;
  private dragStartY = 0;
  private initialX = 0;
  private initialY = 0;
  private currentX = 0;
  private currentY = 0;

  // Position storage key
  private readonly POSITION_STORAGE_KEY = 'ai-chat-widget-position';

  ngOnInit(): void {
    this.initializeForm();
    this.setupKeyboardListeners();
    this.loadSavedPosition();

    // Watch for new messages to trigger scroll
    this.messages()?.length !== this.lastMessageCount && this.scheduleScrollToBottom();
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Initialize the message form
   */
  private initializeForm(): void {
    this.messageForm = this.fb.group({
      message: ['', [Validators.required, Validators.maxLength(1000)]]
    });
  }

  /**
   * Setup keyboard event listeners
   */
  private setupKeyboardListeners(): void {
    // Handle Enter key for sending messages
    document.addEventListener('keydown', (event) => {
      if (this.isExpanded() && event.key === 'Enter' && !event.shiftKey) {
        const target = event.target as HTMLElement;
        if (target.tagName === 'TEXTAREA' && target.classList.contains('message-input')) {
          event.preventDefault();
          this.sendMessage();
        }
      }
    });

    // Handle Escape key for closing widget
    document.addEventListener('keydown', (event) => {
      if (this.isExpanded() && event.key === 'Escape') {
        this.toggleWidget();
      }
    });
  }

  /**
   * Toggle chat widget expanded state
   */
  toggleWidget(): void {
    this.chatStore.toggleExpanded();
    
    // Focus on input when expanding
    if (this.isExpanded()) {
      setTimeout(() => {
        this.focusInput();
        this.scheduleScrollToBottom();
      }, 300); // Wait for animation
    }
  }

  /**
   * Expand the chat widget
   */
  expandWidget(): void {
    this.chatStore.expand();
    setTimeout(() => {
      this.focusInput();
      this.scheduleScrollToBottom();
    }, 300);
  }

  /**
   * Collapse the chat widget
   */
  collapseWidget(): void {
    this.chatStore.collapse();
  }

  /**
   * Send a message to the AI assistant
   */
  sendMessage(): void {
    if (this.messageForm.invalid || this.isLoading()) {
      return;
    }

    const message = this.messageForm.get('message')?.value?.trim();
    if (!message) {
      return;
    }

    // Clear the form
    this.messageForm.reset();

    // Send message through store
    this.chatStore.sendMessage(message);

    // Schedule scroll to bottom for new messages
    this.scheduleScrollToBottom();

    // Focus back on input
    setTimeout(() => this.focusInput(), 100);
  }

  /**
   * Clear all chat messages
   */
  clearChat(): void {
    if (confirm('Are you sure you want to clear all chat messages?')) {
      this.chatStore.clearMessages();
    }
  }

  /**
   * Retry sending the last message if there was an error
   */
  retryLastMessage(): void {
    const messages = this.messages();
    const lastUserMessage = [...messages].reverse().find(msg => msg.sender === 'user');
    
    if (lastUserMessage) {
      this.chatStore.sendMessage(lastUserMessage.content);
      this.scheduleScrollToBottom();
    }
  }

  /**
   * Clear error state
   */
  clearError(): void {
    this.chatStore.clearError();
  }

  /**
   * Focus on the message input
   */
  private focusInput(): void {
    if (this.messageInput?.nativeElement) {
      this.messageInput.nativeElement.focus();
    }
  }

  /**
   * Schedule scroll to bottom on next view check
   */
  private scheduleScrollToBottom(): void {
    this.shouldScrollToBottom = true;
    this.lastMessageCount = this.messages()?.length || 0;
  }

  /**
   * Scroll messages container to bottom
   */
  private scrollToBottom(): void {
    if (this.messagesContainer?.nativeElement) {
      const container = this.messagesContainer.nativeElement;
      container.scrollTop = container.scrollHeight;
    }
  }

  /**
   * Format timestamp for display
   */
  formatTimestamp(timestamp: Date): string {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) { // 24 hours
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours}h ago`;
    } else {
      return messageTime.toLocaleDateString();
    }
  }

  /**
   * Get placeholder text for input
   */
  getInputPlaceholder(): string {
    if (this.isLoading()) {
      return 'AI is thinking...';
    }
    return 'Ask me anything...';
  }

  /**
   * Check if input is disabled
   */
  isInputDisabled(): boolean {
    return this.isLoading();
  }

  /**
   * Get message CSS classes
   */
  getMessageClasses(message: ChatMessage): string {
    const classes = ['message'];
    classes.push(`message--${message.sender}`);
    
    if (message.isLoading) {
      classes.push('message--loading');
    }
    
    if (message.error) {
      classes.push('message--error');
    }
    
    return classes.join(' ');
  }

  /**
   * Handle Enter key press in textarea
   */
  onEnterKeyPress(event: KeyboardEvent): void {
    if (!event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  /**
   * Track messages for ngFor performance
   */
  trackMessage(index: number, message: ChatMessage): string {
    return message.id;
  }

  // Drag and Drop Methods

  /**
   * Handle mouse down on chat bubble to start dragging
   */
  onMouseDown(event: MouseEvent): void {
    if (this.isExpanded()) {
      return; // Don't allow dragging when expanded
    }

    event.preventDefault();
    this.isDragging = true;

    const rect = this.chatWidget.nativeElement.getBoundingClientRect();
    this.dragStartX = event.clientX - rect.left;
    this.dragStartY = event.clientY - rect.top;

    this.initialX = this.currentX;
    this.initialY = this.currentY;

    // Add dragging class for visual feedback
    this.renderer.addClass(this.chatWidget.nativeElement, 'ai-chat-widget--dragging');

    // Prevent text selection during drag
    this.renderer.addClass(document.body, 'user-select-none');
  }

  /**
   * Handle mouse move during dragging
   */
  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (!this.isDragging) {
      return;
    }

    event.preventDefault();

    const newX = event.clientX - this.dragStartX;
    const newY = event.clientY - this.dragStartY;

    // Constrain to viewport boundaries
    const constrainedPosition = this.constrainToViewport(newX, newY);
    this.currentX = constrainedPosition.x;
    this.currentY = constrainedPosition.y;

    this.updatePosition();
  }

  /**
   * Handle mouse up to end dragging
   */
  @HostListener('document:mouseup', ['$event'])
  onMouseUp(event: MouseEvent): void {
    if (!this.isDragging) {
      return;
    }

    this.isDragging = false;

    // Remove dragging class
    this.renderer.removeClass(this.chatWidget.nativeElement, 'ai-chat-widget--dragging');
    this.renderer.removeClass(document.body, 'user-select-none');

    // Save position to localStorage
    this.savePosition();
  }

  /**
   * Handle touch start for mobile dragging
   */
  onTouchStart(event: TouchEvent): void {
    if (this.isExpanded() || event.touches.length !== 1) {
      return;
    }

    event.preventDefault();
    const touch = event.touches[0];
    this.isDragging = true;

    const rect = this.chatWidget.nativeElement.getBoundingClientRect();
    this.dragStartX = touch.clientX - rect.left;
    this.dragStartY = touch.clientY - rect.top;

    this.initialX = this.currentX;
    this.initialY = this.currentY;

    this.renderer.addClass(this.chatWidget.nativeElement, 'ai-chat-widget--dragging');
  }

  /**
   * Handle touch move for mobile dragging
   */
  @HostListener('document:touchmove', ['$event'])
  onTouchMove(event: TouchEvent): void {
    if (!this.isDragging || event.touches.length !== 1) {
      return;
    }

    event.preventDefault();
    const touch = event.touches[0];

    const newX = touch.clientX - this.dragStartX;
    const newY = touch.clientY - this.dragStartY;

    const constrainedPosition = this.constrainToViewport(newX, newY);
    this.currentX = constrainedPosition.x;
    this.currentY = constrainedPosition.y;

    this.updatePosition();
  }

  /**
   * Handle touch end for mobile dragging
   */
  @HostListener('document:touchend', ['$event'])
  onTouchEnd(event: TouchEvent): void {
    if (!this.isDragging) {
      return;
    }

    this.isDragging = false;
    this.renderer.removeClass(this.chatWidget.nativeElement, 'ai-chat-widget--dragging');
    this.savePosition();
  }

  /**
   * Constrain widget position to viewport boundaries
   */
  private constrainToViewport(x: number, y: number): { x: number; y: number } {
    const widget = this.chatWidget.nativeElement;
    const rect = widget.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Add some padding from edges
    const padding = 16;

    const constrainedX = Math.max(padding, Math.min(x, viewportWidth - rect.width - padding));
    const constrainedY = Math.max(padding, Math.min(y, viewportHeight - rect.height - padding));

    return { x: constrainedX, y: constrainedY };
  }

  /**
   * Update widget position
   */
  private updatePosition(): void {
    const widget = this.chatWidget.nativeElement;
    this.renderer.setStyle(widget, 'left', `${this.currentX}px`);
    this.renderer.setStyle(widget, 'top', `${this.currentY}px`);
    this.renderer.setStyle(widget, 'right', 'auto');
    this.renderer.setStyle(widget, 'bottom', 'auto');
  }

  /**
   * Save current position to localStorage
   */
  private savePosition(): void {
    const position = {
      x: this.currentX,
      y: this.currentY,
      timestamp: Date.now()
    };

    try {
      localStorage.setItem(this.POSITION_STORAGE_KEY, JSON.stringify(position));
    } catch (error) {
      console.warn('Failed to save chat widget position:', error);
    }
  }

  /**
   * Load saved position from localStorage
   */
  private loadSavedPosition(): void {
    try {
      const savedPosition = localStorage.getItem(this.POSITION_STORAGE_KEY);
      if (savedPosition) {
        const position = JSON.parse(savedPosition);

        // Check if position is recent (within 7 days)
        const isRecent = Date.now() - position.timestamp < 7 * 24 * 60 * 60 * 1000;

        if (isRecent && position.x !== undefined && position.y !== undefined) {
          // Validate position is still within viewport
          const constrainedPosition = this.constrainToViewport(position.x, position.y);
          this.currentX = constrainedPosition.x;
          this.currentY = constrainedPosition.y;

          // Apply position after view init
          setTimeout(() => this.updatePosition(), 0);
        }
      }
    } catch (error) {
      console.warn('Failed to load saved chat widget position:', error);
    }
  }

  /**
   * Reset widget position to default
   */
  resetPosition(): void {
    this.currentX = 0;
    this.currentY = 0;

    const widget = this.chatWidget.nativeElement;
    this.renderer.removeStyle(widget, 'left');
    this.renderer.removeStyle(widget, 'top');
    this.renderer.setStyle(widget, 'right', '2rem');
    this.renderer.setStyle(widget, 'bottom', '2rem');

    // Clear saved position
    try {
      localStorage.removeItem(this.POSITION_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear saved position:', error);
    }
  }
}
