import { Compo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { AiChatStore } from '../../../../core/state/ai-chat/ai-chat.state';
import { ChatMessage } from '../../../../core/models/ai-chat.interface';
import { IconComponent } from '../../atoms/icon/icon.component';

@Component({
  selector: 'app-ai-chat-widget',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    IconComponent
  ],
  templateUrl: './ai-chat-widget.component.html',
  styleUrls: ['./ai-chat-widget.component.scss']
})
export class AiChatWidgetComponent implements OnInit, On<PERSON><PERSON>roy, AfterViewChecked {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  private readonly fb = inject(FormBuilder);
  private readonly chatStore = inject(AiChatStore);
  private readonly destroy$ = new Subject<void>();

  // Form for message input
  messageForm!: FormGroup;

  // Store signals
  messages = this.chatStore.messages;
  isExpanded = this.chatStore.isExpanded;
  isLoading = this.chatStore.isLoading;
  error = this.chatStore.error;
  hasNewMessage = this.chatStore.hasNewMessage;

  // Component state
  private shouldScrollToBottom = false;
  private lastMessageCount = 0;

  ngOnInit(): void {
    this.initializeForm();
    this.setupKeyboardListeners();
    
    // Watch for new messages to trigger scroll
    this.messages()?.length !== this.lastMessageCount && this.scheduleScrollToBottom();
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Initialize the message form
   */
  private initializeForm(): void {
    this.messageForm = this.fb.group({
      message: ['', [Validators.required, Validators.maxLength(1000)]]
    });
  }

  /**
   * Setup keyboard event listeners
   */
  private setupKeyboardListeners(): void {
    // Handle Enter key for sending messages
    document.addEventListener('keydown', (event) => {
      if (this.isExpanded() && event.key === 'Enter' && !event.shiftKey) {
        const target = event.target as HTMLElement;
        if (target.tagName === 'TEXTAREA' && target.classList.contains('message-input')) {
          event.preventDefault();
          this.sendMessage();
        }
      }
    });

    // Handle Escape key for closing widget
    document.addEventListener('keydown', (event) => {
      if (this.isExpanded() && event.key === 'Escape') {
        this.toggleWidget();
      }
    });
  }

  /**
   * Toggle chat widget expanded state
   */
  toggleWidget(): void {
    this.chatStore.toggleExpanded();
    
    // Focus on input when expanding
    if (this.isExpanded()) {
      setTimeout(() => {
        this.focusInput();
        this.scheduleScrollToBottom();
      }, 300); // Wait for animation
    }
  }

  /**
   * Expand the chat widget
   */
  expandWidget(): void {
    this.chatStore.expand();
    setTimeout(() => {
      this.focusInput();
      this.scheduleScrollToBottom();
    }, 300);
  }

  /**
   * Collapse the chat widget
   */
  collapseWidget(): void {
    this.chatStore.collapse();
  }

  /**
   * Send a message to the AI assistant
   */
  sendMessage(): void {
    if (this.messageForm.invalid || this.isLoading()) {
      return;
    }

    const message = this.messageForm.get('message')?.value?.trim();
    if (!message) {
      return;
    }

    // Clear the form
    this.messageForm.reset();

    // Send message through store
    this.chatStore.sendMessage(message);

    // Schedule scroll to bottom for new messages
    this.scheduleScrollToBottom();

    // Focus back on input
    setTimeout(() => this.focusInput(), 100);
  }

  /**
   * Clear all chat messages
   */
  clearChat(): void {
    if (confirm('Are you sure you want to clear all chat messages?')) {
      this.chatStore.clearMessages();
    }
  }

  /**
   * Retry sending the last message if there was an error
   */
  retryLastMessage(): void {
    const messages = this.messages();
    const lastUserMessage = [...messages].reverse().find(msg => msg.sender === 'user');
    
    if (lastUserMessage) {
      this.chatStore.sendMessage(lastUserMessage.content);
      this.scheduleScrollToBottom();
    }
  }

  /**
   * Clear error state
   */
  clearError(): void {
    this.chatStore.clearError();
  }

  /**
   * Focus on the message input
   */
  private focusInput(): void {
    if (this.messageInput?.nativeElement) {
      this.messageInput.nativeElement.focus();
    }
  }

  /**
   * Schedule scroll to bottom on next view check
   */
  private scheduleScrollToBottom(): void {
    this.shouldScrollToBottom = true;
    this.lastMessageCount = this.messages()?.length || 0;
  }

  /**
   * Scroll messages container to bottom
   */
  private scrollToBottom(): void {
    if (this.messagesContainer?.nativeElement) {
      const container = this.messagesContainer.nativeElement;
      container.scrollTop = container.scrollHeight;
    }
  }

  /**
   * Format timestamp for display
   */
  formatTimestamp(timestamp: Date): string {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) { // 24 hours
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours}h ago`;
    } else {
      return messageTime.toLocaleDateString();
    }
  }

  /**
   * Get placeholder text for input
   */
  getInputPlaceholder(): string {
    if (this.isLoading()) {
      return 'AI is thinking...';
    }
    return 'Ask me anything...';
  }

  /**
   * Check if input is disabled
   */
  isInputDisabled(): boolean {
    return this.isLoading();
  }

  /**
   * Get message CSS classes
   */
  getMessageClasses(message: ChatMessage): string {
    const classes = ['message'];
    classes.push(`message--${message.sender}`);
    
    if (message.isLoading) {
      classes.push('message--loading');
    }
    
    if (message.error) {
      classes.push('message--error');
    }
    
    return classes.join(' ');
  }

  /**
   * Handle Enter key press in textarea
   */
  onEnterKeyPress(event: KeyboardEvent): void {
    if (!event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  /**
   * Track messages for ngFor performance
   */
  trackMessage(index: number, message: ChatMessage): string {
    return message.id;
  }
}
