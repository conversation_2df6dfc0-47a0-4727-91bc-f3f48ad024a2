import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'app-stat-card',
  standalone: true,
  imports: [CommonModule, IconComponent],
  template: `
    <div class="stat-card">
      <div class="stat-icon" [ngStyle]="{'background-color': backgroundColor}">
        <app-icon [name]="icon" size="md" [color]="'white'"></app-icon>
      </div>
      <div class="stat-content">
        <div class="stat-label">{{ label }}</div>
        <div class="stat-value">{{ value }}</div>
      </div>
      <div class="stat-trend">
        <img [src]="trendImageUrl" alt="Trend" class="trend-image">
      </div>
    </div>
  `,
  styles: [`
    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .stat-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      border-radius: 4px;
      margin-right: 16px;
    }
    
    .stat-content {
      flex: 1;
    }
    
    .stat-label {
      font-size: 14px;
      color: #6c757d;
      margin-bottom: 4px;
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #343a40;
    }
    
    .stat-trend {
      margin-left: 16px;
    }
    
    .trend-image {
      height: 40px;
      width: auto;
    }
  `]
})
export class StatCardComponent {
  @Input() label: string = '';
  @Input() value: string | number = '';
  @Input() icon: string = '';
  @Input() backgroundColor: string = '#ff6b6b';
  @Input() trendImageUrl: string = '';
}
