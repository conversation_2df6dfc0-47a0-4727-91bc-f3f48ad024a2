import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthStore } from '../../core/state';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent {
  private authStore = inject(AuthStore);
  private fb = inject(FormBuilder);
  
  // Access state from the store using signals
  user = this.authStore.user;
  isAdmin = this.authStore.isAdmin;
  
  // Form state
  profileForm: FormGroup;
  isEditing = false;
  isSaving = false;
  
  // Tab state
  activeTab = 'profile'; // 'profile', 'security', 'notifications', 'preferences'
  
  constructor() {
    this.profileForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      position: [''],
      department: [''],
      bio: ['']
    });
    
    // Initialize form with user data
    this.initForm();
  }
  
  private initForm(): void {
    const userData = this.user();
    if (userData) {
      this.profileForm.patchValue({
        firstName: userData.first_name,
        lastName: userData.last_name,
        email: userData.email,
        // Other fields would come from the user profile
        phone: '',
        position: '',
        department: '',
        bio: ''
      });
    }
  }
  
  toggleEdit(): void {
    this.isEditing = !this.isEditing;
    if (!this.isEditing) {
      this.initForm(); // Reset form when canceling edit
    }
  }
  
  saveProfile(): void {
    if (this.profileForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.profileForm.controls).forEach(key => {
        const control = this.profileForm.get(key);
        control?.markAsTouched();
      });
      return;
    }
    
    this.isSaving = true;
    
    // Here you would typically call an API to update the user profile
    // For now, we'll just simulate a delay and then toggle edit mode
    setTimeout(() => {
      this.isSaving = false;
      this.isEditing = false;
      // Show success message or handle response
    }, 1000);
  }
  
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
}
