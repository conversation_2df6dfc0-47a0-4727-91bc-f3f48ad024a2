/* Font is imported in the add-employee-modal component */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-container {
  background: linear-gradient(to bottom, #ffffff, #f8f9fa);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.modal-container.small {
  max-width: 400px;
}

.modal-container.medium {
  max-width: 600px;
}

.modal-container.large {
  max-width: 800px;
}

.modal-container.full {
  max-width: 90vw;
  height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(233, 236, 239, 0.7);
  background: linear-gradient(to right, #ffffff, #f8f9fa);
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #343a40;
  letter-spacing: -0.01em;
}

.modal-close {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 16px;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
}

.modal-close:hover {
  color: #343a40;
  background-color: rgba(0, 0, 0, 0.05);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid rgba(233, 236, 239, 0.7);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: linear-gradient(to right, #f8f9fa, #ffffff);
}
