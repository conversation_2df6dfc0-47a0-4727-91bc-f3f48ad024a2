@use 'sass:color';

// Color Variables

// Brand Colors
$primary: #ff6b35;
$secondary: #6c757d;
$success: #28a745;
$info: #17a2b8;
$warning: #ffc107;
$danger: #dc3545;
$light: #f8f9fa;
$dark: #343a40;

// Gray Scale
$white: #ffffff;
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;
$black: #000000;

// UI Colors
$body-bg: $gray-100;
$body-color: $gray-900;
$border-color: $gray-200;
$link-color: $primary;
$link-hover-color: color.adjust($primary, $lightness: -15%);

// Background Colors
$background-light: $gray-100;
$background-dark: $gray-800;

// Text Colors
$text-primary: $gray-900;
$text-secondary: $gray-600;
$text-muted: $gray-500;

// Border Colors
$border-light: $gray-200;
$border-medium: $gray-300;
$border-dark: $gray-400;

// Primary Color Variants
$primary-light: color.adjust($primary, $lightness: 20%);
$primary-dark: color.adjust($primary, $lightness: -20%);

// Error/Success Colors
$error: $danger;
$success-light: color.adjust($success, $lightness: 20%);

// Component Colors
$header-bg: $white;
$sidebar-bg: $white;
$sidebar-active-bg: #f0f7ff;
$sidebar-active-color: $primary;
$sidebar-hover-bg: rgba(0, 0, 0, 0.05);
$card-bg: $white;
$card-border-color: $gray-200;
$input-bg: $white;
$input-border-color: $gray-300;
$input-focus-border-color: color.adjust($primary, $lightness: 25%);
$input-focus-box-shadow-color: rgba($primary, 0.25);

// Status Colors
$status-online: $success;
$status-offline: $gray-500;
$status-away: $warning;
$status-busy: $danger;

// Notification Colors
$notification-bg: $danger;
$notification-color: $white;

// Shadow Colors
$shadow-color: rgba(0, 0, 0, 0.1);
$shadow-color-darker: rgba(0, 0, 0, 0.2);

// Transparent Colors
$transparent-dark: rgba(0, 0, 0, 0.5);
$transparent-light: rgba(255, 255, 255, 0.5);

// Font weights
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;

// Typography
$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-base: 1rem;
$font-size-lg: 1.125rem;
$font-size-xl: 1.25rem;

// Line heights
$line-height-tight: 1.25;
$line-height-base: 1.5;
$line-height-relaxed: 1.75;

// Color Mixins
@mixin color-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;
}

// Generate color utility classes
@each $color, $value in (
  'primary': $primary,
  'secondary': $secondary,
  'success': $success,
  'info': $info,
  'warning': $warning,
  'danger': $danger,
  'light': $light,
  'dark': $dark
) {
  .text-#{$color} {
    color: $value !important;
  }

  .bg-#{$color} {
    background-color: $value !important;
  }

  .border-#{$color} {
    border-color: $value !important;
  }
}


