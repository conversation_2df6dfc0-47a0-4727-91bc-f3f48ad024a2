<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>HR Team Illustration</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FF6B35" offset="0%"></stop>
            <stop stop-color="#E85A2A" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#343A40" offset="0%"></stop>
            <stop stop-color="#212529" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="HR-Team-Illustration" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background Elements -->
        <circle id="Background-Circle-1" fill="#F8F9FA" cx="400" cy="300" r="250"></circle>
        <circle id="Background-Circle-2" fill="#E9ECEF" cx="200" cy="150" r="80"></circle>
        <circle id="Background-Circle-3" fill="#E9ECEF" cx="600" cy="450" r="100"></circle>
        
        <!-- Decorative Elements -->
        <path d="M100,200 C150,150 200,180 250,150 C300,120 350,150 400,120 C450,90 500,120 550,90 C600,60 650,90 700,60" id="Wave-Line" stroke="#FF6B35" stroke-width="3" stroke-linecap="round"></path>
        <path d="M100,400 C150,370 200,400 250,370 C300,340 350,370 400,340 C450,310 500,340 550,310 C600,280 650,310 700,280" id="Wave-Line-2" stroke="#FF6B35" stroke-width="3" stroke-linecap="round"></path>
        
        <!-- People Silhouettes -->
        <!-- Person 1 -->
        <circle id="Person-1-Head" fill="url(#linearGradient-2)" cx="250" cy="200" r="40"></circle>
        <rect id="Person-1-Body" fill="url(#linearGradient-2)" x="230" y="240" width="40" height="80" rx="20"></rect>
        <rect id="Person-1-Arm-Left" fill="url(#linearGradient-2)" x="190" y="250" width="40" height="15" rx="7.5" transform="translate(210, 257.5) rotate(-30) translate(-210, -257.5)"></rect>
        <rect id="Person-1-Arm-Right" fill="url(#linearGradient-2)" x="270" y="250" width="40" height="15" rx="7.5" transform="translate(290, 257.5) rotate(30) translate(-290, -257.5)"></rect>
        <rect id="Person-1-Leg-Left" fill="url(#linearGradient-2)" x="230" y="320" width="15" height="60" rx="7.5"></rect>
        <rect id="Person-1-Leg-Right" fill="url(#linearGradient-2)" x="255" y="320" width="15" height="60" rx="7.5"></rect>
        
        <!-- Person 2 -->
        <circle id="Person-2-Head" fill="url(#linearGradient-1)" cx="400" cy="200" r="40"></circle>
        <rect id="Person-2-Body" fill="url(#linearGradient-1)" x="380" y="240" width="40" height="80" rx="20"></rect>
        <rect id="Person-2-Arm-Left" fill="url(#linearGradient-1)" x="340" y="250" width="40" height="15" rx="7.5" transform="translate(360, 257.5) rotate(-30) translate(-360, -257.5)"></rect>
        <rect id="Person-2-Arm-Right" fill="url(#linearGradient-1)" x="420" y="250" width="40" height="15" rx="7.5" transform="translate(440, 257.5) rotate(30) translate(-440, -257.5)"></rect>
        <rect id="Person-2-Leg-Left" fill="url(#linearGradient-1)" x="380" y="320" width="15" height="60" rx="7.5"></rect>
        <rect id="Person-2-Leg-Right" fill="url(#linearGradient-1)" x="405" y="320" width="15" height="60" rx="7.5"></rect>
        
        <!-- Person 3 -->
        <circle id="Person-3-Head" fill="url(#linearGradient-2)" cx="550" cy="200" r="40"></circle>
        <rect id="Person-3-Body" fill="url(#linearGradient-2)" x="530" y="240" width="40" height="80" rx="20"></rect>
        <rect id="Person-3-Arm-Left" fill="url(#linearGradient-2)" x="490" y="250" width="40" height="15" rx="7.5" transform="translate(510, 257.5) rotate(-30) translate(-510, -257.5)"></rect>
        <rect id="Person-3-Arm-Right" fill="url(#linearGradient-2)" x="570" y="250" width="40" height="15" rx="7.5" transform="translate(590, 257.5) rotate(30) translate(-590, -257.5)"></rect>
        <rect id="Person-3-Leg-Left" fill="url(#linearGradient-2)" x="530" y="320" width="15" height="60" rx="7.5"></rect>
        <rect id="Person-3-Leg-Right" fill="url(#linearGradient-2)" x="555" y="320" width="15" height="60" rx="7.5"></rect>
        
        <!-- HR Elements -->
        <rect id="Document-1" fill="#FFFFFF" stroke="#ADB5BD" stroke-width="2" x="300" y="400" width="80" height="100" rx="5"></rect>
        <line x1="320" y1="420" x2="360" y2="420" id="Doc-Line-1" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
        <line x1="320" y1="440" x2="360" y2="440" id="Doc-Line-2" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
        <line x1="320" y1="460" x2="360" y2="460" id="Doc-Line-3" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
        
        <rect id="Document-2" fill="#FFFFFF" stroke="#ADB5BD" stroke-width="2" x="420" y="400" width="80" height="100" rx="5"></rect>
        <line x1="440" y1="420" x2="480" y2="420" id="Doc-Line-4" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
        <line x1="440" y1="440" x2="480" y2="440" id="Doc-Line-5" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
        <line x1="440" y1="460" x2="480" y2="460" id="Doc-Line-6" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
        
        <!-- HR Text -->
        <text id="HR-Shell" font-family="Arial-BoldMT, Arial" font-size="36" font-weight="bold" fill="#FF6B35">
            <tspan x="325" y="530">HR Shell</tspan>
        </text>
    </g>
</svg>
