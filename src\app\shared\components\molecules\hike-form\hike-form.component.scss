@import '../../../../../styles/variables/_colors';

.hike-form {
  background: $white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .form-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, $primary, $primary-light);
    color: $white;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: $font-weight-semibold;
    }
  }

  .form-content {
    padding: 2rem;

    .form-group {
      margin-bottom: 1.5rem;

      &.disabled {
        opacity: 0.6;
        pointer-events: none;
      }

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: $font-weight-medium;
        color: $text-primary;
        font-size: 0.9rem;

        .required {
          color: $error;
          margin-left: 0.25rem;
        }
      }

      .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .currency-symbol {
          position: absolute;
          left: 1rem;
          color: $text-secondary;
          font-weight: $font-weight-medium;
          z-index: 1;
        }

        .percentage-symbol {
          position: absolute;
          right: 1rem;
          color: $text-secondary;
          font-weight: $font-weight-medium;
          z-index: 1;
        }

        .form-input {
          width: 100%;
          padding: 0.75rem 1rem;
          border: 2px solid $border-light;
          border-radius: 8px;
          font-size: 1rem;
          transition: all 0.3s ease;
          background-color: $white;

          &:focus {
            outline: none;
            border-color: $primary;
            box-shadow: 0 0 0 3px rgba($primary, 0.1);
          }

          &.error {
            border-color: $error;
            box-shadow: 0 0 0 3px rgba($error, 0.1);
          }

          &.calculated {
            background-color: $background-light;
            color: $text-secondary;
            cursor: not-allowed;
          }

          &::placeholder {
            color: $text-secondary;
          }

          // Adjust padding for currency/percentage symbols
          &:not(.calculated) {
            &[formControlName="previous_salary"],
            &[formControlName="hike_amount"],
            &[formControlName="new_salary"] {
              padding-left: 2.5rem;
            }

            &[formControlName="hike_percentage"] {
              padding-right: 2.5rem;
            }
          }

          // Remove number input arrows
          &[type="number"] {
            -moz-appearance: textfield;

            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }
          }
        }
      }

      .form-textarea {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid $border-light;
        border-radius: 8px;
        font-size: 1rem;
        font-family: inherit;
        resize: vertical;
        min-height: 100px;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: $primary;
          box-shadow: 0 0 0 3px rgba($primary, 0.1);
        }

        &.error {
          border-color: $error;
          box-shadow: 0 0 0 3px rgba($error, 0.1);
        }

        &::placeholder {
          color: $text-secondary;
        }
      }

      .character-count {
        text-align: right;
        margin-top: 0.25rem;
        font-size: 0.8rem;
        color: $text-secondary;
      }

      .error-message {
        margin-top: 0.5rem;
        color: $error;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;

        &::before {
          content: '⚠';
          font-size: 0.9rem;
        }
      }

      .field-note {
        margin-top: 0.5rem;
        color: $text-secondary;
        font-size: 0.8rem;
        font-style: italic;
      }
    }

    .calculation-method {
      margin-bottom: 1.5rem;

      .form-label {
        display: block;
        margin-bottom: 0.75rem;
        font-weight: $font-weight-medium;
        color: $text-primary;
        font-size: 0.9rem;
      }

      .method-toggle {
        display: flex;
        gap: 0.5rem;
        background: $background-light;
        padding: 0.25rem;
        border-radius: 8px;

        .method-button {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          padding: 0.75rem 1rem;
          background: none;
          border: none;
          border-radius: 6px;
          color: $text-secondary;
          font-weight: $font-weight-medium;
          cursor: pointer;
          transition: all 0.3s ease;

          .material-icons {
            font-size: 1.1rem;
          }

          &:hover {
            background: rgba($primary, 0.1);
            color: $primary;
          }

          &.active {
            background: $primary;
            color: $white;
            box-shadow: 0 2px 8px rgba($primary, 0.3);
          }
        }
      }
    }

    .hike-inputs {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .calculated-field {
      background: $background-light;
      padding: 1.5rem;
      border-radius: 12px;
      margin: 1.5rem 0;
      border-left: 4px solid $primary;

      .form-group {
        margin-bottom: 0;
      }
    }

    .hike-summary {
      background: linear-gradient(135deg, rgba($primary, 0.05), rgba($primary-light, 0.05));
      padding: 1.5rem;
      border-radius: 12px;
      margin: 1.5rem 0;
      border: 1px solid rgba($primary, 0.1);

      h4 {
        margin: 0 0 1rem 0;
        font-size: 1.1rem;
        font-weight: $font-weight-semibold;
        color: $text-primary;
      }

      .summary-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;

        .summary-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem 0;

          .label {
            color: $text-secondary;
            font-size: 0.9rem;
          }

          .value {
            font-weight: $font-weight-semibold;
            color: $text-primary;
          }

          &.highlight {
            grid-column: 1 / -1;
            background: rgba($primary, 0.1);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-top: 0.5rem;

            .label {
              color: $primary;
              font-weight: $font-weight-semibold;
            }

            .value {
              color: $primary;
              font-size: 1.1rem;
              font-weight: $font-weight-bold;
            }
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 2rem;
      padding-top: 1.5rem;
      border-top: 1px solid $border-light;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .hike-form {
    .form-header {
      padding: 1rem 1.5rem;

      h3 {
        font-size: 1.1rem;
      }
    }

    .form-content {
      padding: 1.5rem;

      .hike-inputs {
        grid-template-columns: 1fr;
        gap: 1.25rem;
      }

      .hike-summary {
        .summary-grid {
          grid-template-columns: 1fr;
        }
      }

      .form-actions {
        flex-direction: column-reverse;
        gap: 0.75rem;

        app-button {
          width: 100%;
        }
      }
    }
  }
}

// Animation for calculated fields
@keyframes highlight {
  0% { background-color: rgba($primary, 0.2); }
  100% { background-color: rgba($primary, 0.05); }
}

.calculated-field {
  animation: highlight 0.5s ease-in-out;
}

// Focus states
.form-group:focus-within .form-label {
  color: $primary;
}
