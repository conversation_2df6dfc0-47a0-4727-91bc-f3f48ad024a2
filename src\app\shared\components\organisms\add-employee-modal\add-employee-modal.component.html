<app-modal
  id="add-employee-modal"
  [title]="isEditMode ? 'Edit Employee' : 'Add New Employee'"
  size="large"
  [showFooter]="true">

  <form [formGroup]="employeeForm" (ngSubmit)="onSubmit()" class="employee-form">
    <div class="employee-form__container">
      <div class="employee-form__avatar-section">
        <h3 class="employee-form__section-title">Profile Photo</h3>
        <app-file-upload
          formControlName="avatar"
          (fileSelected)="onAvatarSelected($event)"
          (fileRemoved)="onAvatarRemoved()"
          placeholder="Upload Profile Photo"
          acceptedFileTypes="JPG, PNG, GIF (max 5MB)"
        ></app-file-upload>
        <p class="employee-form__help-text">Upload a professional photo for the employee profile</p>
      </div>

      <div class="employee-form__fields-section">
        <h3 class="employee-form__section-title">Personal Information</h3>

        <div class="employee-form__row">
          <div class="employee-form__group">
            <label for="first_name" class="employee-form__label">
              First Name <span class="employee-form__required">*</span>
            </label>
            <input
              type="text"
              id="first_name"
              formControlName="first_name"
              class="employee-form__input"
              [class.employee-form__input--invalid]="isFieldInvalid('first_name')"
              placeholder="Enter first name"
            >
            <div class="employee-form__error" *ngIf="isFieldInvalid('first_name')">
              First name is required
            </div>
          </div>

          <div class="employee-form__group">
            <label for="last_name" class="employee-form__label">
              Last Name <span class="employee-form__required">*</span>
            </label>
            <input
              type="text"
              id="last_name"
              formControlName="last_name"
              class="employee-form__input"
              [class.employee-form__input--invalid]="isFieldInvalid('last_name')"
              placeholder="Enter last name"
            >
            <div class="employee-form__error" *ngIf="isFieldInvalid('last_name')">
              Last name is required
            </div>
          </div>
        </div>

        <div class="employee-form__row">
          <div class="employee-form__group">
            <label for="email" class="employee-form__label">
              Email <span class="employee-form__required">*</span>
            </label>
            <input
              type="email"
              id="email"
              formControlName="email"
              class="employee-form__input"
              [class.employee-form__input--invalid]="isFieldInvalid('email')"
              placeholder="Enter email address"
            >
            <div class="employee-form__error" *ngIf="isFieldInvalid('email')">
              <span *ngIf="employeeForm.get('email')?.errors?.['required']">
                Email is required
              </span>
              <span *ngIf="employeeForm.get('email')?.errors?.['email']">
                Please enter a valid email address
              </span>
            </div>
          </div>

          <div class="employee-form__group">
            <label for="phone" class="employee-form__label">
              Phone Number <span class="employee-form__required">*</span>
            </label>
            <input
              type="text"
              id="phone"
              formControlName="phone"
              class="employee-form__input"
              [class.employee-form__input--invalid]="isFieldInvalid('phone')"
              placeholder="Enter 10-digit phone number"
            >
            <div class="employee-form__error" *ngIf="isFieldInvalid('phone')">
              <span *ngIf="employeeForm.get('phone')?.errors?.['required']">
                Phone number is required
              </span>
              <span *ngIf="employeeForm.get('phone')?.errors?.['pattern']">
                Please enter a valid 10-digit phone number
              </span>
            </div>
          </div>
        </div>

        <h3 class="employee-form__section-title">Employment Details</h3>

        <div class="employee-form__row">
          <div class="employee-form__group">
            <label for="position" class="employee-form__label">
              Position <span class="employee-form__required">*</span>
            </label>
            <input
              type="text"
              id="position"
              formControlName="position"
              class="employee-form__input"
              [class.employee-form__input--invalid]="isFieldInvalid('position')"
              placeholder="Enter job position"
            >
            <div class="employee-form__error" *ngIf="isFieldInvalid('position')">
              Position is required
            </div>
          </div>

          <div class="employee-form__group">
            <label for="department_id" class="employee-form__label">
              Department <span class="employee-form__required">*</span>
            </label>
            <app-searchable-dropdown
              id="department_id"
              formControlName="department_id"
              [options]="departments"
              [invalid]="isFieldInvalid('department_id')"
              placeholder="Select Department"
              searchPlaceholder="Search departments..."
            ></app-searchable-dropdown>
            <div class="employee-form__error" *ngIf="isFieldInvalid('department_id')">
              Department is required
            </div>
          </div>
        </div>

        <div class="employee-form__row">
          <div class="employee-form__group">
            <label for="hire_date" class="employee-form__label">
              Hire Date <span class="employee-form__required">*</span>
            </label>
            <div class="employee-form__date-container">
              <input
                type="date"
                id="hire_date"
                formControlName="hire_date"
                class="employee-form__input employee-form__input--date"
                [class.employee-form__input--invalid]="isFieldInvalid('hire_date')"
              >
              <div class="employee-form__calendar-icon" (click)="openDatePicker('hire_date')">
                <i class="fa fa-calendar"></i>
              </div>
            </div>
            <div class="employee-form__error" *ngIf="isFieldInvalid('hire_date')">
              Hire date is required
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>

  <div modal-footer class="employee-form__footer">
    <app-button
      class="employee-form__button"
      label="Cancel"
      variant="secondary"
      (onClick)="closeModal()"
      >
    </app-button>

    <app-button
      class="employee-form__button"
      [label]="isEditMode ? 'Update Employee' : 'Add Employee'"
      variant="primary"
      [disabled]="employeeForm.invalid || isSubmitting"
      [icon]="isSubmitting ? 'fa fa-spinner fa-spin' : ''"
      (onClick)="onSubmit()">
    </app-button>
  </div>
</app-modal>
