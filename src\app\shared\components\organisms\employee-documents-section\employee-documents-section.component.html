<div class="employee-documents-section">
  <!-- Section Header -->
  <div class="section-header">
    <div class="header-content">
      <h3 class="section-title">
        <i class="fas fa-file-alt"></i>
        Employee Documents
      </h3>
      <p class="section-description">Manage and organize employee documents and files</p>
    </div>
    <button
      class="btn btn-primary add-document-btn"
      (click)="openAddModal()"
      [disabled]="isLoading">
      <i class="fas fa-plus"></i>
      Upload Document
    </button>
  </div>

  <!-- Loading State -->
  @if (isLoading && documents.length === 0) {
    <div class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading documents...</p>
      </div>
    </div>
  }

  <!-- Empty State -->
  @if (!isLoading && documents.length === 0) {
    <div class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-file-alt"></i>
      </div>
      <h4>No Documents Found</h4>
      <p>This employee doesn't have any documents uploaded yet.</p>
      <button class="btn btn-primary" (click)="openAddModal()">
        <i class="fas fa-plus"></i>
        Upload First Document
      </button>
    </div>
  }

  <!-- Documents Table -->
  @if (documents.length > 0) {
    <div class="documents-table-container">
      <div class="table-wrapper">
        <table class="documents-table">
          <thead>
            <tr>
              <th>Document Name</th>
              <th>Type</th>
              <th>File Size</th>
              <th>Uploaded On</th>
              <th class="actions-column">Actions</th>
            </tr>
          </thead>
          <tbody>
            @for (document of documents; track document.id) {
              <tr class="document-row">
                <td class="document-name">
                  <div class="document-info">
                    <i class="fas fa-file-alt document-icon"></i>
                    <span class="name">{{ document.document_name }}</span>
                  </div>
                </td>
                <td class="document-type">
                  <span class="type-badge" [class]="'type-' + document.document_type">
                    {{ getDocumentTypeLabel(document.document_type) }}
                  </span>
                </td>
                <td class="file-size">
                  {{ formatFileSize(document.file_size) }}
                </td>
                <td class="upload-date">
                  {{ formatDate(document.uploaded_on || document.created_at) }}
                </td>
                <td class="actions">
                  <div class="action-buttons">
                    <button
                      class="btn-action view"
                      [class.disabled]="!document.file_url"
                      (click)="viewDocument(document)"
                      [title]="document.file_url ? 'View Document' : 'File not available'">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button
                      class="btn-action download"
                      [class.disabled]="!document.file_url"
                      (click)="downloadDocument(document)"
                      [title]="document.file_url ? 'Download Document' : 'File not available'">
                      <i class="fas fa-download"></i>
                    </button>
                    <button
                      class="btn-action edit"
                      (click)="openEditModal(document)"
                      title="Edit Document">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button
                      class="btn-action delete"
                      (click)="deleteDocument(document)"
                      title="Delete Document">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>
  }

  <!-- Modal Overlay -->
  @if (isModalOpen) {
    <div class="modal-overlay" (click)="closeModal()">
      <div class="modal-container" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h4 class="modal-title">
            <i class="fas fa-file-upload"></i>
            {{ isEditing ? 'Edit Document' : 'Upload New Document' }}
          </h4>
          <button class="modal-close" (click)="closeModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <form [formGroup]="documentForm" (ngSubmit)="submitForm()" class="modal-body">
          <!-- Document Name Field -->
          <div class="form-group">
            <label for="document_name" class="form-label">
              Document Name <span class="required">*</span>
            </label>
            <input
              type="text"
              id="document_name"
              formControlName="document_name"
              class="form-input"
              [class.error]="getFieldError('document_name')"
              placeholder="Enter document name">
            @if (getFieldError('document_name')) {
              <span class="error-message">{{ getFieldError('document_name') }}</span>
            }
          </div>

          <!-- Document Type Field -->
          <div class="form-group">
            <label for="document_type" class="form-label">
              Document Type <span class="required">*</span>
            </label>
            <select
              id="document_type"
              formControlName="document_type"
              class="form-select"
              [class.error]="getFieldError('document_type')">
              <option value="">Select document type</option>
              @for (type of documentTypes; track type.value) {
                <option [value]="type.value">{{ type.label }}</option>
              }
            </select>
            @if (getFieldError('document_type')) {
              <span class="error-message">{{ getFieldError('document_type') }}</span>
            }
          </div>

          <!-- Description Field -->
          <div class="form-group">
            <label for="description" class="form-label">
              Description <span class="optional">(Optional)</span>
            </label>
            <textarea
              id="description"
              formControlName="description"
              class="form-control"
              rows="3"
              [class.error]="getFieldError('description')"
              placeholder="Enter document description or notes...">
            </textarea>
            @if (getFieldError('description')) {
              <span class="error-message">{{ getFieldError('description') }}</span>
            }
          </div>

          <!-- File Upload Field -->
          <div class="form-group">
            <label for="file" class="form-label">
              File {{ !isEditing ? '*' : '(Optional)' }}
            </label>
            <div class="file-upload-container">
              <input
                type="file"
                #fileInput
                id="file"
                (change)="onFileSelected($event)"
                class="file-input"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
              <div
                class="file-upload-area"
                [class.has-file]="selectedFile"
                (click)="fileInput.click()"
                (dragover)="onDragOver($event)"
                (dragleave)="onDragLeave($event)"
                (drop)="onDrop($event)">
                @if (selectedFile) {
                  <div class="selected-file">
                    <i class="fas fa-file-alt"></i>
                    <span class="file-name">{{ selectedFile.name }}</span>
                    <span class="file-size">({{ formatFileSize(selectedFile.size) }})</span>
                    <button
                      type="button"
                      class="remove-file-btn"
                      (click)="removeSelectedFile($event)">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                } @else {
                  <div class="upload-placeholder">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <span class="upload-text">Click to select file or drag and drop</span>
                    <span class="upload-hint">PDF, DOC, DOCX, JPG, PNG (Max 10MB)</span>
                  </div>
                }
              </div>
            </div>
            @if (getFieldError('file')) {
              <span class="error-message">{{ getFieldError('file') }}</span>
            }
          </div>

          <!-- Modal Actions -->
          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" (click)="closeModal()">
              Cancel
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="documentForm.invalid || isLoading">
              @if (isLoading) {
                <i class="fas fa-spinner fa-spin"></i>
              } @else {
                <i class="fas {{ isEditing ? 'fa-save' : 'fa-upload' }}"></i>
              }
              {{ isEditing ? 'Update Document' : 'Upload Document' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  }
</div>
