@use 'sass:color';
@import '../../../styles/variables/_colors';

.employees-page {
  min-height: 100vh;
  background: $background-light;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding: 2rem;

  &__header {
    margin-bottom: 2rem;
  }

  &__title {
    font-size: 2rem;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin: 0 0 0.5rem 0;
  }

  &__subtitle {
    font-size: 1.1rem;
    color: $text-secondary;
    margin: 0;
  }

  &__content {
    background: $white;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
}

// Responsive design
@media (max-width: 768px) {
  .employees-page {
    padding: 1rem;

    &__header {
      .view-toggle {
        width: 100%;

        .toggle-btn {
          flex: 1;
          justify-content: center;
          padding: 0.625rem 1rem;

          span:not(.material-icons) {
            display: none;
          }
        }
      }
    }

    &__title {
      font-size: 1.5rem;
    }

    &__subtitle {
      font-size: 1rem;
    }
  }
}
