import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'app-date-range-picker',
  standalone: true,
  imports: [CommonModule, IconComponent],
  template: `
    <div class="date-range-picker">
      <button class="date-range-toggle" (click)="togglePicker()">
        <span>{{ displayValue }}</span>
        <app-icon name="fa fa-calendar" size="sm" class="calendar-icon"></app-icon>
      </button>
    </div>
  `,
  styles: [`
    .date-range-picker {
      display: inline-block;
    }
    
    .date-range-toggle {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: #fff;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      font-size: 14px;
      color: #495057;
      cursor: pointer;
    }
    
    .date-range-toggle:hover {
      border-color: #ced4da;
    }
    
    .calendar-icon {
      margin-left: 8px;
    }
  `]
})
export class DateRangePickerComponent {
  @Input() startDate: Date | null = null;
  @Input() endDate: Date | null = null;
  @Input() format: string = 'MM/dd/yyyy';
  
  @Output() dateRangeChange = new EventEmitter<{start: Date, end: Date}>();
  
  get displayValue(): string {
    if (this.startDate && this.endDate) {
      return `${this.formatDate(this.startDate)} - ${this.formatDate(this.endDate)}`;
    }
    return 'Select date range';
  }
  
  togglePicker(): void {
    // In a real implementation, this would open a date picker
    // For this example, we'll just set some default dates
    const today = new Date();
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(today.getDate() - 7);
    
    this.startDate = oneWeekAgo;
    this.endDate = today;
    this.dateRangeChange.emit({start: oneWeekAgo, end: today});
  }
  
  private formatDate(date: Date): string {
    // Simple date formatter - in a real app, use a proper date library
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${month}/${day}/${year}`;
  }
}
