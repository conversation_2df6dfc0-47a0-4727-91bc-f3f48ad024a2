@import '../../../../../styles/variables/colors';

// Block: header
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px;
  background-color: $header-bg;
  border-bottom: 1px solid $border-color;

  // Element: left section
  &__left {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  // Element: actions
  &__actions {
    display: flex;
    align-items: center;
  }
}

// Block: menu
.menu {
  // Element: toggle
  &__toggle {
    background: $gray-200;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: $gray-600;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 18px;

    &:hover {
      background-color: rgba($sidebar-hover-bg, 0.1);
      color: $gray-800;
    }
  }
}

// Block: home
.home {
  // Element: link
  &__link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: $primary;
    font-weight: $font-weight-medium;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.2s ease;

    i {
      margin-right: 8px;
    }

    &:hover {
      background-color: rgba($primary, 0.1);
    }
  }
}

// Block: search
.search {
  // Element: bar
  &__bar {
    display: flex;
    align-items: center;
    width: 300px;
    height: 36px;
    background-color: $gray-100;
    border-radius: 4px;
    overflow: hidden;
  }

  // Element: input
  &__input {
    flex: 1;
    height: 100%;
    padding: 0 10px;
    border: none;
    background: transparent;
    font-size: $font-size-sm;

    &:focus {
      outline: none;
    }
  }

  // Element: button
  &__button {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    color: $secondary;

    &:hover {
      color: $dark;
    }
  }
}

// Block: action
.action {
  // Element: button
  &__button {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    color: $secondary;
    margin-left: 10px;
    position: relative;

    &:hover {
      color: $dark;
    }

    // Modifier: notification
    &--notification {
      position: relative;
    }
  }
}

// Block: notification
.notification {
  // Element: badge
  &__badge {
    position: absolute;
    top: 0;
    right: 0;
    width: 18px;
    height: 18px;
    background-color: $notification-bg;
    color: $white;
    font-size: $font-size-xs;
    font-weight: $font-weight-semibold;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }
}

// Block: user
.user {
  // Element: profile
  &__profile {
    margin-left: 15px;
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
    padding: 5px;
    border-radius: 4px;

    &:hover {
      background-color: rgba($gray-200, 0.5);
    }
  }

  // Element: avatar
  &__avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
  }

  // Element: info
  &__info {
    margin-left: 10px;
    display: flex;
    flex-direction: column;
  }

  // Element: name
  &__name {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $gray-800;
  }

  // Element: role
  &__role {
    font-size: $font-size-xs;
    color: $primary;
  }

  // Element: menu
  &__menu {
    position: absolute;
    top: calc(100% + 5px);
    right: 0;
    width: 240px;
    background-color: $white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba($black, 0.1);
    z-index: 1000;
    overflow: hidden;
  }

  // Element: menu header
  &__menu-header {
    padding: 15px;
    background-color: rgba($primary, 0.05);
    border-bottom: 1px solid $gray-200;
    display: flex;
    flex-direction: column;
  }

  // Element: menu name
  &__menu-name {
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin-bottom: 5px;
  }

  // Element: menu email
  &__menu-email {
    font-size: $font-size-xs;
    color: $gray-600;
  }

  // Element: menu list
  &__menu-list {
    list-style: none;
    padding: 10px 0;
    margin: 0;
  }

  // Element: menu item
  &__menu-item {
    padding: 0;
    margin: 0;
  }

  // Element: menu link
  &__menu-link {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    color: $gray-700;
    text-decoration: none;
    font-size: $font-size-sm;
    transition: all 0.2s ease;

    i {
      margin-right: 10px;
      width: 16px;
      text-align: center;
    }

    &:hover {
      background-color: rgba($gray-100, 0.7);
      color: $primary;
    }
  }

  // Element: menu button
  &__menu-button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 10px 15px;
    color: $danger;
    background: none;
    border: none;
    text-align: left;
    font-size: $font-size-sm;
    cursor: pointer;
    transition: all 0.2s ease;

    i {
      margin-right: 10px;
      width: 16px;
      text-align: center;
    }

    &:hover {
      background-color: rgba($danger, 0.05);
    }
  }

  // Element: menu divider
  &__menu-divider {
    height: 1px;
    background-color: $gray-200;
    margin: 5px 0;
  }
}



