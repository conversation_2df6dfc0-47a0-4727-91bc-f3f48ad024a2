import { Injectable, signal, computed, effect, inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { 
  ThemeMode, 
  ThemeConfig, 
  ThemeColors, 
  LIGHT_THEME_COLORS, 
  DARK_THEME_COLORS,
  DEFAULT_THEME_CONFIG 
} from '../models/theme.interface';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly document = inject(DOCUMENT);
  private readonly STORAGE_KEY = 'app-theme-config';

  // Theme state signals
  private readonly _themeConfig = signal<ThemeConfig>(this.loadThemeConfig());
  private readonly _systemTheme = signal<'light' | 'dark'>('light');

  // Public readonly signals
  public readonly themeConfig = this._themeConfig.asReadonly();
  
  // Computed theme mode based on config and system preference
  public readonly currentTheme = computed<'light' | 'dark'>(() => {
    const config = this._themeConfig();
    if (config.mode === 'auto') {
      return this._systemTheme();
    }
    return config.mode as 'light' | 'dark';
  });

  // Computed theme colors based on current theme
  public readonly themeColors = computed<ThemeColors>(() => {
    const theme = this.currentTheme();
    return theme === 'dark' ? DARK_THEME_COLORS : LIGHT_THEME_COLORS;
  });

  // Computed CSS custom properties
  public readonly cssCustomProperties = computed<Record<string, string>>(() => {
    const colors = this.themeColors();
    const properties: Record<string, string> = {};
    
    // Convert theme colors to CSS custom properties
    Object.entries(colors).forEach(([key, value]) => {
      const cssVar = `--theme-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
      properties[cssVar] = value;
    });
    
    return properties;
  });

  constructor() {
    // Initialize system theme detection
    this.detectSystemTheme();
    
    // Listen for system theme changes
    this.setupSystemThemeListener();
    
    // Apply theme on initialization and changes
    effect(() => {
      this.applyTheme();
    });
  }

  /**
   * Set theme mode
   */
  setThemeMode(mode: ThemeMode): void {
    const newConfig = { ...this._themeConfig(), mode };
    this._themeConfig.set(newConfig);
    this.saveThemeConfig(newConfig);
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme(): void {
    const currentMode = this._themeConfig().mode;
    let newMode: ThemeMode;
    
    if (currentMode === 'auto') {
      // If auto, switch to opposite of current system theme
      newMode = this._systemTheme() === 'light' ? 'dark' : 'light';
    } else {
      // Toggle between light and dark
      newMode = currentMode === 'light' ? 'dark' : 'light';
    }
    
    this.setThemeMode(newMode);
  }

  /**
   * Set custom theme colors
   */
  setCustomColors(colors: Partial<ThemeColors>): void {
    const newConfig = { 
      ...this._themeConfig(), 
      customColors: { ...this._themeConfig().customColors, ...colors }
    };
    this._themeConfig.set(newConfig);
    this.saveThemeConfig(newConfig);
  }

  /**
   * Reset theme to default
   */
  resetTheme(): void {
    this._themeConfig.set(DEFAULT_THEME_CONFIG);
    this.saveThemeConfig(DEFAULT_THEME_CONFIG);
  }

  /**
   * Check if current theme is dark
   */
  isDarkTheme(): boolean {
    return this.currentTheme() === 'dark';
  }

  /**
   * Check if current theme is light
   */
  isLightTheme(): boolean {
    return this.currentTheme() === 'light';
  }

  /**
   * Get theme mode display name
   */
  getThemeModeDisplayName(mode: ThemeMode): string {
    switch (mode) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'auto':
        return 'System';
      default:
        return 'Unknown';
    }
  }

  /**
   * Apply theme to document
   */
  private applyTheme(): void {
    const theme = this.currentTheme();
    const colors = this.themeColors();
    const customProperties = this.cssCustomProperties();
    
    // Remove existing theme classes
    this.document.documentElement.classList.remove('theme-light', 'theme-dark');
    
    // Add current theme class
    this.document.documentElement.classList.add(`theme-${theme}`);
    
    // Apply CSS custom properties
    Object.entries(customProperties).forEach(([property, value]) => {
      this.document.documentElement.style.setProperty(property, value);
    });
    
    // Set data attribute for theme
    this.document.documentElement.setAttribute('data-theme', theme);
  }

  /**
   * Detect system theme preference
   */
  private detectSystemTheme(): void {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      this._systemTheme.set(isDark ? 'dark' : 'light');
    }
  }

  /**
   * Setup system theme change listener
   */
  private setupSystemThemeListener(): void {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e: MediaQueryListEvent) => {
        this._systemTheme.set(e.matches ? 'dark' : 'light');
      };
      
      // Modern browsers
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleChange);
      } else {
        // Fallback for older browsers
        mediaQuery.addListener(handleChange);
      }
    }
  }

  /**
   * Load theme configuration from localStorage
   */
  private loadThemeConfig(): ThemeConfig {
    if (typeof window === 'undefined') {
      return DEFAULT_THEME_CONFIG;
    }
    
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...DEFAULT_THEME_CONFIG, ...parsed };
      }
    } catch (error) {
      console.warn('Failed to load theme config from localStorage:', error);
    }
    
    return DEFAULT_THEME_CONFIG;
  }

  /**
   * Save theme configuration to localStorage
   */
  private saveThemeConfig(config: ThemeConfig): void {
    if (typeof window === 'undefined') {
      return;
    }
    
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(config));
    } catch (error) {
      console.warn('Failed to save theme config to localStorage:', error);
    }
  }
}
