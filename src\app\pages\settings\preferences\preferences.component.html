<!-- Page Header -->
<div class="page-header">
  <div class="page-header__content">
    <div class="page-header__title">
      <h1>Preferences</h1>
      <p>Customize your application appearance and layout settings</p>
    </div>
    <div class="page-header__actions">
      <button 
        type="button" 
        class="btn btn-outline-secondary"
        (click)="resetToDefaults()">
        <i class="fas fa-undo"></i>
        Reset to Defaults
      </button>
    </div>
  </div>
</div>

<!-- Preferences Content -->
<div class="preferences-container">
  <form [formGroup]="preferencesForm" class="preferences-form">
    
    <!-- Theme Settings Section -->
    <div class="preferences-section">
      <div class="preferences-section__header">
        <h3 class="preferences-section__title">
          <i class="fas fa-palette"></i>
          Theme Settings
        </h3>
        <p class="preferences-section__description">
          Choose your preferred color scheme and visual appearance
        </p>
      </div>
      
      <div class="preferences-section__content">
        <div class="preference-group">
          <label class="preference-group__label">Color Scheme</label>
          <div class="preference-options">
            <div 
              *ngFor="let option of themeOptions"
              class="preference-option"
              [class.preference-option--active]="isThemeModeSelected(option.value as any)"
              (click)="preferencesForm.patchValue({themeMode: option.value})">
              <div class="preference-option__icon">
                <i [class]="option.icon"></i>
              </div>
              <div class="preference-option__content">
                <h4 class="preference-option__title">{{ option.label }}</h4>
                <p class="preference-option__description">{{ option.description }}</p>
              </div>
              <div class="preference-option__check">
                <i 
                  *ngIf="isThemeModeSelected(option.value as any)"
                  class="fas fa-check"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Layout Settings Section -->
    <div class="preferences-section">
      <div class="preferences-section__header">
        <h3 class="preferences-section__title">
          <i class="fas fa-layout"></i>
          Layout Settings
        </h3>
        <p class="preferences-section__description">
          Configure how the application interface is organized
        </p>
      </div>
      
      <div class="preferences-section__content">
        <!-- Header Position -->
        <div class="preference-group">
          <label class="preference-group__label">Header Position</label>
          <div class="preference-options">
            <div 
              *ngFor="let option of headerPositionOptions"
              class="preference-option"
              [class.preference-option--active]="isHeaderPositionSelected(option.value as any)"
              (click)="preferencesForm.patchValue({headerPosition: option.value})">
              <div class="preference-option__icon">
                <i [class]="option.icon"></i>
              </div>
              <div class="preference-option__content">
                <h4 class="preference-option__title">{{ option.label }}</h4>
                <p class="preference-option__description">{{ option.description }}</p>
              </div>
              <div class="preference-option__check">
                <i 
                  *ngIf="isHeaderPositionSelected(option.value as any)"
                  class="fas fa-check"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Layout Type -->
        <div class="preference-group">
          <label class="preference-group__label">Layout Type</label>
          <div class="preference-options">
            <div 
              *ngFor="let option of layoutTypeOptions"
              class="preference-option"
              [class.preference-option--active]="isLayoutTypeSelected(option.value as any)"
              (click)="preferencesForm.patchValue({layoutType: option.value})">
              <div class="preference-option__icon">
                <i [class]="option.icon"></i>
              </div>
              <div class="preference-option__content">
                <h4 class="preference-option__title">{{ option.label }}</h4>
                <p class="preference-option__description">{{ option.description }}</p>
              </div>
              <div class="preference-option__check">
                <i 
                  *ngIf="isLayoutTypeSelected(option.value as any)"
                  class="fas fa-check"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar Position -->
        <div class="preference-group">
          <label class="preference-group__label">Sidebar Position</label>
          <div class="preference-options">
            <div 
              *ngFor="let option of sidebarPositionOptions"
              class="preference-option"
              [class.preference-option--active]="isSidebarPositionSelected(option.value as any)"
              (click)="preferencesForm.patchValue({sidebarPosition: option.value})">
              <div class="preference-option__icon">
                <i [class]="option.icon"></i>
              </div>
              <div class="preference-option__content">
                <h4 class="preference-option__title">{{ option.label }}</h4>
                <p class="preference-option__description">{{ option.description }}</p>
              </div>
              <div class="preference-option__check">
                <i 
                  *ngIf="isSidebarPositionSelected(option.value as any)"
                  class="fas fa-check"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar Collapsed -->
        <div class="preference-group">
          <label class="preference-group__label">Sidebar State</label>
          <div class="preference-toggle">
            <label class="toggle-switch">
              <input 
                type="checkbox" 
                formControlName="sidebarCollapsed"
                class="toggle-switch__input">
              <span class="toggle-switch__slider"></span>
              <span class="toggle-switch__label">Collapse sidebar by default</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview Section -->
    <div class="preferences-section">
      <div class="preferences-section__header">
        <h3 class="preferences-section__title">
          <i class="fas fa-eye"></i>
          Live Preview
        </h3>
        <p class="preferences-section__description">
          See how your settings affect the application appearance
        </p>
      </div>

      <div class="preferences-section__content">
        <div class="layout-preview">
          <div class="layout-preview__container"
               [attr.data-theme]="currentTheme()"
               [attr.data-header-position]="layoutConfig().headerPosition"
               [attr.data-layout-type]="layoutConfig().layoutType">
            <div class="layout-preview__header">Header</div>
            <div class="layout-preview__sidebar"
                 [class.layout-preview__sidebar--collapsed]="layoutConfig().sidebarCollapsed"
                 [class.layout-preview__sidebar--right]="layoutConfig().sidebarPosition === 'right'">
              Sidebar
            </div>
            <div class="layout-preview__content">Main Content</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Current Settings Summary -->
    <div class="preferences-section">
      <div class="preferences-section__header">
        <h3 class="preferences-section__title">
          <i class="fas fa-info-circle"></i>
          Current Settings
        </h3>
        <p class="preferences-section__description">
          Summary of your current preferences
        </p>
      </div>

      <div class="preferences-section__content">
        <div class="settings-summary">
          <div class="settings-summary__item">
            <span class="settings-summary__label">Theme:</span>
            <span class="settings-summary__value">{{ getCurrentThemeDisplayName() }}</span>
          </div>
          <div class="settings-summary__item">
            <span class="settings-summary__label">Header Position:</span>
            <span class="settings-summary__value">{{ getCurrentHeaderPositionDisplayName() }}</span>
          </div>
          <div class="settings-summary__item">
            <span class="settings-summary__label">Layout Type:</span>
            <span class="settings-summary__value">{{ layoutConfig().layoutType | titlecase }}</span>
          </div>
          <div class="settings-summary__item">
            <span class="settings-summary__label">Sidebar Position:</span>
            <span class="settings-summary__value">{{ layoutConfig().sidebarPosition | titlecase }}</span>
          </div>
          <div class="settings-summary__item">
            <span class="settings-summary__label">Sidebar Collapsed:</span>
            <span class="settings-summary__value">{{ layoutConfig().sidebarCollapsed ? 'Yes' : 'No' }}</span>
          </div>
        </div>
      </div>
    </div>

  </form>
</div>
