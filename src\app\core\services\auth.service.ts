import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { API_ENDPOINTS } from '../constants/api.constants';
import { isPlatformBrowser } from '@angular/common';
import {
  AuthTokens,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  RegistrationRequest,
  RegistrationResponse,
  User,
  VerifyTokenRequest,
  VerifyTokenResponse
} from '../models/user.interface';
import { JwtService } from './jwt.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly ACCESS_TOKEN_KEY = 'access_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_KEY = 'user';

  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasValidToken());
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(
    private http: HttpClient,
    private jwtService: JwtService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    if (this.isBrowser()) {
      this.loadUserFromStorage();
    }
  }

  /**
   * Check if code is running in browser environment
   */
  private isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  /**
   * Register a new user
   */
  register(userData: RegistrationRequest): Observable<RegistrationResponse> {
    return this.http.post<RegistrationResponse>(
      `${environment.apiUrl}${API_ENDPOINTS.AUTH.REGISTER}`,
      userData
    ).pipe(
      catchError(error => {
        console.error('Registration error:', error);
        return throwError(() => new Error(error.error?.message || 'Registration failed'));
      })
    );
  }

  /**
   * Login user and store tokens
   */
  login(credentials: LoginRequest): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(
      `${environment.apiUrl}${API_ENDPOINTS.AUTH.LOGIN}`,
      credentials
    ).pipe(
      tap(response => {
        this.storeTokens(response);

        // If user data is included in the response, store it
        if (response.user) {
          this.storeUser(response.user);
        }
        // We don't fetch the user profile here anymore to avoid unnecessary API calls
        // The profile will be fetched only when needed (e.g., when visiting the profile page)

        this.isAuthenticatedSubject.next(true);
      }),
      catchError(error => {
        console.error('Login error:', error);
        return throwError(() => new Error(error.error?.message || 'Login failed'));
      })
    );
  }

  /**
   * Get user profile
   */
  getUserProfile(): Observable<User> {
    return this.http.get<User>(
      `${environment.apiUrl}${API_ENDPOINTS.AUTH.USER_PROFILE}`
    ).pipe(
      tap(user => {
        this.storeUser(user);
        this.currentUserSubject.next(user);
        this.isAuthenticatedSubject.next(true);
      }),
      catchError(error => {
        console.error('Get user profile error:', error);
        return throwError(() => new Error(error.error?.message || 'Failed to get user profile'));
      })
    );
  }

  /**
   * Refresh access token using refresh token
   */
  refreshToken(): Observable<string> {
    const refreshToken = this.getRefreshToken();

    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    const refreshRequest: RefreshTokenRequest = {
      refresh_token: refreshToken
    };

    return this.http.post<RefreshTokenResponse>(
      `${environment.apiUrl}${API_ENDPOINTS.AUTH.REFRESH_TOKEN}`,
      refreshRequest
    ).pipe(
      map(response => {
        if (this.isBrowser()) {
          localStorage.setItem(this.ACCESS_TOKEN_KEY, response.access_token);

          // If a new refresh token is provided, update it
          if (response.refresh_token) {
            localStorage.setItem(this.REFRESH_TOKEN_KEY, response.refresh_token);
          }

          // Store token expiry time if available
          if (response.expires_in) {
            const expiryTime = Date.now() + response.expires_in * 1000;
            localStorage.setItem('token_expiry', expiryTime.toString());
          }
        }
        return response.access_token;
      }),
      catchError(error => {
        console.error('Token refresh error:', error);
        this.logout();
        return throwError(() => new Error('Session expired. Please login again.'));
      })
    );
  }

  /**
   * Verify token validity
   */
  verifyToken(token: string): Observable<boolean> {
    const verifyRequest: VerifyTokenRequest = {
      access_token: token
    };

    return this.http.post<VerifyTokenResponse>(
      `${environment.apiUrl}${API_ENDPOINTS.AUTH.VERIFY_TOKEN}`,
      verifyRequest
    ).pipe(
      map(response => response.is_valid),
      catchError(() => of(false))
    );
  }

  /**
   * Check if current token is valid by decoding it and checking expiry
   * Only refreshes the token if it's expired and we have a refresh token
   */
  checkTokenValidity(): Observable<boolean> {
    const token = this.getAccessToken();

    if (!token) {
      this.isAuthenticatedSubject.next(false);
      return of(false);
    }

    // Check if token is expired
    if (this.jwtService.isTokenExpired(token)) {
      // Only try to refresh if we have a refresh token
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        this.isAuthenticatedSubject.next(false);
        return of(false);
      }

      // Try to refresh the token
      return this.refreshToken().pipe(
        map(newToken => {
          const isValid = !!newToken;
          this.isAuthenticatedSubject.next(isValid);
          return isValid;
        }),
        catchError(() => {
          this.isAuthenticatedSubject.next(false);
          return of(false);
        })
      );
    }

    // Token is valid
    this.isAuthenticatedSubject.next(true);
    return of(true);
  }

  /**
   * Logout user and clear storage
   */
  logout(): void {
    if (this.isBrowser()) {
      localStorage.removeItem(this.ACCESS_TOKEN_KEY);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
      localStorage.removeItem(this.USER_KEY);
    }
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
  }

  /**
   * Get current user
   */
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Get access token
   */
  getAccessToken(): string | null {
    return this.isBrowser() ? localStorage.getItem(this.ACCESS_TOKEN_KEY) : null;
  }

  /**
   * Get refresh token
   */
  getRefreshToken(): string | null {
    return this.isBrowser() ? localStorage.getItem(this.REFRESH_TOKEN_KEY) : null;
  }

  /**
   * Check if user has a valid token
   */
  hasValidToken(): boolean {
    const token = this.getAccessToken();
    if (!token) {
      return false;
    }

    // If token is expired but we have a refresh token, consider it potentially valid
    // The actual refresh will happen in checkTokenValidity()
    if (this.jwtService.isTokenExpired(token)) {
      return !!this.getRefreshToken();
    }

    return true;
  }

  /**
   * Store tokens in local storage
   */
  public storeTokens(tokens: AuthTokens): void {
    if (this.isBrowser()) {
      localStorage.setItem(this.ACCESS_TOKEN_KEY, tokens.access);
      localStorage.setItem(this.REFRESH_TOKEN_KEY, tokens.refresh);

      // Store token expiry time if available
      if ('expires_in' in tokens && tokens.expires_in) {
        const expiryTime = Date.now() + tokens.expires_in * 1000;
        localStorage.setItem('token_expiry', expiryTime.toString());
      }
    }
  }

  /**
   * Store user in local storage
   */
  public storeUser(user: User): void {
    if (this.isBrowser()) {
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
    // Always update the BehaviorSubject
    this.currentUserSubject.next(user);
  }

  /**
   * Load user from local storage
   */
  private loadUserFromStorage(): void {
    if (!this.isBrowser()) {
      return;
    }

    const userJson = localStorage.getItem(this.USER_KEY);
    if (userJson) {
      try {
        const user = JSON.parse(userJson);
        this.currentUserSubject.next(user);
      } catch (e) {
        console.error('Error parsing user from storage', e);
        localStorage.removeItem(this.USER_KEY);
      }
    }
  }
}
