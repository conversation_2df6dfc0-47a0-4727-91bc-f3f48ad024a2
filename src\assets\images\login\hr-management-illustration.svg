<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>HR Management Illustration</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FF6B35" offset="0%"></stop>
            <stop stop-color="#E85A2A" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#343A40" offset="0%"></stop>
            <stop stop-color="#212529" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#F8F9FA" offset="0%"></stop>
            <stop stop-color="#E9ECEF" offset="100%"></stop>
        </linearGradient>
        <filter x="-15.0%" y="-15.0%" width="130.0%" height="130.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="15" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="HR-Management-Illustration" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background Elements -->
        <circle id="Glow-1" fill="#FF6B35" opacity="0.2" filter="url(#filter-4)" cx="400" cy="300" r="200"></circle>
        <circle id="Glow-2" fill="#343A40" opacity="0.1" filter="url(#filter-4)" cx="200" cy="200" r="100"></circle>
        <circle id="Glow-3" fill="#343A40" opacity="0.1" filter="url(#filter-4)" cx="600" cy="400" r="100"></circle>
        
        <!-- Dashboard UI -->
        <g id="Dashboard" transform="translate(200, 150)">
            <rect id="Dashboard-Background" fill="#FFFFFF" x="0" y="0" width="400" height="300" rx="10"></rect>
            <rect id="Dashboard-Header" fill="url(#linearGradient-2)" x="0" y="0" width="400" height="50" rx="10 10 0 0"></rect>
            <text id="Dashboard-Title" font-family="Arial-BoldMT, Arial" font-size="16" font-weight="bold" fill="#FFFFFF">
                <tspan x="20" y="30">HR Management Dashboard</tspan>
            </text>
            
            <!-- Sidebar -->
            <rect id="Sidebar" fill="#F8F9FA" x="0" y="50" width="80" height="250"></rect>
            <rect id="Sidebar-Item-1" fill="url(#linearGradient-1)" x="0" y="70" width="80" height="40"></rect>
            <rect id="Sidebar-Item-2" fill="#E9ECEF" x="0" y="110" width="80" height="40"></rect>
            <rect id="Sidebar-Item-3" fill="#E9ECEF" x="0" y="150" width="80" height="40"></rect>
            <rect id="Sidebar-Item-4" fill="#E9ECEF" x="0" y="190" width="80" height="40"></rect>
            <rect id="Sidebar-Item-5" fill="#E9ECEF" x="0" y="230" width="80" height="40"></rect>
            
            <!-- Main Content -->
            <g id="Content" transform="translate(100, 70)">
                <!-- Stats Cards -->
                <g id="Stats-Row" transform="translate(0, 0)">
                    <rect id="Stat-Card-1" fill="#F8F9FA" x="0" y="0" width="80" height="80" rx="5"></rect>
                    <rect id="Stat-Card-2" fill="#F8F9FA" x="90" y="0" width="80" height="80" rx="5"></rect>
                    <rect id="Stat-Card-3" fill="#F8F9FA" x="180" y="0" width="80" height="80" rx="5"></rect>
                    
                    <text id="Stat-1-Number" font-family="Arial-BoldMT, Arial" font-size="24" font-weight="bold" fill="#FF6B35">
                        <tspan x="30" y="40">42</tspan>
                    </text>
                    <text id="Stat-1-Label" font-family="Arial, Arial" font-size="12" fill="#6C757D">
                        <tspan x="15" y="60">Employees</tspan>
                    </text>
                    
                    <text id="Stat-2-Number" font-family="Arial-BoldMT, Arial" font-size="24" font-weight="bold" fill="#FF6B35">
                        <tspan x="120" y="40">8</tspan>
                    </text>
                    <text id="Stat-2-Label" font-family="Arial, Arial" font-size="12" fill="#6C757D">
                        <tspan x="105" y="60">Departments</tspan>
                    </text>
                    
                    <text id="Stat-3-Number" font-family="Arial-BoldMT, Arial" font-size="24" font-weight="bold" fill="#FF6B35">
                        <tspan x="210" y="40">12</tspan>
                    </text>
                    <text id="Stat-3-Label" font-family="Arial, Arial" font-size="12" fill="#6C757D">
                        <tspan x="205" y="60">Positions</tspan>
                    </text>
                </g>
                
                <!-- Chart -->
                <g id="Chart" transform="translate(0, 100)">
                    <rect id="Chart-Background" fill="#F8F9FA" x="0" y="0" width="260" height="120" rx="5"></rect>
                    <text id="Chart-Title" font-family="Arial-BoldMT, Arial" font-size="14" font-weight="bold" fill="#343A40">
                        <tspan x="10" y="20">Employee Performance</tspan>
                    </text>
                    
                    <!-- Chart Bars -->
                    <rect id="Chart-Bar-1" fill="url(#linearGradient-1)" x="40" y="40" width="20" height="60" rx="2"></rect>
                    <rect id="Chart-Bar-2" fill="url(#linearGradient-1)" x="70" y="50" width="20" height="50" rx="2"></rect>
                    <rect id="Chart-Bar-3" fill="url(#linearGradient-1)" x="100" y="30" width="20" height="70" rx="2"></rect>
                    <rect id="Chart-Bar-4" fill="url(#linearGradient-1)" x="130" y="60" width="20" height="40" rx="2"></rect>
                    <rect id="Chart-Bar-5" fill="url(#linearGradient-1)" x="160" y="45" width="20" height="55" rx="2"></rect>
                    <rect id="Chart-Bar-6" fill="url(#linearGradient-1)" x="190" y="35" width="20" height="65" rx="2"></rect>
                    <rect id="Chart-Bar-7" fill="url(#linearGradient-1)" x="220" y="55" width="20" height="45" rx="2"></rect>
                    
                    <!-- X-Axis -->
                    <line x1="30" y1="110" x2="240" y2="110" id="X-Axis" stroke="#ADB5BD" stroke-width="1"></line>
                    
                    <!-- Y-Axis -->
                    <line x1="30" y1="30" x2="30" y2="110" id="Y-Axis" stroke="#ADB5BD" stroke-width="1"></line>
                </g>
            </g>
        </g>
        
        <!-- People Elements -->
        <g id="People" transform="translate(100, 250)">
            <!-- Person 1 (Manager) -->
            <g id="Person-1" transform="translate(0, 0)">
                <circle id="Person-1-Head" fill="url(#linearGradient-2)" cx="30" cy="30" r="30"></circle>
                <rect id="Person-1-Body" fill="url(#linearGradient-2)" x="15" y="60" width="30" height="60" rx="15"></rect>
                <rect id="Person-1-Arm-Left" fill="url(#linearGradient-2)" x="-10" y="70" width="30" height="10" rx="5" transform="translate(5, 75) rotate(-30) translate(-5, -75)"></rect>
                <rect id="Person-1-Arm-Right" fill="url(#linearGradient-2)" x="40" y="70" width="30" height="10" rx="5" transform="translate(55, 75) rotate(30) translate(-55, -75)"></rect>
                <rect id="Person-1-Leg-Left" fill="url(#linearGradient-2)" x="15" y="120" width="10" height="40" rx="5"></rect>
                <rect id="Person-1-Leg-Right" fill="url(#linearGradient-2)" x="35" y="120" width="10" height="40" rx="5"></rect>
            </g>
            
            <!-- Person 2 (Employee) -->
            <g id="Person-2" transform="translate(600, 50)">
                <circle id="Person-2-Head" fill="url(#linearGradient-1)" cx="30" cy="30" r="30"></circle>
                <rect id="Person-2-Body" fill="url(#linearGradient-1)" x="15" y="60" width="30" height="60" rx="15"></rect>
                <rect id="Person-2-Arm-Left" fill="url(#linearGradient-1)" x="-10" y="70" width="30" height="10" rx="5" transform="translate(5, 75) rotate(-30) translate(-5, -75)"></rect>
                <rect id="Person-2-Arm-Right" fill="url(#linearGradient-1)" x="40" y="70" width="30" height="10" rx="5" transform="translate(55, 75) rotate(30) translate(-55, -75)"></rect>
                <rect id="Person-2-Leg-Left" fill="url(#linearGradient-1)" x="15" y="120" width="10" height="40" rx="5"></rect>
                <rect id="Person-2-Leg-Right" fill="url(#linearGradient-1)" x="35" y="120" width="10" height="40" rx="5"></rect>
            </g>
        </g>
        
        <!-- HR Elements -->
        <g id="HR-Elements" transform="translate(50, 400)">
            <!-- Document 1 -->
            <g id="Document-1" transform="translate(0, 0)">
                <rect id="Doc-1-Background" fill="#FFFFFF" stroke="#ADB5BD" stroke-width="2" x="0" y="0" width="70" height="90" rx="5"></rect>
                <line x1="15" y1="20" x2="55" y2="20" id="Doc-1-Line-1" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
                <line x1="15" y1="35" x2="55" y2="35" id="Doc-1-Line-2" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
                <line x1="15" y1="50" x2="55" y2="50" id="Doc-1-Line-3" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
                <line x1="15" y1="65" x2="40" y2="65" id="Doc-1-Line-4" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
            </g>
            
            <!-- Document 2 -->
            <g id="Document-2" transform="translate(680, 20)">
                <rect id="Doc-2-Background" fill="#FFFFFF" stroke="#ADB5BD" stroke-width="2" x="0" y="0" width="70" height="90" rx="5"></rect>
                <line x1="15" y1="20" x2="55" y2="20" id="Doc-2-Line-1" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
                <line x1="15" y1="35" x2="55" y2="35" id="Doc-2-Line-2" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
                <line x1="15" y1="50" x2="55" y2="50" id="Doc-2-Line-3" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
                <line x1="15" y1="65" x2="40" y2="65" id="Doc-2-Line-4" stroke="#ADB5BD" stroke-width="2" stroke-linecap="round"></line>
            </g>
        </g>
        
        <!-- HR Shell Text -->
        <text id="HR-Shell" font-family="Arial-BoldMT, Arial" font-size="36" font-weight="bold" fill="#FF6B35">
            <tspan x="325" y="530">HR Shell</tspan>
        </text>
    </g>
</svg>
